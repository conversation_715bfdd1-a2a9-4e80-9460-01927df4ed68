# Sailing Serai Development Rules

## Project Documentation References

1. **Requirements**: [requirements.md](./Sailing_Serai/requirements.md)
2. **Design**: [design.md](./Sailing_Serai/design.md)
3. **Tasks**: [tasks.md](./Sailing_Serai/tasks.md)

## Development Guidelines

### 1. Technology Stack Compliance
- DO NOT use Mock data for authentication, booking, and payment processing
- USE MOCK DATA only to beautify the UI and provide a better user experience
- Always use the specified tech stack from `requirements.md`
- Keep all dependencies up-to-date using `npm outdated` and update regularly
- Use shadcn/ui components as the primary UI library
- Follow React 18+ best practices with TypeScript
- Always investigate Code as there is a reason it is there, instead of commenting the code out to fix an error, which is the easy way out
### 2. Directory Structure
```
src/
├── components/     # Reusable UI components
│   ├── ui/        # shadcn/ui components
│   └── layout/    # Layout components
├── pages/         # Page components
├── hooks/         # Custom React hooks
├── lib/           # Utility functions and config
├── services/      # API and service integrations
├── styles/        # Global styles and themes
├── types/         # TypeScript type definitions
└── i18n/          # Internationalization files
```

### 3. Code Quality
- Use ESLint and Prettier for code formatting
- Follow TypeScript strict mode
- Write unit tests with Vitest and React Testing Library
- Maintain 80%+ test coverage
- Use descriptive variable and function names
- Add JSDoc comments for complex logic

### 4. MCP Tools Usage
- **Context7**: Use for up-to-date documentation and code examples
  - Before implementing complex features, check for existing solutions
  - Use `mcp0_resolve-library-id` and `mcp0_get-library-docs` for library documentation
- **GitHub**: Use for version control and project management
  - Create feature branches from `main`
  - Use conventional commits
  - Open pull requests for code review
- **Playwright**: Use for end-to-end testing
  - Write tests for critical user journeys
  - Run tests in CI/CD pipeline

### 5. Internationalization
- All user-facing text must be in both English and Te Reo Māori
- Use react-i18next for translations
- Keep translation keys organized by feature
- Always use translation hooks for dynamic content

### 6. Performance
- Implement code splitting and lazy loading
- Optimize images and assets
- Monitor bundle size
- Follow performance best practices from Lighthouse

### 7. Security
- Validate all user inputs
- Sanitize data before rendering
- Use environment variables for sensitive data
- Follow OWASP security guidelines

### 8. Accessibility
- Follow WCAG 2.1 AA standards
- Use semantic HTML
- Ensure keyboard navigation
- Test with screen readers
- Maintain sufficient color contrast

### 9. Documentation
- Keep documentation up-to-date
- Document complex components and hooks
- Update README with setup instructions
- Document environment variables

### 10. Development Workflow
1. Create a new branch for each feature
2. Write tests for new features
3. Run linter and tests before committing
4. Push to remote and create a pull request
5. Get code review before merging to main

### 11. Dependencies
- Use `mcp0_resolve-library-id` before adding new dependencies
- Check for existing solutions in the codebase
- Document the reason for adding new dependencies
- Keep dependencies up-to-date

### 12. Code Review
- Review for:
  - Code quality and readability
  - Performance implications
  - Security considerations
  - Test coverage
  - Documentation updates

### 13. Deployment
- Follow the deployment checklist in `tasks.md`
- Test in staging before production
- Monitor application after deployment
- Keep deployment documentation updated

### 14. Version Control
- Follow Git Flow branching strategy
- Use conventional commits format
- Write clear, descriptive commit messages
- Keep commits atomic and focused
- Use `datahub save -m "commit message"` to save changes to DataHub
  - Example: `datahub save -m "feat: add user authentication"`
  - Always include a descriptive message that follows conventional commits format

## Emergency Procedures
- For critical bugs, create a hotfix branch from main
- Document the issue and solution
- Add tests to prevent regression
- Update documentation if necessary

## Contact
For questions or clarification, refer to the project documentation or contact the project lead.

---
Last Updated: 2025-07-22