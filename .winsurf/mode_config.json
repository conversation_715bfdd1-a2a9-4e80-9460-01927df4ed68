{"name": "sailing-serai-mode", "description": "Custom mode for Sailing Serai project that enforces project rules and configurations", "version": "1.0.0", "beforeTask": [{"type": "readFile", "path": ".winsurf/.rules.md", "purpose": "Review project rules and guidelines before starting any task"}, {"type": "readFile", "path": ".winsurf/config.yml", "purpose": "Load project configuration"}, {"type": "readFile", "path": ".winsurf/Sailing_Serai/requirements.md", "purpose": "Review project requirements"}, {"type": "readFile", "path": ".winsurf/Sailing_Serai/design.md", "purpose": "Review design specifications"}, {"type": "readFile", "path": ".winsurf/Sailing_Serai/tasks.md", "purpose": "Review task list and dependencies"}], "rules": {"enforceCodeStyle": true, "requireTests": true, "checkDependencies": true, "validateConfig": true, "checkEnvVars": true}, "preferences": {"autoFormat": true, "suggestImprovements": true, "showDocumentationLinks": true, "enforceBestPractices": true}, "features": {"codeReview": {"enabled": true, "checklist": ["Follows project structure", "Meets accessibility standards", "Includes tests", "Documents complex logic", "Follows TypeScript best practices"]}, "validation": {"checkImports": true, "checkTypes": true, "checkSecurity": true, "checkPerformance": true}}, "keybindings": {"formatCode": "alt+shift+f", "runTests": "alt+t", "showDocumentation": "alt+d"}, "notifications": {"onRuleViolation": "warn", "onSuccess": "notify", "onError": "alert"}, "versionControl": {"enforceBranchNaming": true, "requirePullRequest": true, "enforceCommitMessage": true}}