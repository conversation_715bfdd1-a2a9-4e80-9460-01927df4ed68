# Sailing Serai Project Configuration
# This file contains project-wide configuration settings

# Project Metadata
project:
  name: "Sailing Serai"
  version: "1.0.0"
  description: "A responsive web application for Sailing Serai, offering sailing experiences in Auckland Harbour"
  repository: "https://github.com/yourusername/sailing-serai"
  license: "MIT"

# Development Environment
environment:
  node: ">=18.0.0"
  npm: ">=9.0.0"
  browsers:
    - "last 2 Chrome versions"
    - "last 2 Firefox versions"
    - "last 2 Safari versions"
    - "last 2 Edge versions"

# Build Configuration
build:
  sourceDir: "./src"
  outputDir: "./dist"
  publicPath: "/"
  assetsDir: "assets"

# Development Server
devServer:
  port: 3005  # Default Vite port (can be overridden by VITE_DEV_SERVER_PORT)
  host: "localhost"
  open: true
  hot: true
  https: false
  # Note: If you need to specify a different port, set VITE_DEV_SERVER_PORT in .env.local
  # Example: VITE_DEV_SERVER_PORT=4000

# Internationalization
i18n:
  defaultLocale: "en"
  supportedLocales: ["en", "mi"]
  localeDir: "./public/locales"
  namespaces: ["common", "home", "booking", "about"]

# Code Quality
codeQuality:
  eslint:
    config: "./eslint.config.js"
    extensions: [".js", ".jsx", ".ts", ".tsx"]
  prettier:
    config: "./.prettierrc"
    extensions: [
      ".js", ".jsx", ".ts", ".tsx", 
      ".json", ".md", 
      ".css", ".scss"
    ]
  stylelint:
    config: "./stylelint.config.js"
    extensions: [
      ".css", ".scss", 
      ".module.css", ".module.scss"
    ]

testing:
  unit:
    framework: "vitest"
    coverage: 80
    watch: true
  e2e:
    framework: "playwright"
    browsers: ["chromium", "firefox", "webkit"]
    headless: true

# Deployment
deployment:
  environments:
    staging:
      url: "https://staging.sailingserai.com"
      branch: "staging"
    production:
      url: "https://sailingserai.com"
      branch: "main"
  cdn:
    enabled: true
    provider: "cloudflare"

# Dependencies
dependencies:
  required:
    - "react"
    - "react-dom"
    - "react-router-dom"
    - "@tanstack/react-query"
    - "i18next"
    - "react-i18next"
    - "@hookform/resolvers"
    - "zod"
    - "@radix-ui/react-dialog"
    - "@radix-ui/react-slot"
    - "class-variance-authority"
    - "clsx"
    - "tailwind-merge"
    - "tailwindcss-animate"
    - "date-fns"

# Path Aliases
paths:
  "@/*": ["./src/*"]
  "@components/*": ["./src/components/*"]
  "@pages/*": ["./src/pages/*"]
  "@hooks/*": ["./src/hooks/*"]
  "@lib/*": ["./src/lib/*"]
  "@styles/*": ["./src/styles/*"]
  "@types/*": ["./src/types/*"]
  "@i18n/*": ["./src/i18n/*"]

# Environment Variables
env:
  required:
    - "VITE_API_BASE_URL"
    - "VITE_GOOGLE_MAPS_API_KEY"
    - "VITE_RECAPTCHA_SITE_KEY"
  optional:
    - "VITE_ANALYTICS_ID"
    - "VITE_SENTRY_DSN"

# Scripts
scripts:
  dev: "vite"
  build: "tsc && vite build"
  preview: "vite preview"
  lint: "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"
  format: "prettier --write \"**/*.{ts,tsx,md}\""
  test: "vitest"
  test:watch: "vitest watch"
  test:coverage: "vitest run --coverage"
  e2e: "playwright test"
  e2e:ui: "playwright test --ui"
  prepare: "husky install"