#!/bin/bash

# Create output directory if it doesn't exist
mkdir -p public/images/experiences

# Colors for different experience types
colors=("#3B82F6" "#10B981" "#F59E0B" "#8B5CF6" "#EC4899" "#14B8A6")
titles=("Sunset Sailing" "Island Adventure" "Whale Watching" "Private Charter" "Learn to Sail" "Overnight Sailing")

for i in {1..6}; do
  idx=$((i-1))
  color=${colors[$idx]}
  title="${titles[$idx]}"
  
  # Create a temporary SVG
  cat > /tmp/temp.svg <<EOL
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="${color}" opacity="0.7"/>
  <rect x="20" y="20" width="760" height="560" fill="none" stroke="white" stroke-width="2" stroke-dasharray="8,8"/>
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
        fill="white" font-family="sans-serif" font-size="24" font-weight="bold">
    ${title}
  </text>
</svg>
EOL

  # Convert SVG to JPG using ImageMagick
  convert /tmp/temp.svg "public/images/experiences/experience-${i}.jpg"
  echo "Generated placeholder for experience ${i}: ${title}"
done

echo "All placeholder images generated successfully!"
