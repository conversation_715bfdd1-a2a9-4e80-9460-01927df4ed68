import { promises as fs } from 'fs';
import { join } from 'path';
import sharp from 'sharp';

// Colors for different experience types
const COLORS = [
  '#3B82F6', // blue-500
  '#10B981', // emerald-500
  '#F59E0B', // amber-500
  '#8B5CF6', // violet-500
  '#EC4899', // pink-500
  '#14B8A6'  // teal-500
];

const WIDTH = 800;
const HEIGHT = 600;
const OUTPUT_DIR = join(__dirname, '../public/images/experiences');

async function generatePlaceholder(color: string, index: number): Promise<void> {
  const svg = `
    <svg width="${WIDTH}" height="${HEIGHT}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${color}" opacity="0.7"/>
      <rect x="20" y="20" width="${WIDTH - 40}" height="${HEIGHT - 40}" 
            fill="none" stroke="white" stroke-width="2" stroke-dasharray="8,8"/>
      <text x="50%" y="50%" 
            dominant-baseline="middle" 
            text-anchor="middle" 
            fill="white" 
            font-family="sans-serif" 
            font-size="24" 
            font-weight="bold">
        Experience ${index + 1}
      </text>
    </svg>
  `;

  const pngBuffer = await sharp(Buffer.from(svg))
    .png()
    .toBuffer();

  await fs.writeFile(join(OUTPUT_DIR, `experience-${index + 1}.jpg`), pngBuffer);
  console.log(`Generated placeholder for experience ${index + 1}`);
}

async function main() {
  try {
    await fs.mkdir(OUTPUT_DIR, { recursive: true });
    
    const promises = COLORS.map((color, index) => 
      generatePlaceholder(color, index)
    );
    
    await Promise.all(promises);
    console.log('All placeholder images generated successfully!');
  } catch (error) {
    console.error('Error generating placeholder images:', error);
    process.exit(1);
  }
}

main();
