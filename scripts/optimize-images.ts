import { promises as fs } from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import sharp from 'sharp';
import { glob } from 'glob';

// Get the current directory name in ES module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const IMAGE_DIR = path.join(process.cwd(), 'public');

// Add error type for TypeScript
type ErrorWithCode = Error & {
  code?: string;
};
const QUALITY = 80;
const SUPPORTED_EXTENSIONS = ['.jpg', '.jpeg', '.png'];
const RESOLUTIONS = [1, 2]; // 1x and 2x resolutions

// Ensure the output directories exist
async function ensureOutputDirs() {
  const outputDirs = ['/images/optimized'];
  
  for (const dir of outputDirs) {
    const fullPath = path.join(IMAGE_DIR, dir);
    try {
      await fs.mkdir(fullPath, { recursive: true });
    } catch (error) {
      const err = error as ErrorWithCode;
      if (err.code !== 'EEXIST') throw error;
    }
  }
}

// Process a single image file
async function processImage(filePath: string) {
  const ext = path.extname(filePath).toLowerCase();
  if (!SUPPORTED_EXTENSIONS.includes(ext)) return;

  const relativePath = path.relative(IMAGE_DIR, filePath);
  const dirName = path.dirname(relativePath);
  const baseName = path.basename(filePath, ext);
  const outputDir = path.join(IMAGE_DIR, dirName);

  console.log(`Processing: ${relativePath}`);

  try {
    // Process for each resolution
    for (const resolution of RESOLUTIONS) {
      const scale = resolution === 1 ? '' : `@${resolution}x`;
      const outputName = `${baseName}${scale}`;
      
      // Skip if output files already exist
      const webpPath = path.join(outputDir, `${outputName}.webp`);
      const avifPath = path.join(outputDir, `${outputName}.avif`);
      
      try {
        await fs.access(webpPath);
        await fs.access(avifPath);
        console.log(`  ✓ Already processed ${outputName} [${resolution}x]`);
        continue;
      } catch {
        // Files don't exist, continue with processing
      }

      // Load and process the image
      const image = sharp(filePath);
      const metadata = await image.metadata();
      
      // Skip if not an image
      if (!metadata.width || !metadata.height) {
        console.log(`  ⚠️ Skipping (invalid image): ${relativePath}`);
        return;
      }

      // Calculate new dimensions for the resolution
      const newWidth = Math.round(metadata.width * (resolution === 2 ? 2 : 1));
      const newHeight = metadata.height ? 
        Math.round(metadata.height * (resolution === 2 ? 2 : 1)) : 
        undefined;

      // Process WebP
      await image
        .clone()
        .resize(newWidth, newHeight, {
          fit: 'cover',
          position: 'center',
        })
        .webp({
          quality: QUALITY,
          effort: 6, // Higher effort = better compression, but slower
        })
        .toFile(path.join(outputDir, `${outputName}.webp`));

      // Process AVIF (only for 1x resolution to save space)
      if (resolution === 1) {
        await image
          .clone()
          .resize(newWidth, newHeight, {
            fit: 'cover',
            position: 'center',
          })
          .avif({
            quality: QUALITY - 10, // Slightly lower quality for AVIF
            effort: 8, // Higher effort = better compression, but much slower
          })
          .toFile(path.join(outputDir, `${outputName}.avif`));
      }

      console.log(`  ✓ Generated ${outputName} [${resolution}x]`);
    }
  } catch (error) {
    const err = error as Error;
    console.error(`  ❌ Error processing ${relativePath}:`, err.message);
  }
}

// Main function
async function main() {
  console.log('Starting image optimization...');
  
  try {
    // Ensure output directories exist
    await ensureOutputDirs();
    
    // Find all image files in the public directory
    const files = await glob('**/*.{jpg,jpeg,png}', { 
      cwd: IMAGE_DIR,
      absolute: true,
      ignore: ['**/node_modules/**', '**/dist/**', '**/optimized/**']
    });
    
    console.log(`Found ${files.length} images to process\n`);
    
    // Process each image
    for (const file of files) {
      await processImage(file);
    }
    
    console.log('\n✅ Image optimization complete!');
  } catch (error) {
    console.error('❌ Error during image optimization:', error);
    process.exit(1);
  }
}

// Run the script
main();
