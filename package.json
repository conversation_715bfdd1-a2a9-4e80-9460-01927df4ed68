{"name": "source", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "optimize-images": "NODE_OPTIONS='--loader ts-node/esm' node scripts/optimize-images.ts"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.83.0", "@types/react-big-calendar": "^1.16.2", "@types/recharts": "^2.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-big-calendar": "^1.19.4", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.0", "react-router-dom": "^7.7.0", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss7-compat": "^2.2.17", "@types/glob": "^9.0.0", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^9.8.8", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "glob": "^11.0.3", "globals": "^16.3.0", "postcss": "^7.0.39", "sharp": "^0.34.3", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vite-imagetools": "^7.1.0", "vite-plugin-image-optimizer": "^2.0.2"}}