import { useState, useCallback } from 'react';
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { ContactFormData, ContactFormResponse } from '@/types/contact';
import { submitContactForm } from '@/api/contact';

// Define the form validation schema
const contactFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  phone: z.string().optional(),
  subject: z.string().min(5, { message: 'Subject must be at least 5 characters' }),
  message: z.string().min(10, { message: 'Message must be at least 10 characters' }),
  privacyPolicy: z.boolean().refine(val => val === true, {
    message: 'You must accept the privacy policy',
  }),
  recaptchaToken: z.string().min(1, { message: 'Please complete the reCAPTCHA' }),
});

type ContactFormValues = z.infer<typeof contactFormSchema>;

interface UseContactFormOptions {
  /** reCAPTCHA site key */
  recaptchaSiteKey: string;
  
  /** Custom submit handler (defaults to using the API) */
  onSubmit?: (data: Omit<ContactFormData, 'recaptchaToken'>) => Promise<boolean>;
  
  /** Callback when form is successfully submitted */
  onSuccess?: (response: ContactFormResponse) => void;
  
  /** Callback when form submission fails */
  onError?: (error: Error) => void;
  
  /** Additional form data to include in the submission */
  additionalData?: Record<string, any>;
}

/**
 * Custom hook for handling contact form logic
 */
export function useContactForm({
  recaptchaSiteKey,
  onSubmit: customOnSubmit,
  onSuccess,
  onError,
  additionalData = {},
}: UseContactFormOptions) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    success: boolean;
    message: string;
    errors?: Record<string, string>;
  } | null>(null);

  // Initialize react-hook-form
  const {
    register,
    handleSubmit,
    setValue,
    trigger,
    reset,
    formState: { errors, isDirty, isValid },
  } = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    mode: 'onChange',
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: '',
      privacyPolicy: false,
      recaptchaToken: '',
    },
  });

  // Handle reCAPTCHA verification
  const handleRecaptchaChange = useCallback((token: string | null) => {
    if (token) {
      setValue('recaptchaToken', token, { shouldValidate: true });
    } else {
      setValue('recaptchaToken', '', { shouldValidate: true });
    }
  }, [setValue]);

  // Reset the form
  const resetForm = useCallback(() => {
    reset();
    setSubmitStatus(null);
    
    // Reset reCAPTCHA
    if (window.grecaptcha) {
      window.grecaptcha.reset();
    }
  }, [reset]);

  // Handle form submission
  const handleFormSubmit: SubmitHandler<ContactFormValues> = useCallback(async (data) => {
    if (isSubmitting) return;
    
    try {
      setIsSubmitting(true);
      setSubmitStatus(null);
      
      // Extract recaptchaToken from the form data
      const { recaptchaToken, ...formData } = data;
      
      // Merge with additional data
      const submissionData = {
        ...formData,
        ...additionalData,
        metadata: {
          submittedAt: new Date().toISOString(),
          ...(additionalData.metadata || {}),
        },
      };
      
      // Use custom submit handler if provided, otherwise use the default API
      const success = customOnSubmit 
        ? await customOnSubmit(submissionData)
        : await submitContactForm(submissionData);
      
      if (success) {
        const successMessage = 'Your message has been sent successfully! We\'ll get back to you soon.';
        setSubmitStatus({
          success: true,
          message: successMessage,
        });
        
        // Reset form on success
        resetForm();
        
        // Call success callback if provided
        if (onSuccess) {
          onSuccess({
            success: true,
            message: successMessage,
            data: {
              submittedAt: new Date().toISOString(),
            },
          });
        }
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      
      const errorMessage = error instanceof Error 
        ? error.message 
        : 'Failed to send message. Please try again later or contact us <NAME_EMAIL>';
      
      setSubmitStatus({
        success: false,
        message: errorMessage,
      });
      
      // Call error callback if provided
      if (onError) {
        onError(error instanceof Error ? error : new Error(errorMessage));
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [isSubmitting, customOnSubmit, onSuccess, onError, resetForm, additionalData]);

  return {
    // Form state
    isSubmitting,
    submitStatus,
    errors,
    isDirty,
    isValid,
    
    // Form methods
    register,
    handleSubmit: handleSubmit(handleFormSubmit),
    setValue,
    trigger,
    reset: resetForm,
    
    // reCAPTCHA
    recaptchaSiteKey,
    handleRecaptchaChange,
    
    // Helper functions
    getFieldError: (field: keyof ContactFormValues) => 
      errors[field]?.message?.toString(),
    
    // Form fields metadata
    fields: {
      name: register('name'),
      email: register('email'),
      phone: register('phone'),
      subject: register('subject'),
      message: register('message'),
      privacyPolicy: register('privacyPolicy'),
    } as const,
  };
}

export type UseContactFormReturn = ReturnType<typeof useContactForm>;
