import { jsx as _jsx } from "react/jsx-runtime";
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { App } from './App';
import { BlogPage } from './pages/BlogPage';
import { BlogPostPage } from './pages/BlogPostPage';
import { AdminLayout } from './layouts/AdminLayout';
import { DashboardPage } from './pages/admin/DashboardPage';
import { BookingsPage } from './pages/admin/BookingsPage';
import { CalendarPage } from './pages/admin/CalendarPage';
import { CustomersPage } from './pages/admin/CustomersPage';
import { ReportsPage } from './pages/admin/ReportsPage';
import { BlogListPage } from './pages/admin/blog/BlogListPage';
import { BlogEditPage } from './pages/admin/blog/BlogEditPage';
import { LoginPage } from './pages/admin/LoginPage';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
export const router = createBrowserRouter([
    {
        path: '/',
        element: _jsx(App, {}),
        children: [
            {
                path: '/',
                element: _jsx(Navigate, { to: "/home", replace: true }),
            },
            {
                path: 'home',
                element: _jsx("div", { children: "Home Page" }),
            },
            {
                path: 'destinations',
                element: _jsx("div", { children: "Destinations Page" }),
            },
            {
                path: 'yachts',
                element: _jsx("div", { children: "Yachts Page" }),
            },
            {
                path: 'about',
                element: _jsx("div", { children: "About Page" }),
            },
            {
                path: 'contact',
                element: _jsx("div", { children: "Contact Page" }),
            },
            {
                path: 'blog',
                children: [
                    {
                        path: '',
                        element: _jsx(BlogPage, {}),
                    },
                    {
                        path: ':slug',
                        element: _jsx(BlogPostPage, {}),
                    },
                ],
            },
        ],
    },
    {
        path: '/admin',
        element: _jsx(AdminLayout, {}),
        children: [
            {
                path: 'login',
                element: _jsx(LoginPage, {}),
            },
            {
                path: '',
                element: _jsx(ProtectedRoute, {}),
                children: [
                    {
                        path: '',
                        element: _jsx(Navigate, { to: "dashboard", replace: true }),
                    },
                    {
                        path: 'dashboard',
                        element: _jsx(DashboardPage, {}),
                    },
                    {
                        path: 'bookings',
                        element: _jsx(BookingsPage, {}),
                    },
                    {
                        path: 'calendar',
                        element: _jsx(CalendarPage, {}),
                    },
                    {
                        path: 'customers',
                        element: _jsx(CustomersPage, {}),
                    },
                    {
                        path: 'reports',
                        element: _jsx(ReportsPage, {}),
                    },
                    {
                        path: 'blog',
                        children: [
                            {
                                path: '',
                                element: _jsx(BlogListPage, {}),
                            },
                            {
                                path: 'new',
                                element: _jsx(BlogEditPage, {}),
                            },
                            {
                                path: 'edit/:id',
                                element: _jsx(BlogEditPage, {}),
                            },
                        ],
                    },
                ],
            },
        ],
    },
    {
        path: '*',
        element: _jsx(Navigate, { to: "/", replace: true }),
    },
]);
