/* Reset default styles that might interfere with layout */
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Ensure full width for main content */
main {
  flex: 1;
}

/* Reset default button styles */
button, input[type="button"], input[type="submit"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

/* Ensure images are responsive */
img {
  max-width: 100%;
  height: auto;
}

/* Base typography */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  color: #1a1a1a;
  background-color: #ffffff;
}

/* Links */
a {
  color: #2563eb;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
