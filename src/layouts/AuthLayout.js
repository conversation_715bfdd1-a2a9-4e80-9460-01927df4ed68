import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Outlet, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { SailingSeraiLogo } from '@/components/SailingSeraiLogo';
export function AuthLayout() {
    const { t } = useTranslation();
    return (_jsxs("div", { className: "min-h-screen bg-gray-50 flex flex-col", children: [_jsx("header", { className: "bg-white shadow-sm", children: _jsx("div", { className: "max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8", children: _jsxs("div", { className: "flex justify-between items-center", children: [_jsxs(Link, { to: "/", className: "flex items-center", children: [_jsx(SailingSeraiLogo, { className: "h-8 w-auto" }), _jsx("span", { className: "ml-2 text-xl font-semibold text-gray-900", children: "Sailing Serai" })] }), _jsx("div", { className: "flex items-center space-x-4", children: _jsx(Link, { to: "/", className: "text-sm font-medium text-gray-500 hover:text-gray-900", children: t('auth.returnToHome', 'Return to home') }) })] }) }) }), _jsx("main", { className: "flex-grow flex items-center justify-center px-4 py-12 sm:px-6 lg:px-8", children: _jsxs("div", { className: "w-full max-w-md space-y-8", children: [_jsxs("div", { className: "text-center", children: [_jsx("h2", { className: "mt-6 text-3xl font-extrabold text-gray-900", children: t('auth.welcomeBack', 'Welcome back!') }), _jsx("p", { className: "mt-2 text-sm text-gray-600", children: t('auth.signInToContinue', 'Sign in to your account to continue') })] }), _jsx("div", { className: "bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10", children: _jsx(Outlet, {}) })] }) }), _jsx("footer", { className: "bg-white py-4 border-t", children: _jsx("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: _jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("p", { className: "text-sm text-gray-500", children: ["\u00A9 ", new Date().getFullYear(), " Sailing Serai. ", t('common.allRightsReserved', 'All rights reserved.')] }), _jsxs("div", { className: "flex space-x-6", children: [_jsxs("a", { href: "#", className: "text-gray-400 hover:text-gray-500", children: [_jsx("span", { className: "sr-only", children: "Privacy" }), t('common.privacy', 'Privacy')] }), _jsxs("a", { href: "#", className: "text-gray-400 hover:text-gray-500", children: [_jsx("span", { className: "sr-only", children: "Terms" }), t('common.terms', 'Terms')] })] })] }) }) })] }));
}
export default AuthLayout;
