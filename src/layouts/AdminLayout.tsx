import React, { useState } from 'react';
import { Outlet, Link, useLocation, Navigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  LayoutDashboard, 
  Calendar, 
  Users, 
  FileText, 
  Settings, 
  Menu, 
  X,
  BookOpen,
  BarChart2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';

type NavItem = {
  name: string;
  href: string;
  icon: React.ReactNode;
  children?: NavItem[];
};

export const AdminLayout: React.FC = () => {
  const { t } = useTranslation();
  const { user, logout } = useAuth();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Navigation items
  const navigation: NavItem[] = [
    {
      name: t('admin.dashboard', 'Dashboard'),
      href: '/admin/dashboard',
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      name: t('admin.bookings', 'Bookings'),
      href: '/admin/bookings',
      icon: <Calendar className="h-5 w-5" />,
    },
    {
      name: t('admin.calendar', 'Calendar'),
      href: '/admin/calendar',
      icon: <Calendar className="h-5 w-5" />,
    },
    {
      name: t('admin.customers', 'Customers'),
      href: '/admin/customers',
      icon: <Users className="h-5 w-5" />,
    },
    {
      name: t('admin.blog', 'Blog'),
      href: '/admin/blog',
      icon: <BookOpen className="h-5 w-5" />,
    },
    {
      name: t('admin.reports', 'Reports'),
      href: '/admin/reports',
      icon: <BarChart2 className="h-5 w-5" />,
    },
  ];

  // Redirect to login if not authenticated
  if (!user && location.pathname !== '/admin/login') {
    return <Navigate to="/admin/login" state={{ from: location }} replace />;
  }

  // Redirect to dashboard if already authenticated and on login page
  if (user && location.pathname === '/admin/login') {
    return <Navigate to="/admin/dashboard" replace />;
  }

  // Don't render layout for login page
  if (location.pathname === '/admin/login') {
    return <Outlet />;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Mobile menu */}
      <div className="lg:hidden">
        <div className="flex items-center justify-between bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center
          ">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="mr-2"
            >
              {mobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
              <span className="sr-only">
                {mobileMenuOpen ? 'Close menu' : 'Open menu'}
              </span>
            </Button>
            <h1 className="text-lg font-semibold">
              {t('admin.title', 'Admin Dashboard')}
            </h1>
          </div>
          <div className="flex items-center">
            <Button variant="ghost" size="sm" onClick={logout}>
              {t('auth.logout', 'Logout')}
            </Button>
          </div>
        </div>

        {mobileMenuOpen && (
          <div className="bg-white border-b border-gray-200 px-4 py-2">
            <nav className="space-y-1">
              {navigation.map((item) => (
                <Link
                  key={item.href}
                  to={item.href}
                  className={`group flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                    location.pathname === item.href
                      ? 'bg-gray-100 text-gray-900'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <span className="mr-3">{item.icon}</span>
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
        )}
      </div>

      <div className="flex h-screen overflow-hidden bg-gray-100">
        {/* Desktop sidebar */}
        <div className="hidden lg:flex lg:flex-shrink-0">
          <div className="flex flex-col w-64 border-r border-gray-200 bg-white">
            <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
              <div className="flex items-center flex-shrink-0 px-4">
                <h1 className="text-xl font-bold text-gray-900">
                  {t('admin.title', 'Admin Dashboard')}
                </h1>
              </div>
              <nav className="mt-5 flex-1 px-2 space-y-1">
                {navigation.map((item) => (
                  <Link
                    key={item.href}
                    to={item.href}
                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                      location.pathname === item.href
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <span className="mr-3">{item.icon}</span>
                    {item.name}
                  </Link>
                ))}
              </nav>
            </div>
            <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
              <div className="flex items-center">
                <div>
                  <div className="text-base font-medium text-gray-800">
                    {user?.name || 'Admin'}
                  </div>
                  <div className="text-sm font-medium text-gray-500">
                    {user?.email || '<EMAIL>'}
                  </div>
                </div>
                <div className="ml-3">
                  <Button variant="ghost" size="sm" onClick={logout}>
                    {t('auth.logout', 'Logout')}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 overflow-auto focus:outline-none">
          <main className="flex-1 relative pb-8 z-0 overflow-y-auto">
            <div className="bg-white shadow-sm">
              <div className="px-4 sm:px-6 lg:px-8 py-4">
                <h1 className="text-2xl font-semibold text-gray-900">
                  {navigation.find((item) => location.pathname === item.href)?.name ||
                    navigation.find((item) => location.pathname.startsWith(item.href))?.name ||
                    'Admin'}
                </h1>
              </div>
            </div>
            <div className="px-4 sm:px-6 lg:px-8 py-6">
              <Outlet />
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;
