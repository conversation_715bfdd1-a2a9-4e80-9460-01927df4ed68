import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Outlet, Link, useLocation, Navigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { LayoutDashboard, Calendar, Users, Menu, X, BookOpen, BarChart2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
export const AdminLayout = () => {
    const { t } = useTranslation();
    const { user, logout } = useAuth();
    const location = useLocation();
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    // Navigation items
    const navigation = [
        {
            name: t('admin.dashboard', 'Dashboard'),
            href: '/admin/dashboard',
            icon: _jsx(LayoutDashboard, { className: "h-5 w-5" }),
        },
        {
            name: t('admin.bookings', 'Bookings'),
            href: '/admin/bookings',
            icon: _jsx(Calendar, { className: "h-5 w-5" }),
        },
        {
            name: t('admin.calendar', 'Calendar'),
            href: '/admin/calendar',
            icon: _jsx(Calendar, { className: "h-5 w-5" }),
        },
        {
            name: t('admin.customers', 'Customers'),
            href: '/admin/customers',
            icon: _jsx(Users, { className: "h-5 w-5" }),
        },
        {
            name: t('admin.blog', 'Blog'),
            href: '/admin/blog',
            icon: _jsx(BookOpen, { className: "h-5 w-5" }),
        },
        {
            name: t('admin.reports', 'Reports'),
            href: '/admin/reports',
            icon: _jsx(BarChart2, { className: "h-5 w-5" }),
        },
    ];
    // Redirect to login if not authenticated
    if (!user && location.pathname !== '/admin/login') {
        return _jsx(Navigate, { to: "/admin/login", state: { from: location }, replace: true });
    }
    // Redirect to dashboard if already authenticated and on login page
    if (user && location.pathname === '/admin/login') {
        return _jsx(Navigate, { to: "/admin/dashboard", replace: true });
    }
    // Don't render layout for login page
    if (location.pathname === '/admin/login') {
        return _jsx(Outlet, {});
    }
    return (_jsxs("div", { className: "min-h-screen bg-gray-100", children: [_jsxs("div", { className: "lg:hidden", children: [_jsxs("div", { className: "flex items-center justify-between bg-white border-b border-gray-200 px-4 py-3", children: [_jsxs("div", { className: "flex items-center\n          ", children: [_jsxs(Button, { variant: "ghost", size: "icon", onClick: () => setMobileMenuOpen(!mobileMenuOpen), className: "mr-2", children: [mobileMenuOpen ? (_jsx(X, { className: "h-5 w-5" })) : (_jsx(Menu, { className: "h-5 w-5" })), _jsx("span", { className: "sr-only", children: mobileMenuOpen ? 'Close menu' : 'Open menu' })] }), _jsx("h1", { className: "text-lg font-semibold", children: t('admin.title', 'Admin Dashboard') })] }), _jsx("div", { className: "flex items-center", children: _jsx(Button, { variant: "ghost", size: "sm", onClick: logout, children: t('auth.logout', 'Logout') }) })] }), mobileMenuOpen && (_jsx("div", { className: "bg-white border-b border-gray-200 px-4 py-2", children: _jsx("nav", { className: "space-y-1", children: navigation.map((item) => (_jsxs(Link, { to: item.href, className: `group flex items-center px-3 py-2 text-sm font-medium rounded-md ${location.pathname === item.href
                                    ? 'bg-gray-100 text-gray-900'
                                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`, onClick: () => setMobileMenuOpen(false), children: [_jsx("span", { className: "mr-3", children: item.icon }), item.name] }, item.href))) }) }))] }), _jsxs("div", { className: "flex h-screen overflow-hidden bg-gray-100", children: [_jsx("div", { className: "hidden lg:flex lg:flex-shrink-0", children: _jsxs("div", { className: "flex flex-col w-64 border-r border-gray-200 bg-white", children: [_jsxs("div", { className: "flex-1 flex flex-col pt-5 pb-4 overflow-y-auto", children: [_jsx("div", { className: "flex items-center flex-shrink-0 px-4", children: _jsx("h1", { className: "text-xl font-bold text-gray-900", children: t('admin.title', 'Admin Dashboard') }) }), _jsx("nav", { className: "mt-5 flex-1 px-2 space-y-1", children: navigation.map((item) => (_jsxs(Link, { to: item.href, className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md ${location.pathname === item.href
                                                    ? 'bg-gray-100 text-gray-900'
                                                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`, children: [_jsx("span", { className: "mr-3", children: item.icon }), item.name] }, item.href))) })] }), _jsx("div", { className: "flex-shrink-0 flex border-t border-gray-200 p-4", children: _jsxs("div", { className: "flex items-center", children: [_jsxs("div", { children: [_jsx("div", { className: "text-base font-medium text-gray-800", children: user?.name || 'Admin' }), _jsx("div", { className: "text-sm font-medium text-gray-500", children: user?.email || '<EMAIL>' })] }), _jsx("div", { className: "ml-3", children: _jsx(Button, { variant: "ghost", size: "sm", onClick: logout, children: t('auth.logout', 'Logout') }) })] }) })] }) }), _jsx("div", { className: "flex-1 overflow-auto focus:outline-none", children: _jsxs("main", { className: "flex-1 relative pb-8 z-0 overflow-y-auto", children: [_jsx("div", { className: "bg-white shadow-sm", children: _jsx("div", { className: "px-4 sm:px-6 lg:px-8 py-4", children: _jsx("h1", { className: "text-2xl font-semibold text-gray-900", children: navigation.find((item) => location.pathname === item.href)?.name ||
                                                navigation.find((item) => location.pathname.startsWith(item.href))?.name ||
                                                'Admin' }) }) }), _jsx("div", { className: "px-4 sm:px-6 lg:px-8 py-6", children: _jsx(Outlet, {}) })] }) })] })] }));
};
export default AdminLayout;
