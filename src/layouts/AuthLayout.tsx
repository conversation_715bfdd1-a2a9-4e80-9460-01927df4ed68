import { Outlet, <PERSON> } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { SailingSeraiLogo } from '@/components/SailingSeraiLogo';

export function AuthLayout() {
  const { t } = useTranslation();
  
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <Link to="/" className="flex items-center">
              <SailingSeraiLogo className="h-8 w-auto" />
              <span className="ml-2 text-xl font-semibold text-gray-900">
                Sailing Serai
              </span>
            </Link>
            <div className="flex items-center space-x-4">
              <Link
                to="/"
                className="text-sm font-medium text-gray-500 hover:text-gray-900"
              >
                {t('auth.returnToHome', 'Return to home')}
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow flex items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              {t('auth.welcomeBack', 'Welcome back!')}
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              {t('auth.signInToContinue', 'Sign in to your account to continue')}
            </p>
          </div>
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <Outlet />
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white py-4 border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-500">
              &copy; {new Date().getFullYear()} Sailing Serai. {t('common.allRightsReserved', 'All rights reserved.')}
            </p>
            <div className="flex space-x-6">
              <a href="#" className="text-gray-400 hover:text-gray-500">
                <span className="sr-only">Privacy</span>
                {t('common.privacy', 'Privacy')}
              </a>
              <a href="#" className="text-gray-400 hover:text-gray-500">
                <span className="sr-only">Terms</span>
                {t('common.terms', 'Terms')}
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default AuthLayout;
