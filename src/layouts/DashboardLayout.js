import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Sidebar } from '@/components/admin/Sidebar';
import { Header } from '@/components/admin/Header';
import { Toaster } from '@/components/ui/toaster';
import { SailingSeraiLogo } from '@/components/SailingSeraiLogo';
export function DashboardLayout() {
    const { user, logout } = useAuth();
    const location = useLocation();
    const navigate = useNavigate();
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    // Close mobile menu when location changes
    useEffect(() => {
        setMobileMenuOpen(false);
    }, [location]);
    // Handle logout
    const handleLogout = async () => {
        try {
            await logout();
            navigate('/auth/login');
        }
        catch (error) {
            console.error('Logout error:', error);
        }
    };
    if (!user) {
        return null; // or a loading spinner
    }
    return (_jsxs("div", { className: "flex h-screen bg-gray-100", children: [_jsx("div", { className: `fixed inset-0 z-40 lg:hidden ${mobileMenuOpen ? 'block' : 'hidden'}`, onClick: () => setMobileMenuOpen(false), children: _jsx("div", { className: "fixed inset-0 bg-gray-600 bg-opacity-75", "aria-hidden": "true" }) }), _jsx("div", { className: `fixed inset-y-0 left-0 z-50 w-64 transform ${mobileMenuOpen ? 'translate-x-0' : '-translate-x-full'} lg:relative lg:translate-x-0 transition duration-200 ease-in-out`, children: _jsxs("div", { className: "flex h-full flex-col bg-white shadow-lg", children: [_jsx("div", { className: "flex h-16 flex-shrink-0 items-center px-4", children: _jsxs(Link, { to: "/", className: "flex items-center", children: [_jsx(SailingSeraiLogo, { className: "h-8 w-auto" }), _jsx("span", { className: "ml-2 text-xl font-semibold text-gray-900", children: "Sailing Serai" })] }) }), _jsx(Sidebar, { user: user, onLogout: handleLogout })] }) }), _jsxs("div", { className: "flex flex-1 flex-col overflow-hidden", children: [_jsx(Header, { onMenuClick: () => setMobileMenuOpen(!mobileMenuOpen), user: user, onLogout: handleLogout }), _jsx("main", { className: "flex-1 overflow-y-auto bg-gray-50 p-4 md:p-6", children: _jsx("div", { className: "mx-auto max-w-7xl", children: _jsx(Outlet, {}) }) })] }), _jsx(Toaster, {})] }));
}
export default DashboardLayout;
