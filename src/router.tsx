import { createBrowserRouter, Navigate } from 'react-router-dom';
import { App } from './App';
import { BlogPage } from './pages/BlogPage';
import { BlogPostPage } from './pages/BlogPostPage';
import { AdminLayout } from './layouts/AdminLayout';
import { DashboardPage } from './pages/admin/DashboardPage';
import { BookingsPage } from './pages/admin/BookingsPage';
import { CalendarPage } from './pages/admin/CalendarPage';
import { CustomersPage } from './pages/admin/CustomersPage';
import { ReportsPage } from './pages/admin/ReportsPage';
import { BlogListPage } from './pages/admin/blog/BlogListPage';
import { BlogEditPage } from './pages/admin/blog/BlogEditPage';
import { LoginPage } from './pages/admin/LoginPage';
import { ProtectedRoute } from './components/auth/ProtectedRoute';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: [
      {
        path: '/',
        element: <Navigate to="/home" replace />,
      },
      {
        path: 'home',
        element: <div>Home Page</div>,
      },
      {
        path: 'destinations',
        element: <div>Destinations Page</div>,
      },
      {
        path: 'yachts',
        element: <div>Yachts Page</div>,
      },
      {
        path: 'about',
        element: <div>About Page</div>,
      },
      {
        path: 'contact',
        element: <div>Contact Page</div>,
      },
      {
        path: 'blog',
        children: [
          {
            path: '',
            element: <BlogPage />,
          },
          {
            path: ':slug',
            element: <BlogPostPage />,
          },
        ],
      },
    ],
  },
  {
    path: '/admin',
    element: <AdminLayout />,
    children: [
      {
        path: 'login',
        element: <LoginPage />,
      },
      {
        path: '',
        element: <ProtectedRoute />,
        children: [
          {
            path: '',
            element: <Navigate to="dashboard" replace />,
          },
          {
            path: 'dashboard',
            element: <DashboardPage />,
          },
          {
            path: 'bookings',
            element: <BookingsPage />,
          },
          {
            path: 'calendar',
            element: <CalendarPage />,
          },
          {
            path: 'customers',
            element: <CustomersPage />,
          },
          {
            path: 'reports',
            element: <ReportsPage />,
          },
          {
            path: 'blog',
            children: [
              {
                path: '',
                element: <BlogListPage />,
              },
              {
                path: 'new',
                element: <BlogEditPage />,
              },
              {
                path: 'edit/:id',
                element: <BlogEditPage />,
              },
            ],
          },
        ],
      },
    ],
  },
  {
    path: '*',
    element: <Navigate to="/" replace />,
  },
]);
