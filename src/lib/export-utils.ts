/**
 * Utility functions for exporting data to different formats
 */

type ExportFormat = 'csv' | 'pdf';

/**
 * Exports data to CSV format
 * @param data Array of objects to export
 * @param filename Base filename (without extension)
 */
export function exportToCsv<T extends Record<string, any>>(data: T[], filename: string): void {
  if (!data || data.length === 0) {
    console.warn('No data to export');
    return;
  }

  try {
    const headers = Object.keys(data[0]);
    const csvRows = [];
    
    // Add headers
    csvRows.push(headers.join(','));
    
    // Add rows
    for (const row of data) {
      const values = headers.map(header => {
        const value = row[header];
        // Handle nested objects and arrays
        const formattedValue = typeof value === 'object' 
          ? JSON.stringify(value)
          : String(value);
        // Escape quotes and wrap in quotes
        return `"${formattedValue.replace(/"/g, '""')}"`;
      });
      csvRows.push(values.join(','));
    }
    
    // Create CSV file
    const csvString = csvRows.join('\n');
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error exporting to CSV:', error);
    throw new Error('Failed to export data to CSV');
  }
}

/**
 * Exports data to PDF format (stub implementation)
 * @param data Array of objects to export
 * @param filename Base filename (without extension)
 * @param options PDF generation options
 */
export function exportToPdf<T extends Record<string, any>>(
  data: T[], 
  filename: string, 
  options?: {
    title?: string;
    columns?: string[];
    columnLabels?: Record<string, string>;
  }
): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      // This is a stub implementation
      // In a real app, you would use a library like jsPDF or pdf-lib
      console.log('PDF export functionality would be implemented here', {
        data,
        filename,
        options
      });
      
      // For now, we'll simulate a successful export
      setTimeout(() => {
        console.log(`PDF exported: ${filename}.pdf`);
        resolve();
      }, 1000);
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      reject(new Error('Failed to export data to PDF'));
    }
  });
}

/**
 * Main export function that handles different export formats
 * @param format Export format ('csv' or 'pdf')
 * @param data Data to export
 * @param filename Base filename (without extension)
 * @param options Format-specific options
 */
export async function exportData<T extends Record<string, any>>(
  format: ExportFormat,
  data: T[],
  filename: string,
  options?: any
): Promise<void> {
  switch (format) {
    case 'csv':
      return exportToCsv(data, filename);
    case 'pdf':
      return exportToPdf(data, filename, options);
    default:
      throw new Error(`Unsupported export format: ${format}`);
  }
}

export default {
  exportToCsv,
  exportToPdf,
  exportData
};
