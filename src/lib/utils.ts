import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// Type for currency formatting options
interface FormatCurrencyOptions {
  currency?: string;
  locale?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
}
 
/**
 * Combines multiple class names using clsx and tailwind-merge
 * @param inputs - Class values to combine
 * @returns A single className string with merged Tailwind classes
 */
export function cn(...inputs: ClassValue[]): string {
  try {
    return twMerge(clsx(inputs));
  } catch (error) {
    console.error('Error combining class names:', error);
    // Fallback to basic class combination if there's an error with twMerge
    return clsx(inputs) as string;
  }
}

/**
 * Formats a number as a currency string with proper error handling
 * @param amount - The amount to format (number or string that can be converted to a number)
 * @param options - Formatting options (currency, locale, etc.)
 * @returns Formatted currency string, or empty string on error
 */
export function formatCurrency(
  amount: number | string,
  options: FormatCurrencyOptions = {}
): string {
  const {
    currency = 'NZD',
    locale = 'en-NZ',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
  } = options;

  try {
    // Convert string amounts to numbers
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    // Handle invalid numbers
    if (isNaN(numericAmount)) {
      console.warn(`Invalid amount provided to formatCurrency: ${amount}`);
      return '';
    }
    
    // Handle very large numbers that might cause formatting issues
    if (Math.abs(numericAmount) > Number.MAX_SAFE_INTEGER) {
      console.warn('Amount exceeds maximum safe integer, precision may be lost');
    }
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits,
      maximumFractionDigits,
    }).format(numericAmount);
  } catch (error) {
    console.error('Error formatting currency:', error);
    return ''; // Return empty string on error
  }
}

/**
 * Safely parses a JSON string into an object
 * @param jsonString - The JSON string to parse
 * @param defaultValue - The default value to return if parsing fails
 * @returns The parsed object or the default value
 */
export function safeJsonParse<T = any>(
  jsonString: string,
  defaultValue: T
): T {
  try {
    return jsonString ? JSON.parse(jsonString) : defaultValue;
  } catch (error) {
    console.error('Error parsing JSON:', error);
    return defaultValue;
  }
}

/**
 * Debounces a function call
 * @param func - The function to debounce
 * @param wait - The number of milliseconds to delay
 * @returns A debounced version of the function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  
  return function(this: ThisParameterType<T>, ...args: Parameters<T>) {
    const context = this;
    
    const later = () => {
      timeout = null;
      func.apply(context, args);
    };
    
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(later, wait);
  };
}

/**
 * Generates a unique ID
 * @param prefix - Optional prefix for the ID
 * @returns A unique ID string
 */
export function generateId(prefix: string = 'id'): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Checks if a value is empty (null, undefined, empty string, empty array, or empty object)
 * @param value - The value to check
 * @returns True if the value is empty, false otherwise
 */
export function isEmpty(value: any): boolean {
  if (value === null || value === undefined) {
    return true;
  }
  
  if (typeof value === 'string' || Array.isArray(value)) {
    return value.length === 0;
  }
  
  if (typeof value === 'object') {
    return Object.keys(value).length === 0;
  }
  
  return false;
}

/**
 * Truncates a string to a specified length and adds an ellipsis if needed
 * @param str - The string to truncate
 * @param maxLength - The maximum length of the string
 * @param ellipsis - The ellipsis string to append (default: '...')
 * @returns The truncated string
 */
export function truncateString(
  str: string,
  maxLength: number,
  ellipsis: string = '...'
): string {
  if (!str || str.length <= maxLength) {
    return str;
  }
  
  return str.substring(0, maxLength) + (str.length > maxLength ? ellipsis : '');
}
