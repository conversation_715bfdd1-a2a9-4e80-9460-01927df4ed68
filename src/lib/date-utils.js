import { format, parseISO, isBefore, addDays, addMonths, isWeekend, isToday, isValid } from 'date-fns';
export const formatDate = (date, formatStr = 'MMMM d, yyyy') => {
    try {
        const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
        if (!isValid(dateObj)) {
            throw new Error(`Invalid date: ${date}`);
        }
        return format(dateObj, formatStr);
    }
    catch (error) {
        console.error('Error formatting date:', error);
        throw new Error(`Failed to format date: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};
export const formatTime = (date, formatStr = 'h:mm a') => {
    try {
        const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
        if (!isValid(dateObj)) {
            throw new Error(`Invalid date: ${date}`);
        }
        return format(dateObj, formatStr);
    }
    catch (error) {
        console.error('Error formatting time:', error);
        throw new Error(`Failed to format time: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};
/**
 * Checks if a date is available based on business rules
 * @param date - The date to check
 * @param unavailableDates - Array of unavailable dates
 * @returns boolean indicating if the date is available
 */
export const isDateAvailable = (date, unavailableDates = []) => {
    try {
        const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
        if (!isValid(dateObj)) {
            return false;
        }
        // Check if the date is in the past (excluding today)
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        if (isBefore(dateObj, today)) {
            return false;
        }
        // Check if it's a weekend
        if (isWeekend(dateObj)) {
            return false;
        }
        // Check if it's in the unavailable dates
        const isUnavailable = unavailableDates.some(unavailableDate => {
            const unavailableDateObj = typeof unavailableDate === 'string' ? parseISO(unavailableDate) : new Date(unavailableDate);
            return formatDate(unavailableDateObj) === formatDate(dateObj);
        });
        return !isUnavailable;
    }
    catch (error) {
        console.error('Error checking date availability:', error);
        return false;
    }
};
/**
 * Gets an array of available dates starting from a specific date
 * @param startDate - The start date
 * @param days - Number of days to check
 * @param unavailableDates - Array of unavailable dates
 * @returns Array of available dates
 */
export const getAvailableDates = (startDate, days, unavailableDates = []) => {
    try {
        const startDateObj = typeof startDate === 'string' ? parseISO(startDate) : new Date(startDate);
        if (!isValid(startDateObj)) {
            throw new Error(`Invalid start date: ${startDate}`);
        }
        if (days <= 0) {
            return [];
        }
        const availableDates = [];
        let currentDate = new Date(startDateObj);
        for (let i = 0; i < days; i++) {
            if (isDateAvailable(currentDate, unavailableDates)) {
                availableDates.push(new Date(currentDate));
            }
            currentDate = addDays(currentDate, 1);
        }
        return availableDates;
    }
    catch (error) {
        console.error('Error getting available dates:', error);
        throw new Error(`Failed to get available dates: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};
/**
 * Gets the next available date based on business rules
 * @param unavailableDates - Array of unavailable dates
 * @returns The next available date
 */
export const getNextAvailableDate = (unavailableDates = []) => {
    try {
        let date = new Date();
        // Start checking from tomorrow
        date = addDays(date, 1);
        // Safety counter to prevent infinite loops
        let maxAttempts = 365; // One year ahead maximum
        let attempts = 0;
        // Find the next available date that's not in the unavailable dates and not a weekend
        while (!isDateAvailable(date, unavailableDates) && attempts < maxAttempts) {
            date = addDays(date, 1);
            attempts++;
        }
        if (attempts >= maxAttempts) {
            throw new Error('Could not find an available date within the next year');
        }
        return date;
    }
    catch (error) {
        console.error('Error getting next available date:', error);
        // Fallback: return a date 7 days from now
        return addDays(new Date(), 7);
    }
};
/**
 * Checks if a date is in the past (before today)
 * @param date - The date to check
 * @returns boolean indicating if the date is in the past
 */
export const isPastDate = (date) => {
    try {
        const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
        if (!isValid(dateObj)) {
            throw new Error(`Invalid date: ${date}`);
        }
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return isBefore(dateObj, today);
    }
    catch (error) {
        console.error('Error checking if date is in the past:', error);
        return false;
    }
};
/**
 * Checks if a date is in the future (today or later)
 * @param date - The date to check
 * @returns boolean indicating if the date is in the future
 */
export const isFutureDate = (date) => {
    return !isPastDate(date);
};
/**
 * Gets the full day name of a date
 * @param date - The date
 * @returns The full day name (e.g., 'Monday')
 */
export const getDayName = (date) => {
    try {
        const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
        if (!isValid(dateObj)) {
            throw new Error(`Invalid date: ${date}`);
        }
        return format(dateObj, 'EEEE');
    }
    catch (error) {
        console.error('Error getting day name:', error);
        return '';
    }
};
/**
 * Gets the full month name of a date
 * @param date - The date
 * @returns The full month name (e.g., 'January')
 */
export const getMonthName = (date) => {
    try {
        const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
        if (!isValid(dateObj)) {
            throw new Error(`Invalid date: ${date}`);
        }
        return format(dateObj, 'MMMM');
    }
    catch (error) {
        console.error('Error getting month name:', error);
        return '';
    }
};
/**
 * Checks if a date is today
 * @param date - The date to check
 * @returns boolean indicating if the date is today
 */
export const isTodayDate = (date) => {
    try {
        const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
        if (!isValid(dateObj)) {
            throw new Error(`Invalid date: ${date}`);
        }
        return isToday(dateObj);
    }
    catch (error) {
        console.error('Error checking if date is today:', error);
        return false;
    }
};
/**
 * Adds a specified number of days to a date
 * @param date - The base date
 * @param days - Number of days to add (can be negative)
 * @returns A new Date object with the days added
 */
export const addDaysToDate = (date, days) => {
    try {
        const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
        if (!isValid(dateObj)) {
            throw new Error(`Invalid date: ${date}`);
        }
        return addDays(dateObj, days);
    }
    catch (error) {
        console.error('Error adding days to date:', error);
        // Return a fallback date (today + days) if there's an error
        return addDays(new Date(), days);
    }
};
/**
 * Adds a specified number of months to a date
 * @param date - The base date
 * @param months - Number of months to add (can be negative)
 * @returns A new Date object with the months added
 */
export const addMonthsToDate = (date, months) => {
    try {
        const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
        if (!isValid(dateObj)) {
            throw new Error(`Invalid date: ${date}`);
        }
        return addMonths(dateObj, months);
    }
    catch (error) {
        console.error('Error adding months to date:', error);
        // Return a fallback date (today + months) if there's an error
        return addMonths(new Date(), months);
    }
};
