// Mock data for the dashboard
const generateMockData = () => {
    const today = new Date();
    const lastMonth = new Date(today);
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    // Generate revenue data for the last 30 days
    const revenueData = [];
    for (let i = 30; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        revenueData.push({
            date: date.toISOString().split('T')[0],
            revenue: Math.floor(Math.random() * 5000) + 1000,
            bookings: Math.floor(Math.random() * 15) + 5,
        });
    }
    // Generate recent bookings
    const recentBookings = [];
    const statuses = ['confirmed', 'pending', 'cancelled', 'completed'];
    const experiences = [
        'Sunset Sail',
        'Full Day Charter',
        'Whale Watching',
        'Private Charter',
        'Lunch Cruise',
    ];
    for (let i = 0; i < 5; i++) {
        const firstName = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'][Math.floor(Math.random() * 8)];
        const lastName = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'][Math.floor(Math.random() * 8)];
        const name = `${firstName} ${lastName}`;
        const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`;
        const bookingDate = new Date();
        bookingDate.setDate(bookingDate.getDate() - Math.floor(Math.random() * 30));
        recentBookings.push({
            id: `B${Math.floor(1000 + Math.random() * 9000)}`,
            customer: {
                name,
                email,
                avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}`,
            },
            experience: experiences[Math.floor(Math.random() * experiences.length)],
            date: bookingDate.toISOString().split('T')[0],
            time: `${Math.floor(9 + Math.random() * 8)}:${Math.random() > 0.5 ? '00' : '30'}`,
            guests: Math.floor(Math.random() * 5) + 1,
            status: statuses[Math.floor(Math.random() * statuses.length)],
            amount: Math.floor(50 + Math.random() * 500),
        });
    }
    // Calculate totals and trends
    const totalBookings = revenueData.reduce((sum, day) => sum + day.bookings, 0);
    const totalRevenue = revenueData.reduce((sum, day) => sum + day.revenue, 0);
    const avgBookingValue = totalRevenue / totalBookings;
    // Calculate trends (simple random for demo)
    const bookingTrend = Math.floor(Math.random() * 20) - 5; // -5% to +15%
    const revenueTrend = Math.floor(Math.random() * 25) - 5; // -5% to +20%
    const avgBookingValueTrend = Math.floor(Math.random() * 15) - 5; // -5% to +10%
    return {
        totalBookings,
        revenue: totalRevenue,
        avgBookingValue,
        upcomingBookings: Math.floor(Math.random() * 10) + 5,
        bookingTrend,
        revenueTrend,
        avgBookingValueTrend,
        revenueData,
        recentBookings,
    };
};
// Simulate API call with delay
export const getDashboardData = async () => {
    try {
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 800));
        // In a real app, this would be an actual API call:
        // const response = await fetch('/api/dashboard');
        // if (!response.ok) {
        //   throw new Error(`HTTP error! status: ${response.status}`);
        // }
        // return await response.json() as DashboardData;
        return generateMockData();
    }
    catch (error) {
        console.error('Error fetching dashboard data:', error);
        throw new Error('Failed to load dashboard data');
    }
};
// Mock functions for other dashboard-related API calls
export const getRecentBookings = async () => {
    try {
        await new Promise(resolve => setTimeout(resolve, 500));
        return generateMockData().recentBookings;
    }
    catch (error) {
        console.error('Error fetching recent bookings:', error);
        throw new Error('Failed to load recent bookings');
    }
};
export const getRevenueData = async (_period = 'month') => {
    try {
        await new Promise(resolve => setTimeout(resolve, 500));
        const data = generateMockData().revenueData;
        // The period parameter is intentionally not used in this mock implementation
        // In a real app, you would filter the data based on the period
        // For example: filter data based on the selected time period (week/month/year)
        // This is a simplified example that returns all data regardless of period
        return data;
    }
    catch (error) {
        console.error('Error fetching revenue data:', error);
        throw new Error('Failed to load revenue data');
    }
};
