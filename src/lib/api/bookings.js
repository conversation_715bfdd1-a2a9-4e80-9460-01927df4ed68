import { subDays, addDays } from 'date-fns';
// Helper to generate mock bookings
const generateMockBookings = (count = 50) => {
    const statuses = ['confirmed', 'pending', 'cancelled', 'completed'];
    const experiences = [
        'Sunset Sail',
        'Full Day Charter',
        'Whale Watching',
        'Private Charter',
        'Lunch Cruise',
    ];
    const firstNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
    const lastNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
    return Array.from({ length: count }, (_, i) => {
        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
        const name = `${firstName} ${lastName}`;
        const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`;
        // Generate random dates within the last 90 days and next 30 days
        const randomDaysAgo = Math.floor(Math.random() * 120) - 30; // -30 to +90 days
        const bookingDate = new Date();
        bookingDate.setDate(bookingDate.getDate() + randomDaysAgo);
        const createdDate = new Date(bookingDate);
        createdDate.setDate(createdDate.getDate() - Math.floor(Math.random() * 7)); // 0-7 days before booking
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        const experience = experiences[Math.floor(Math.random() * experiences.length)];
        const guests = Math.floor(Math.random() * 5) + 1; // 1-6 guests
        const basePrice = Math.floor(Math.random() * 300) + 50; // $50-$350
        const amount = basePrice * guests;
        return {
            id: `B${1000 + i}`,
            customer: {
                id: `C${1000 + i}`,
                name,
                email,
                phone: `+1${Math.floor(2000000000 + Math.random() * 8000000000)}`,
                avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}`,
            },
            experience,
            date: bookingDate.toISOString().split('T')[0],
            time: `${Math.floor(9 + Math.random() * 8)}:${Math.random() > 0.5 ? '00' : '30'}`,
            guests,
            status,
            amount,
            notes: Math.random() > 0.7 ? 'Special requests: ' + [
                'Vegetarian meal',
                'Wheelchair accessible',
                'Anniversary celebration',
                'Allergies: Shellfish',
                'Late check-in',
            ][Math.floor(Math.random() * 5)] : undefined,
            createdAt: createdDate.toISOString(),
            updatedAt: new Date(createdDate.getTime() + Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)).toISOString(),
        };
    });
};
// In-memory storage for mock data
let mockBookings = generateMockBookings(50);
// Simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
export const getBookings = async (filters) => {
    if (!filters) {
        try {
            await delay(500);
            return [...mockBookings];
        }
        catch (error) {
            console.error('Error fetching all bookings:', error);
            throw new Error('Failed to fetch bookings');
        }
    }
    try {
        await delay(500); // Simulate network delay
        let result = [...mockBookings];
        // Apply filters
        if (filters) {
            if (filters.status && filters.status !== 'all') {
                result = result.filter(booking => booking.status === filters.status);
            }
            if (filters.dateFrom) {
                const dateFrom = new Date(filters.dateFrom);
                result = result.filter(booking => new Date(booking.date) >= dateFrom);
            }
            if (filters.dateTo) {
                const dateTo = new Date(filters.dateTo);
                dateTo.setDate(dateTo.getDate() + 1); // Include the end date
                result = result.filter(booking => new Date(booking.date) < dateTo);
            }
            if (filters.searchQuery) {
                const query = filters.searchQuery.toLowerCase();
                result = result.filter(booking => booking.customer.name.toLowerCase().includes(query) ||
                    booking.experience.toLowerCase().includes(query) ||
                    booking.id.toLowerCase().includes(query) ||
                    booking.customer.email.toLowerCase().includes(query));
            }
            // Apply sorting
            if (filters.sortBy) {
                const sortOrder = filters.sortOrder || 'asc';
                result.sort((a, b) => {
                    let comparison = 0;
                    switch (filters.sortBy) {
                        case 'date':
                            comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
                            break;
                        case 'createdAt':
                            comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
                            break;
                        case 'amount':
                            comparison = a.amount - b.amount;
                            break;
                        default:
                            comparison = 0;
                    }
                    return sortOrder === 'asc' ? comparison : -comparison;
                });
            }
            // Apply pagination
            if (filters.page && filters.pageSize) {
                const start = (filters.page - 1) * filters.pageSize;
                const end = start + filters.pageSize;
                result = result.slice(start, end);
            }
        }
        return result;
    }
    catch (error) {
        console.error('Error fetching filtered bookings:', error);
        throw new Error('Failed to fetch filtered bookings');
    }
};
export const getBookingById = async (id) => {
    try {
        await delay(300);
        const booking = mockBookings.find(booking => booking.id === id);
        if (!booking) {
            throw new Error(`Booking with ID ${id} not found`);
        }
        return booking;
    }
    catch (error) {
        console.error(`Error fetching booking ${id}:`, error);
        throw new Error(`Failed to fetch booking: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};
export const createBooking = async (bookingData) => {
    try {
        // Validate required fields
        if (!bookingData.customer || !bookingData.experience || !bookingData.date) {
            throw new Error('Missing required booking information');
        }
        await delay(500);
        const newBooking = {
            ...bookingData,
            id: `B${1000 + mockBookings.length}`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
        };
        mockBookings = [newBooking, ...mockBookings];
        return newBooking;
    }
    catch (error) {
        console.error('Error creating booking:', error);
        throw new Error(`Failed to create booking: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};
export const updateBooking = async (id, updates) => {
    try {
        await delay(500);
        const index = mockBookings.findIndex(booking => booking.id === id);
        if (index === -1) {
            throw new Error(`Booking with ID ${id} not found`);
        }
        // Prevent updating certain fields
        const { id: _, createdAt, ...safeUpdates } = updates;
        const updatedBooking = {
            ...mockBookings[index],
            ...safeUpdates,
            updatedAt: new Date().toISOString(),
        };
        mockBookings[index] = updatedBooking;
        return updatedBooking;
    }
    catch (error) {
        console.error(`Error updating booking ${id}:`, error);
        throw new Error(`Failed to update booking: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};
export const deleteBooking = async (id) => {
    try {
        await delay(300);
        const initialLength = mockBookings.length;
        mockBookings = mockBookings.filter(booking => booking.id !== id);
        if (mockBookings.length === initialLength) {
            throw new Error(`Booking with ID ${id} not found`);
        }
    }
    catch (error) {
        console.error(`Error deleting booking ${id}:`, error);
        throw new Error(`Failed to delete booking: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};
export const getBookingsStats = async () => {
    try {
        await delay(400);
        const today = new Date();
        const last30Days = subDays(today, 30);
        const next30Days = addDays(today, 30);
        const recentBookings = mockBookings.filter(booking => new Date(booking.date) >= last30Days && new Date(booking.date) <= next30Days);
        const stats = {
            totalBookings: mockBookings.length,
            upcomingBookings: mockBookings.filter(booking => new Date(booking.date) >= today && booking.status !== 'cancelled').length,
            pendingBookings: mockBookings.filter(booking => booking.status === 'pending').length,
            totalRevenue: mockBookings
                .filter(booking => booking.status !== 'cancelled')
                .reduce((sum, booking) => sum + booking.amount, 0),
            recentBookings: recentBookings.slice(0, 5), // Return only first 5 recent bookings
        };
        return stats;
    }
    catch (error) {
        console.error('Error fetching booking stats:', error);
        throw new Error('Failed to fetch booking statistics');
    }
};
