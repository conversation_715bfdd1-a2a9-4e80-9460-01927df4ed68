import { format, subDays, subMonths } from 'date-fns';
import { startOfMonth, endOfMonth } from 'date-fns/fp';
// Helper function to generate mock customer data
const generateMockCustomers = (count = 50) => {
    const firstNames = [
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
    ];
    const lastNames = [
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
    ];
    const domains = ['gmail.com', 'yahoo.com', 'outlook.com', 'icloud.com', 'hotmail.com'];
    const countries = ['New Zealand', 'Australia', 'United States', 'United Kingdom', 'Canada'];
    const cities = {
        'New Zealand': ['Auckland', 'Wellington', 'Christchurch', 'Queenstown', 'Rotorua'],
        'Australia': ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide'],
        'United States': ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'],
        'United Kingdom': ['London', 'Manchester', 'Birmingham', 'Glasgow', 'Liverpool'],
        'Canada': ['Toronto', 'Vancouver', 'Montreal', 'Calgary', 'Ottawa']
    };
    const tags = [
        'VIP', 'Repeat Customer', 'First Time', 'Family', 'Solo Traveler',
        'Honeymoon', 'Anniversary', 'Birthday', 'Corporate', 'Group'
    ];
    return Array.from({ length: count }, (_, i) => {
        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
        const country = countries[Math.floor(Math.random() * countries.length)];
        const city = cities[country][Math.floor(Math.random() * cities[country].length)];
        const domain = domains[Math.floor(Math.random() * domains.length)];
        const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${domain}`.replace(/\s+/g, '');
        const joinDate = new Date();
        joinDate.setDate(joinDate.getDate() - Math.floor(Math.random() * 365 * 3)); // Up to 3 years ago
        const totalBookings = Math.floor(Math.random() * 20) + 1; // 1-20 bookings
        const totalSpent = Math.floor(Math.random() * 10000) + 100; // $100-$10,100
        // Generate some random tags (0-3 per customer)
        const customerTags = [];
        const numTags = Math.floor(Math.random() * 4); // 0-3 tags
        const availableTags = [...tags];
        for (let j = 0; j < numTags && availableTags.length > 0; j++) {
            const randomIndex = Math.floor(Math.random() * availableTags.length);
            customerTags.push(availableTags.splice(randomIndex, 1)[0]);
        }
        return {
            id: `C${1000 + i}`,
            firstName,
            lastName,
            email,
            phone: `+${Math.floor(Math.random() * 90) + 10} ${Math.floor(Math.random() * 900000000) + 100000000}`,
            avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(firstName + ' ' + lastName)}&background=random`,
            joinDate: joinDate.toISOString(),
            lastActive: format(subDays(joinDate, Math.floor(Math.random() * 30)), 'yyyy-MM-dd'),
            totalBookings,
            totalSpent,
            tags: customerTags.length > 0 ? customerTags : undefined,
            address: {
                street: `${Math.floor(Math.random() * 1000) + 1} ${['Main', 'Oak', 'Pine', 'Maple', 'Cedar', 'Elm', 'Lake', 'Hill'][Math.floor(Math.random() * 8)]} St`,
                city,
                state: '',
                postalCode: Math.floor(Math.random() * 90000) + 10000 + '',
                country,
            },
            preferences: {
                communication: {
                    email: Math.random() > 0.2, // 80% chance of true
                    sms: Math.random() > 0.7, // 30% chance of true
                },
                dietaryRestrictions: Math.random() > 0.7 ? [
                    ...(Math.random() > 0.5 ? ['Vegetarian'] : []),
                    ...(Math.random() > 0.5 ? ['Vegan'] : []),
                    ...(Math.random() > 0.5 ? ['Gluten-Free'] : []),
                    ...(Math.random() > 0.5 ? ['Dairy-Free'] : []),
                    ...(Math.random() > 0.5 ? ['Nut Allergy'] : []),
                ].filter(Boolean) : [],
            },
        };
    });
};
// In-memory storage for mock data
let mockCustomers = generateMockCustomers(50);
// Simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
// Get customers with filtering and pagination
export const getCustomers = async (filters) => {
    try {
        await delay(500); // Simulate network delay
        let result = [...mockCustomers];
        const page = filters?.page || 1;
        const pageSize = filters?.pageSize || 10;
        // Apply filters
        if (filters) {
            // Search by name or email
            if (filters.searchQuery) {
                const query = filters.searchQuery.toLowerCase();
                result = result.filter(customer => customer.firstName.toLowerCase().includes(query) ||
                    customer.lastName.toLowerCase().includes(query) ||
                    customer.email.toLowerCase().includes(query) ||
                    customer.phone?.toLowerCase().includes(query) ||
                    customer.id.toLowerCase().includes(query));
            }
            // Filter by tags
            if (filters.tags && filters.tags.length > 0) {
                result = result.filter(customer => customer.tags && filters.tags?.some(tag => customer.tags?.includes(tag)));
            }
            // Apply sorting
            if (filters.sortBy) {
                const sortOrder = filters.sortOrder || 'asc';
                result.sort((a, b) => {
                    let comparison = 0;
                    switch (filters.sortBy) {
                        case 'name':
                            comparison = `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`);
                            break;
                        case 'joinDate':
                            comparison = new Date(a.joinDate).getTime() - new Date(b.joinDate).getTime();
                            break;
                        case 'totalBookings':
                            comparison = a.totalBookings - b.totalBookings;
                            break;
                        case 'totalSpent':
                            comparison = a.totalSpent - b.totalSpent;
                            break;
                        default:
                            comparison = 0;
                    }
                    return sortOrder === 'asc' ? comparison : -comparison;
                });
            }
        }
        // Apply pagination
        const total = result.length;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const paginatedResult = result.slice(start, end);
        return {
            data: paginatedResult,
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize),
        };
    }
    catch (error) {
        console.error('Error fetching customers:', error);
        throw new Error('Failed to fetch customers');
    }
};
// Get a single customer by ID
export const getCustomerById = async (id) => {
    try {
        await delay(300);
        const customer = mockCustomers.find(customer => customer.id === id);
        if (!customer) {
            throw new Error(`Customer with ID ${id} not found`);
        }
        return customer;
    }
    catch (error) {
        console.error(`Error fetching customer ${id}:`, error);
        throw new Error(`Failed to fetch customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};
// Create a new customer
export const createCustomer = async (customerData) => {
    try {
        // Validate required fields
        if (!customerData.firstName || !customerData.lastName || !customerData.email) {
            throw new Error('Missing required customer information');
        }
        await delay(500);
        const newCustomer = {
            ...customerData,
            id: `C${1000 + mockCustomers.length}`,
            joinDate: new Date().toISOString(),
            lastActive: new Date().toISOString(),
            totalBookings: 0,
            totalSpent: 0,
        };
        mockCustomers = [newCustomer, ...mockCustomers];
        return newCustomer;
    }
    catch (error) {
        console.error('Error creating customer:', error);
        throw new Error(`Failed to create customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};
// Update an existing customer
export const updateCustomer = async (id, updates) => {
    try {
        await delay(500);
        const index = mockCustomers.findIndex(customer => customer.id === id);
        if (index === -1) {
            throw new Error(`Customer with ID ${id} not found`);
        }
        // Prevent updating certain fields
        const { id: _, joinDate, totalBookings, totalSpent, ...safeUpdates } = updates;
        const updatedCustomer = {
            ...mockCustomers[index],
            ...safeUpdates,
            id, // Ensure ID doesn't get changed
            lastActive: new Date().toISOString(), // Update last active timestamp
        };
        mockCustomers[index] = updatedCustomer;
        return updatedCustomer;
    }
    catch (error) {
        console.error(`Error updating customer ${id}:`, error);
        throw new Error(`Failed to update customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};
// Delete a customer
export const deleteCustomer = async (id) => {
    try {
        await delay(300);
        const initialLength = mockCustomers.length;
        mockCustomers = mockCustomers.filter(customer => customer.id !== id);
        if (mockCustomers.length === initialLength) {
            throw new Error(`Customer with ID ${id} not found`);
        }
    }
    catch (error) {
        console.error(`Error deleting customer ${id}:`, error);
        throw new Error(`Failed to delete customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};
// Get customer statistics
export const getCustomerStats = async () => {
    try {
        await delay(400);
        const now = new Date();
        const thirtyDaysAgo = subDays(now, 30);
        const ninetyDaysAgo = subDays(now, 90);
        // oneYearAgo is not used in the current implementation
        // const oneYearAgo = subYears(now, 1);
        const allCustomers = mockCustomers;
        const newCustomers = allCustomers.filter(customer => new Date(customer.joinDate) >= thirtyDaysAgo);
        const activeCustomers = allCustomers.filter(customer => customer.lastActive && new Date(customer.lastActive) >= ninetyDaysAgo);
        const highValueCustomers = allCustomers
            .filter(customer => customer.totalSpent >= 1000)
            .sort((a, b) => b.totalSpent - a.totalSpent)
            .slice(0, 5);
        // Calculate customer growth
        const monthlyGrowth = [];
        for (let i = 11; i >= 0; i--) {
            const monthStart = startOfMonth(subMonths(now, i));
            const monthEnd = endOfMonth(monthStart);
            const count = allCustomers.filter(customer => {
                const joinDate = new Date(customer.joinDate);
                return joinDate >= monthStart && joinDate <= monthEnd;
            }).length;
            monthlyGrowth.push({
                month: format(monthStart, 'MMM yyyy'),
                count,
            });
        }
        return {
            totalCustomers: allCustomers.length,
            newCustomers: newCustomers.length,
            activeCustomers: activeCustomers.length,
            highValueCustomers,
            monthlyGrowth,
        };
    }
    catch (error) {
        console.error('Error fetching customer statistics:', error);
        throw new Error('Failed to fetch customer statistics');
    }
};
