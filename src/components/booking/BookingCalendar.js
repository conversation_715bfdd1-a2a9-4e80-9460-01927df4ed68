import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format, isWeekend, isBefore, addMonths } from 'date-fns';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
export function BookingCalendar({ onDateSelect, selectedDate = null, unavailableDates = [], className = '' }) {
    const { t } = useTranslation();
    const [currentMonth, setCurrentMonth] = useState(new Date());
    const [selectedDateState, setSelectedDate] = useState(selectedDate);
    const weekdays = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const nextMonth = () => {
        setCurrentMonth(addMonths(currentMonth, 1));
    };
    const prevMonth = () => {
        setCurrentMonth(addMonths(currentMonth, -1));
    };
    const isDateUnavailable = (date) => {
        return unavailableDates.some(unavailableDate => format(unavailableDate, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd'));
    };
    const isDateSelectable = (date) => {
        return !isBefore(date, today) &&
            !isWeekend(date) &&
            !isDateUnavailable(date);
    };
    const handleDateClick = (date) => {
        if (isDateSelectable(date)) {
            setSelectedDate(date);
            onDateSelect(date);
        }
    };
    // Generate days for the current month view
    const renderDays = () => {
        const year = currentMonth.getFullYear();
        const month = currentMonth.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const startDay = firstDay.getDay();
        const daysInMonth = lastDay.getDate();
        const daysInLastMonth = new Date(year, month, 0).getDate();
        const days = [];
        // Previous month's days
        for (let i = startDay - 1; i >= 0; i--) {
            const day = daysInLastMonth - i;
            const date = new Date(year, month - 1, day);
            days.push({
                date,
                day,
                isCurrentMonth: false,
                isSelectable: false,
                isSelected: false,
                isUnavailable: false
            });
        }
        // Current month's days
        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(year, month, day);
            const selectable = isDateSelectable(date);
            const selected = selectedDateState ?
                format(selectedDateState, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd') : false;
            const unavailable = isDateUnavailable(date);
            days.push({
                date,
                day,
                isCurrentMonth: true,
                isSelectable: selectable,
                isSelected: selected,
                isUnavailable: unavailable
            });
        }
        // Next month's days
        const remainingDays = 42 - days.length; // 6 rows x 7 days
        for (let i = 1; i <= remainingDays; i++) {
            const date = new Date(year, month + 1, i);
            days.push({
                date,
                day: i,
                isCurrentMonth: false,
                isSelectable: false,
                isSelected: false,
                isUnavailable: false
            });
        }
        return days;
    };
    return (_jsxs("div", { className: `bg-white rounded-lg shadow-md p-4 ${className}`, children: [_jsxs("div", { className: "flex justify-between items-center mb-4", children: [_jsx(Button, { variant: "ghost", size: "icon", onClick: prevMonth, "aria-label": "Previous month", children: _jsx(ChevronLeft, { className: "h-4 w-4" }) }), _jsx("h3", { className: "text-lg font-semibold", children: format(currentMonth, 'MMMM yyyy') }), _jsx(Button, { variant: "ghost", size: "icon", onClick: nextMonth, "aria-label": "Next month", children: _jsx(ChevronRight, { className: "h-4 w-4" }) })] }), _jsx("div", { className: "grid grid-cols-7 gap-1 mb-2", children: weekdays.map(day => (_jsx("div", { className: "text-center text-sm font-medium text-gray-500", children: day }, day))) }), _jsx("div", { className: "grid grid-cols-7 gap-1", children: renderDays().map((dayData, index) => {
                    const { date, day, isCurrentMonth, isSelectable, isSelected, isUnavailable } = dayData;
                    const isToday = format(today, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd');
                    return (_jsx("button", { className: `
                h-10 w-10 rounded-full flex items-center justify-center text-sm
                ${!isCurrentMonth ? 'text-gray-300' : ''}
                ${isToday ? 'font-bold border border-blue-500' : ''}
                ${isSelected ? 'bg-blue-500 text-white' : ''}
                ${isUnavailable ? 'line-through text-gray-300 cursor-not-allowed' : ''}
                ${isSelectable && !isSelected ? 'hover:bg-blue-100 cursor-pointer' : 'cursor-default'}
                transition-colors duration-200
              `, onClick: () => handleDateClick(date), disabled: !isSelectable, "aria-label": `Select ${format(date, 'MMMM d, yyyy')}${isUnavailable ? ' (Unavailable)' : ''}`, children: day }, `${day}-${index}`));
                }) }), _jsxs("div", { className: "mt-4 flex items-center justify-between text-sm text-gray-500", children: [_jsxs("div", { className: "flex items-center", children: [_jsx("div", { className: "h-3 w-3 rounded-full bg-blue-500 mr-1" }), _jsx("span", { children: t('booking.selected', 'Selected') })] }), _jsxs("div", { className: "flex items-center", children: [_jsx("div", { className: "h-3 w-3 rounded-full bg-red-100 mr-1" }), _jsx("span", { children: t('booking.unavailable', 'Unavailable') })] })] })] }));
}
export default BookingCalendar;
