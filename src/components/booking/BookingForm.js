import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { BookingCalendar } from './BookingCalendar';
export function BookingForm({ 
// Parameters are kept for future use and consistency with the API
// but are not currently used in the component
experienceId: _experienceId, experienceTitle: _experienceTitle, onBookingSubmit, className = '', initialData = {}, unavailableTimes = [], maxGuests = 6 }) {
    const { t } = useTranslation();
    const [formData, setFormData] = useState({
        date: initialData.date || null,
        time: initialData.time || '09:00',
        guests: initialData.guests || 2,
        name: initialData.name || '',
        email: initialData.email || '',
        phone: initialData.phone || '',
        specialRequests: initialData.specialRequests || '',
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    // Available time slots
    const availableTimes = [
        '09:00', '10:00', '11:00', '12:00',
        '13:00', '14:00', '15:00', '16:00'
    ].filter(time => !unavailableTimes.includes(time));
    // Mock function to get unavailable dates
    const getUnavailableDates = () => {
        const unavailable = [];
        // Mark some random dates as unavailable for demo purposes
        for (let i = 1; i <= 10; i++) {
            const date = new Date();
            date.setDate(date.getDate() + Math.floor(Math.random() * 30) + 1);
            unavailable.push(new Date(date.toDateString()));
        }
        return unavailable;
    };
    const handleDateSelect = (date) => {
        setFormData(prev => ({ ...prev, date }));
    };
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: name === 'guests' ? parseInt(value, 10) : value
        }));
    };
    const handleTimeChange = (value) => {
        setFormData(prev => ({ ...prev, time: value }));
    };
    const handleSubmit = (e) => {
        e.preventDefault();
        if (!formData.date)
            return;
        setIsSubmitting(true);
        // In a real app, you would validate the form data here
        // and possibly show validation errors
        // Simulate API call
        setTimeout(() => {
            onBookingSubmit(formData);
            setIsSubmitting(false);
        }, 1000);
    };
    return (_jsxs("div", { className: `grid grid-cols-1 md:grid-cols-2 gap-8 ${className}`, children: [_jsxs("div", { children: [_jsx("h3", { className: "text-xl font-semibold mb-4", children: t('booking.selectDate', 'Select a Date & Time') }), _jsx("div", { className: "mb-6", children: _jsx(BookingCalendar, { onDateSelect: handleDateSelect, selectedDate: formData.date, unavailableDates: getUnavailableDates() }) }), _jsxs("div", { className: "mb-6", children: [_jsx(Label, { htmlFor: "time", className: "block mb-2", children: t('booking.selectTime', 'Select Time') }), _jsxs(Select, { value: formData.time, onValueChange: handleTimeChange, children: [_jsx(SelectTrigger, { className: "w-full", children: _jsx(SelectValue, { placeholder: "Select a time" }) }), _jsx(SelectContent, { children: availableTimes.map(time => (_jsx(SelectItem, { value: time, children: time }, time))) })] })] }), _jsxs("div", { className: "mb-6", children: [_jsx(Label, { htmlFor: "guests", className: "block mb-2", children: t('booking.numberOfGuests', 'Number of Guests') }), _jsx(Input, { id: "guests", name: "guests", type: "number", min: "1", max: maxGuests, value: formData.guests, onChange: handleChange, className: "w-24" }), _jsx("p", { className: "text-sm text-gray-500 mt-1", children: t('booking.maxGuests', { count: maxGuests, defaultValue: 'Maximum {{count}} guests' }) })] })] }), _jsxs("div", { children: [_jsx("h3", { className: "text-xl font-semibold mb-4", children: t('booking.contactInfo', 'Your Information') }), _jsx("form", { onSubmit: handleSubmit, children: _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { children: [_jsxs(Label, { htmlFor: "name", children: [t('booking.fullName', 'Full Name'), " *"] }), _jsx(Input, { id: "name", name: "name", value: formData.name, onChange: handleChange, required: true })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "email", children: "Email *" }), _jsx(Input, { id: "email", name: "email", type: "email", value: formData.email, onChange: handleChange, required: true })] }), _jsxs("div", { children: [_jsxs(Label, { htmlFor: "phone", children: [t('booking.phoneNumber', 'Phone Number'), " *"] }), _jsx(Input, { id: "phone", name: "phone", type: "tel", value: formData.phone, onChange: handleChange, required: true })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "specialRequests", children: t('booking.specialRequests', 'Special Requests') }), _jsx(Textarea, { id: "specialRequests", name: "specialRequests", value: formData.specialRequests, onChange: handleChange, rows: 4, placeholder: t('booking.specialRequestsPlaceholder', 'Any special requirements or notes...') })] }), _jsxs("div", { className: "pt-2", children: [_jsx(Button, { type: "submit", className: "w-full", disabled: !formData.date || isSubmitting, children: isSubmitting ? (_jsx("span", { children: t('booking.booking', 'Booking...') })) : (_jsx("span", { children: t('booking.bookNow', 'Book Now') })) }), formData.date && (_jsx("p", { className: "text-sm text-gray-500 mt-2 text-center", children: t('booking.selectedDate', 'Selected: {{date}} at {{time}}', {
                                                date: format(formData.date, 'MMMM d, yyyy'),
                                                time: formData.time
                                            }) }))] })] }) })] })] }));
}
export default BookingForm;
