import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { BookingCalendar } from './BookingCalendar';

export interface BookingData {
  date: Date | null;
  time: string;
  guests: number;
  name: string;
  email: string;
  phone: string;
  specialRequests: string;
}

interface BookingFormProps {
  experienceId: number;
  experienceTitle: string;
  onBookingSubmit: (data: BookingData) => void;
  className?: string;
  initialData?: Partial<BookingData>;
  unavailableTimes?: string[];
  maxGuests?: number;
}

export function BookingForm({
  // Parameters are kept for future use and consistency with the API
  // but are not currently used in the component
  experienceId: _experienceId,
  experienceTitle: _experienceTitle,
  onBookingSubmit,
  className = '',
  initialData = {},
  unavailableTimes = [],
  maxGuests = 6
}: BookingFormProps) {
  const { t } = useTranslation();
  
  const [formData, setFormData] = useState<BookingData>({
    date: initialData.date || null,
    time: initialData.time || '09:00',
    guests: initialData.guests || 2,
    name: initialData.name || '',
    email: initialData.email || '',
    phone: initialData.phone || '',
    specialRequests: initialData.specialRequests || '',
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Available time slots
  const availableTimes = [
    '09:00', '10:00', '11:00', '12:00', 
    '13:00', '14:00', '15:00', '16:00'
  ].filter(time => !unavailableTimes.includes(time));
  
  // Mock function to get unavailable dates
  const getUnavailableDates = (): Date[] => {
    const unavailable: Date[] = [];
    // Mark some random dates as unavailable for demo purposes
    for (let i = 1; i <= 10; i++) {
      const date = new Date();
      date.setDate(date.getDate() + Math.floor(Math.random() * 30) + 1);
      unavailable.push(new Date(date.toDateString()));
    }
    return unavailable;
  };

  const handleDateSelect = (date: Date) => {
    setFormData(prev => ({ ...prev, date }));
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'guests' ? parseInt(value, 10) : value
    }));
  };

  const handleTimeChange = (value: string) => {
    setFormData(prev => ({ ...prev, time: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.date) return;
    
    setIsSubmitting(true);
    
    // In a real app, you would validate the form data here
    // and possibly show validation errors
    
    // Simulate API call
    setTimeout(() => {
      onBookingSubmit(formData);
      setIsSubmitting(false);
    }, 1000);
  };

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 gap-8 ${className}`}>
      <div>
        <h3 className="text-xl font-semibold mb-4">
          {t('booking.selectDate', 'Select a Date & Time')}
        </h3>
        <div className="mb-6">
          <BookingCalendar
            onDateSelect={handleDateSelect}
            selectedDate={formData.date}
            unavailableDates={getUnavailableDates()}
          />
        </div>
        
        <div className="mb-6">
          <Label htmlFor="time" className="block mb-2">
            {t('booking.selectTime', 'Select Time')}
          </Label>
          <Select value={formData.time} onValueChange={handleTimeChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a time" />
            </SelectTrigger>
            <SelectContent>
              {availableTimes.map(time => (
                <SelectItem key={time} value={time}>
                  {time}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="mb-6">
          <Label htmlFor="guests" className="block mb-2">
            {t('booking.numberOfGuests', 'Number of Guests')}
          </Label>
          <Input
            id="guests"
            name="guests"
            type="number"
            min="1"
            max={maxGuests}
            value={formData.guests}
            onChange={handleChange}
            className="w-24"
          />
          <p className="text-sm text-gray-500 mt-1">
            {t('booking.maxGuests', { count: maxGuests, defaultValue: 'Maximum {{count}} guests' })}
          </p>
        </div>
      </div>
      
      <div>
        <h3 className="text-xl font-semibold mb-4">
          {t('booking.contactInfo', 'Your Information')}
        </h3>
        
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">{t('booking.fullName', 'Full Name')} *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="phone">{t('booking.phoneNumber', 'Phone Number')} *</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleChange}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="specialRequests">
                {t('booking.specialRequests', 'Special Requests')}
              </Label>
              <Textarea
                id="specialRequests"
                name="specialRequests"
                value={formData.specialRequests}
                onChange={handleChange}
                rows={4}
                placeholder={t('booking.specialRequestsPlaceholder', 'Any special requirements or notes...')}
              />
            </div>
            
            <div className="pt-2">
              <Button 
                type="submit" 
                className="w-full"
                disabled={!formData.date || isSubmitting}
              >
                {isSubmitting ? (
                  <span>{t('booking.booking', 'Booking...')}</span>
                ) : (
                  <span>{t('booking.bookNow', 'Book Now')}</span>
                )}
              </Button>
              
              {formData.date && (
                <p className="text-sm text-gray-500 mt-2 text-center">
                  {t('booking.selectedDate', 'Selected: {{date}} at {{time}}', {
                    date: format(formData.date, 'MMMM d, yyyy'),
                    time: formData.time
                  })}
                </p>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

export default BookingForm;
