import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { BookingForm, BookingData } from './BookingForm';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';

interface BookingModalProps {
  isOpen: boolean;
  onClose: () => void;
  experienceId: number;
  experienceTitle: string;
  maxGuests?: number;
  onBookingSubmit: (data: BookingData) => void;
}

export function BookingModal({
  isOpen,
  onClose,
  experienceId,
  experienceTitle,
  maxGuests = 6,
  onBookingSubmit,
}: BookingModalProps) {
  const { t } = useTranslation();
  const [isSuccess, setIsSuccess] = useState(false);

  const handleBookingSubmit = async (data: BookingData) => {
    try {
      await onBookingSubmit(data);
      setIsSuccess(true);
      // Close the modal after 2 seconds
      setTimeout(() => {
        onClose();
        // Reset success state after animation completes
        setTimeout(() => setIsSuccess(false), 300);
      }, 2000);
    } catch (error) {
      console.error('Booking failed:', error);
      // Error handling would go here
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">
            {t('booking.bookExperience', { name: experienceTitle })}
          </DialogTitle>
          <DialogDescription>
            {t('booking.fillDetails', 'Please fill in your details to book this experience.')}
          </DialogDescription>
        </DialogHeader>
        
        <div className="relative">
          {isSuccess ? (
            <div className="py-8 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="mt-3 text-lg font-medium text-gray-900">
                {t('booking.bookingConfirmed', 'Booking Confirmed!')}
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                {t('booking.confirmationMessage', 'Your booking has been confirmed. We\'ve sent you a confirmation email with all the details.')}
              </p>
              <div className="mt-6">
                <Button onClick={onClose}>
                  {t('common.close', 'Close')}
                </Button>
              </div>
            </div>
          ) : (
            <BookingForm
              experienceId={experienceId}
              experienceTitle={experienceTitle}
              onBookingSubmit={handleBookingSubmit}
              maxGuests={maxGuests}
            />
          )}
          
          <Button
            variant="ghost"
            size="icon"
            className="absolute -top-10 right-0 text-gray-500 hover:text-gray-700"
            onClick={onClose}
            aria-label={t('common.close', 'Close')}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default BookingModal;
