import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { BookingForm } from './BookingForm';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
export function BookingModal({ isOpen, onClose, experienceId, experienceTitle, maxGuests = 6, onBookingSubmit, }) {
    const { t } = useTranslation();
    const [isSuccess, setIsSuccess] = useState(false);
    const handleBookingSubmit = async (data) => {
        try {
            await onBookingSubmit(data);
            setIsSuccess(true);
            // Close the modal after 2 seconds
            setTimeout(() => {
                onClose();
                // Reset success state after animation completes
                setTimeout(() => setIsSuccess(false), 300);
            }, 2000);
        }
        catch (error) {
            console.error('Booking failed:', error);
            // Error handling would go here
        }
    };
    return (_jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: _jsxs(DialogContent, { className: "max-w-4xl max-h-[90vh] overflow-y-auto", children: [_jsxs(DialogHeader, { children: [_jsx(DialogTitle, { className: "text-2xl font-bold", children: t('booking.bookExperience', { name: experienceTitle }) }), _jsx(DialogDescription, { children: t('booking.fillDetails', 'Please fill in your details to book this experience.') })] }), _jsxs("div", { className: "relative", children: [isSuccess ? (_jsxs("div", { className: "py-8 text-center", children: [_jsx("div", { className: "mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100", children: _jsx("svg", { className: "h-6 w-6 text-green-600", fill: "none", viewBox: "0 0 24 24", stroke: "currentColor", children: _jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M5 13l4 4L19 7" }) }) }), _jsx("h3", { className: "mt-3 text-lg font-medium text-gray-900", children: t('booking.bookingConfirmed', 'Booking Confirmed!') }), _jsx("p", { className: "mt-2 text-sm text-gray-500", children: t('booking.confirmationMessage', 'Your booking has been confirmed. We\'ve sent you a confirmation email with all the details.') }), _jsx("div", { className: "mt-6", children: _jsx(Button, { onClick: onClose, children: t('common.close', 'Close') }) })] })) : (_jsx(BookingForm, { experienceId: experienceId, experienceTitle: experienceTitle, onBookingSubmit: handleBookingSubmit, maxGuests: maxGuests })), _jsx(Button, { variant: "ghost", size: "icon", className: "absolute -top-10 right-0 text-gray-500 hover:text-gray-700", onClick: onClose, "aria-label": t('common.close', 'Close'), children: _jsx(X, { className: "h-5 w-5" }) })] })] }) }));
}
export default BookingModal;
