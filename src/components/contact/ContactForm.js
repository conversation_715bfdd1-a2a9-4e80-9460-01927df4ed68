import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Loader2, CheckCircle, AlertCircle, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import ReCAPTCHA from 'react-google-recaptcha';
// Form validation schema
const contactFormSchema = z.object({
    name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
    email: z.string().email({ message: 'Please enter a valid email address' }),
    phone: z.string().optional(),
    subject: z.string().min(5, { message: 'Subject must be at least 5 characters' }),
    message: z.string().min(10, { message: 'Message must be at least 10 characters' }),
    privacyPolicy: z.boolean().refine(val => val === true, {
        message: 'You must accept the privacy policy',
    }),
    recaptchaToken: z.string().min(1, { message: 'Please complete the reCAPTCHA' }),
});
export const ContactForm = ({ className = '', recaptchaSiteKey, onSubmit, }) => {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState(null);
    const { register, handleSubmit, setValue, trigger, reset, formState: { errors, isDirty, isValid }, } = useForm({
        resolver: zodResolver(contactFormSchema),
        mode: 'onChange',
        defaultValues: {
            name: '',
            email: '',
            phone: '',
            subject: '',
            message: '',
            privacyPolicy: false,
            recaptchaToken: '',
        },
    });
    // Handle reCAPTCHA verification
    const handleRecaptchaChange = (token) => {
        if (token) {
            setValue('recaptchaToken', token, { shouldValidate: true });
        }
        else {
            setValue('recaptchaToken', '', { shouldValidate: true });
        }
    };
    // Handle form submission
    const handleFormSubmit = async (data) => {
        if (isSubmitting)
            return;
        try {
            setIsSubmitting(true);
            setSubmitStatus(null);
            // Extract recaptchaToken from the form data
            const { recaptchaToken, ...formData } = data;
            const success = await onSubmit(formData);
            if (success) {
                setSubmitStatus({
                    success: true,
                    message: 'Your message has been sent successfully! We\'ll get back to you soon.',
                });
                reset();
                // Reset reCAPTCHA
                if (window.grecaptcha) {
                    window.grecaptcha.reset();
                }
            }
            else {
                throw new Error('Failed to send message');
            }
        }
        catch (error) {
            console.error('Error submitting contact form:', error);
            setSubmitStatus({
                success: false,
                message: 'Failed to send message. Please try again later or contact us <NAME_EMAIL>',
            });
        }
        finally {
            setIsSubmitting(false);
        }
    };
    // Render form field with error message
    const renderFormField = (id, label, type = 'text', isTextarea = false) => {
        const fieldId = `contact-${id}`;
        const error = errors[id];
        const InputComponent = isTextarea ? Textarea : Input;
        return (_jsxs("div", { className: "space-y-2", children: [_jsxs(Label, { htmlFor: fieldId, className: error ? 'text-destructive' : '', children: [label, !['phone', 'privacyPolicy', 'recaptchaToken'].includes(id) && (_jsx("span", { className: "text-destructive ml-1", children: "*" }))] }), _jsxs("div", { className: "relative", children: [_jsx(InputComponent, { id: fieldId, type: type, className: cn('w-full', error && 'border-destructive focus-visible:ring-destructive'), "aria-invalid": !!error, "aria-describedby": `${fieldId}-error`, ...register(id) }), error && (_jsx("div", { className: "absolute right-3 top-1/2 -translate-y-1/2", children: _jsx(AlertCircle, { className: "h-5 w-5 text-destructive" }) }))] }), error && (_jsx("p", { id: `${fieldId}-error`, className: "text-sm text-destructive", children: error.message?.toString() }))] }));
    };
    return (_jsxs("div", { className: cn('w-full max-w-2xl mx-auto', className), children: [submitStatus && (_jsxs("div", { className: cn('mb-6 p-4 rounded-lg border', submitStatus.success
                    ? 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200'
                    : 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200', 'flex items-start gap-3'), role: "alert", children: [submitStatus.success ? (_jsx(CheckCircle, { className: "h-5 w-5 mt-0.5 flex-shrink-0" })) : (_jsx(AlertCircle, { className: "h-5 w-5 mt-0.5 flex-shrink-0" })), _jsx("div", { children: submitStatus.message }), _jsxs("button", { type: "button", onClick: () => setSubmitStatus(null), className: "ml-auto opacity-70 hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current rounded", "aria-label": "Dismiss message", children: [_jsx("span", { className: "sr-only", children: "Dismiss" }), _jsx("svg", { className: "h-5 w-5", viewBox: "0 0 20 20", fill: "currentColor", children: _jsx("path", { fillRule: "evenodd", d: "M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z", clipRule: "evenodd" }) })] })] })), _jsxs("form", { onSubmit: handleSubmit(handleFormSubmit), className: "space-y-6", children: [_jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [renderFormField('name', 'Full Name'), renderFormField('email', 'Email Address', 'email')] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [renderFormField('phone', 'Phone Number (Optional)', 'tel'), renderFormField('subject', 'Subject')] }), renderFormField('message', 'Your Message', 'text', true), _jsxs("div", { className: "space-y-2", children: [_jsx("div", { className: "flex items-center space-x-2", children: _jsx(ReCAPTCHA, { sitekey: recaptchaSiteKey, onChange: handleRecaptchaChange, onExpired: () => setValue('recaptchaToken', ''), onErrored: () => setValue('recaptchaToken', ''), className: "w-full" }) }), errors.recaptchaToken && (_jsx("p", { className: "text-sm text-destructive", children: errors.recaptchaToken.message?.toString() }))] }), _jsxs("div", { className: "flex items-start space-x-2", children: [_jsx("div", { className: "flex items-center h-5 mt-0.5", children: _jsx(Checkbox, { id: "privacy-policy", onCheckedChange: (checked) => {
                                        setValue('privacyPolicy', checked === true, { shouldValidate: true });
                                    }, className: cn('h-4 w-4 rounded', errors.privacyPolicy && 'border-destructive text-destructive') }) }), _jsxs("div", { className: "text-sm leading-5", children: [_jsxs("label", { htmlFor: "privacy-policy", className: cn('font-medium', errors.privacyPolicy ? 'text-destructive' : 'text-foreground'), children: ["I agree to the", ' ', _jsx("a", { href: "/privacy-policy", target: "_blank", rel: "noopener noreferrer", className: "text-primary hover:underline", children: "Privacy Policy" }), ' ', "and consent to having my data processed in accordance with it."] }), errors.privacyPolicy && (_jsx("p", { className: "mt-1 text-sm text-destructive", children: errors.privacyPolicy.message?.toString() }))] })] }), _jsx("div", { className: "pt-2", children: _jsx(Button, { type: "submit", size: "lg", className: "w-full md:w-auto min-w-[180px]", disabled: isSubmitting || !isDirty || !isValid, children: isSubmitting ? (_jsxs(_Fragment, { children: [_jsx(Loader2, { className: "mr-2 h-4 w-4 animate-spin" }), "Sending..."] })) : (_jsxs(_Fragment, { children: [_jsx(Send, { className: "mr-2 h-4 w-4" }), "Send Message"] })) }) })] })] }));
};
export default ContactForm;
