import React, { useState } from 'react';
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Loader2, CheckCircle, AlertCircle, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import ReCAPTCHA from 'react-google-recaptcha';

// Form validation schema
const contactFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  phone: z.string().optional(),
  subject: z.string().min(5, { message: 'Subject must be at least 5 characters' }),
  message: z.string().min(10, { message: 'Message must be at least 10 characters' }),
  privacyPolicy: z.boolean().refine(val => val === true, {
    message: 'You must accept the privacy policy',
  }),
  recaptchaToken: z.string().min(1, { message: 'Please complete the reCAPTCHA' }),
});

type ContactFormValues = z.infer<typeof contactFormSchema>;

interface ContactFormProps {
  className?: string;
  recaptchaSiteKey: string;
  onSubmit: (data: Omit<ContactFormValues, 'recaptchaToken'>) => Promise<boolean>;
}

export const ContactForm: React.FC<ContactFormProps> = ({
  className = '',
  recaptchaSiteKey,
  onSubmit,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    success: boolean;
    message: string;
  } | null>(null);
  
  const {
    register,
    handleSubmit,
    setValue,
    trigger,
    reset,
    formState: { errors, isDirty, isValid },
  } = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    mode: 'onChange',
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: '',
      privacyPolicy: false,
      recaptchaToken: '',
    },
  });

  // Handle reCAPTCHA verification
  const handleRecaptchaChange = (token: string | null) => {
    if (token) {
      setValue('recaptchaToken', token, { shouldValidate: true });
    } else {
      setValue('recaptchaToken', '', { shouldValidate: true });
    }
  };

  // Handle form submission
  const handleFormSubmit: SubmitHandler<ContactFormValues> = async (data) => {
    if (isSubmitting) return;
    
    try {
      setIsSubmitting(true);
      setSubmitStatus(null);
      
      // Extract recaptchaToken from the form data
      const { recaptchaToken, ...formData } = data;
      
      const success = await onSubmit(formData);
      
      if (success) {
        setSubmitStatus({
          success: true,
          message: 'Your message has been sent successfully! We\'ll get back to you soon.',
        });
        reset();
        // Reset reCAPTCHA
        if (window.grecaptcha) {
          window.grecaptcha.reset();
        }
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      setSubmitStatus({
        success: false,
        message: 'Failed to send message. Please try again later or contact us <NAME_EMAIL>',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render form field with error message
  const renderFormField = (
    id: keyof ContactFormValues,
    label: string,
    type: string = 'text',
    isTextarea: boolean = false
  ) => {
    const fieldId = `contact-${id}`;
    const error = errors[id];
    const InputComponent = isTextarea ? Textarea : Input;
    
    return (
      <div className="space-y-2">
        <Label htmlFor={fieldId} className={error ? 'text-destructive' : ''}>
          {label}
          {!['phone', 'privacyPolicy', 'recaptchaToken'].includes(id) && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>
        <div className="relative">
          <InputComponent
            id={fieldId}
            type={type}
            className={cn(
              'w-full',
              error && 'border-destructive focus-visible:ring-destructive'
            )}
            aria-invalid={!!error}
            aria-describedby={`${fieldId}-error`}
            {...register(id)}
          />
          {error && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <AlertCircle className="h-5 w-5 text-destructive" />
            </div>
          )}
        </div>
        {error && (
          <p id={`${fieldId}-error`} className="text-sm text-destructive">
            {error.message?.toString()}
          </p>
        )}
      </div>
    );
  };

  return (
    <div className={cn('w-full max-w-2xl mx-auto', className)}>
      {submitStatus && (
        <div
          className={cn(
            'mb-6 p-4 rounded-lg border',
            submitStatus.success
              ? 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200'
              : 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200',
            'flex items-start gap-3'
          )}
          role="alert"
        >
          {submitStatus.success ? (
            <CheckCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
          ) : (
            <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
          )}
          <div>{submitStatus.message}</div>
          <button
            type="button"
            onClick={() => setSubmitStatus(null)}
            className="ml-auto opacity-70 hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current rounded"
            aria-label="Dismiss message"
          >
            <span className="sr-only">Dismiss</span>
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      )}

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {renderFormField('name', 'Full Name')}
          {renderFormField('email', 'Email Address', 'email')}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {renderFormField('phone', 'Phone Number (Optional)', 'tel')}
          {renderFormField('subject', 'Subject')}
        </div>

        {renderFormField('message', 'Your Message', 'text', true)}

        {/* reCAPTCHA */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <ReCAPTCHA
              sitekey={recaptchaSiteKey}
              onChange={handleRecaptchaChange}
              onExpired={() => setValue('recaptchaToken', '')}
              onErrored={() => setValue('recaptchaToken', '')}
              className="w-full"
            />
          </div>
          {errors.recaptchaToken && (
            <p className="text-sm text-destructive">
              {errors.recaptchaToken.message?.toString()}
            </p>
          )}
        </div>

        {/* Privacy Policy Checkbox */}
        <div className="flex items-start space-x-2">
          <div className="flex items-center h-5 mt-0.5">
            <Checkbox
              id="privacy-policy"
              onCheckedChange={(checked) => {
                setValue('privacyPolicy', checked === true, { shouldValidate: true });
              }}
              className={cn(
                'h-4 w-4 rounded',
                errors.privacyPolicy && 'border-destructive text-destructive'
              )}
            />
          </div>
          <div className="text-sm leading-5">
            <label
              htmlFor="privacy-policy"
              className={cn(
                'font-medium',
                errors.privacyPolicy ? 'text-destructive' : 'text-foreground'
              )}
            >
              I agree to the{' '}
              <a
                href="/privacy-policy"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:underline"
              >
                Privacy Policy
              </a>{' '}
              and consent to having my data processed in accordance with it.
            </label>
            {errors.privacyPolicy && (
              <p className="mt-1 text-sm text-destructive">
                {errors.privacyPolicy.message?.toString()}
              </p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <div className="pt-2">
          <Button
            type="submit"
            size="lg"
            className="w-full md:w-auto min-w-[180px]"
            disabled={isSubmitting || !isDirty || !isValid}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Send Message
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ContactForm;
