import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { RefreshCw, Info } from 'lucide-react';
import { getMetrics, getMeasures, trackPageLoad } from '@/utils/performance';
import { getPWAInstallPrompt } from '@/utils/pwa';
/**
 * A development-only component that displays performance metrics
 */
export function PerformanceMonitor({ isOpen: isOpenProp = false, onToggle, position = 'bottom-right', showInProduction = false, }) {
    const [isOpen, setIsOpen] = useState(isOpenProp);
    const [activeTab, setActiveTab] = useState('metrics');
    const [metrics, setMetrics] = useState({});
    const [measures, setMeasures] = useState({});
    const [pwaStatus, setPwaStatus] = useState({ canInstall: false, isInstalled: false });
    // Don't render in production unless explicitly enabled
    if (process.env.NODE_ENV === 'production' && !showInProduction) {
        return null;
    }
    // Toggle the monitor
    const toggleMonitor = () => {
        const newState = !isOpen;
        setIsOpen(newState);
        onToggle?.(newState);
        // Track page load when opening the monitor
        if (newState) {
            updateMetrics();
        }
    };
    // Update metrics and measures
    const updateMetrics = () => {
        setMetrics(getMetrics());
        setMeasures(getMeasures());
        // Check PWA status
        const pwa = getPWAInstallPrompt();
        setPwaStatus({
            canInstall: pwa.canInstall,
            isInstalled: pwa.isInstalled,
        });
    };
    // Initial load
    useEffect(() => {
        updateMetrics();
        // Track initial page load
        trackPageLoad();
        // Update metrics on window load
        const handleLoad = () => {
            setTimeout(updateMetrics, 1000);
        };
        window.addEventListener('load', handleLoad);
        return () => window.removeEventListener('load', handleLoad);
    }, []);
    // Format value with units
    const formatValue = (key, value) => {
        // Time values (ms)
        if (key.endsWith('Time') || key.endsWith('Delay') || key.endsWith('Duration') || key === 'fid') {
            return `${value.toFixed(2)}ms`;
        }
        // Size values (KB)
        if (key.endsWith('Size') || key.endsWith('Transfer')) {
            return `${(value / 1024).toFixed(2)} KB`;
        }
        // Percentage values
        if (key.endsWith('Percentage') || key === 'cls') {
            return `${(value * 100).toFixed(2)}%`;
        }
        // Count values
        return value.toString();
    };
    // Get performance score (0-100) based on Core Web Vitals
    const getPerformanceScore = () => {
        const lcp = metrics.lcp || 0;
        const fid = metrics.fid || 0;
        const cls = metrics.cls || 0;
        // Calculate scores for each metric (0-100)
        const lcpScore = Math.max(0, 100 - (Math.max(0, lcp - 2500) / 25));
        const fidScore = Math.max(0, 100 - (Math.max(0, fid - 100) / 2));
        const clsScore = Math.max(0, 100 - (cls * 10000));
        // Weighted average (LCP: 40%, FID: 30%, CLS: 30%)
        return Math.round((lcpScore * 0.4) + (fidScore * 0.3) + (clsScore * 0.3));
    };
    // Get performance rating (good, needs improvement, poor)
    const getPerformanceRating = (score) => {
        if (score >= 90)
            return 'good';
        if (score >= 50)
            return 'needs-improvement';
        return 'poor';
    };
    const performanceScore = getPerformanceScore();
    const performanceRating = getPerformanceRating(performanceScore);
    // Position classes
    const positionClasses = {
        'top-left': 'top-4 left-4',
        'top-right': 'top-4 right-4',
        'bottom-left': 'bottom-4 left-4',
        'bottom-right': 'bottom-4 right-4',
    }[position];
    // Performance rating colors
    const ratingColors = {
        good: 'bg-green-500',
        'needs-improvement': 'bg-yellow-500',
        poor: 'bg-red-500',
    };
    return (_jsxs(_Fragment, { children: [_jsx("button", { onClick: toggleMonitor, className: cn('fixed z-[9999] flex items-center justify-center w-12 h-12 rounded-full shadow-lg', 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white', 'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500', 'transition-all duration-200 ease-in-out', 'hover:scale-105', isOpen ? 'opacity-0 pointer-events-none' : 'opacity-100', positionClasses), "aria-label": "Open performance monitor", title: "Performance Monitor", children: _jsxs("div", { className: "relative", children: [_jsx("div", { className: cn('w-3 h-3 rounded-full absolute -top-1 -right-1', ratingColors[performanceRating]) }), _jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "24", height: "24", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", className: "w-6 h-6", children: [_jsx("path", { d: "M12 2v4" }), _jsx("path", { d: "M12 18v4" }), _jsx("path", { d: "M4.93 4.93l2.83 2.83" }), _jsx("path", { d: "M16.24 16.24l2.83 2.83" }), _jsx("path", { d: "M2 12h4" }), _jsx("path", { d: "M18 12h4" }), _jsx("path", { d: "M4.93 19.07l2.83-2.83" }), _jsx("path", { d: "M16.24 7.76l2.83-2.83" })] })] }) }), isOpen && (_jsxs("div", { className: cn('fixed z-[9998] w-full max-w-md h-[80vh] max-h-[800px] bg-white dark:bg-gray-900', 'rounded-lg shadow-2xl overflow-hidden flex flex-col', 'border border-gray-200 dark:border-gray-700', positionClasses, {
                    'right-4': position.endsWith('right'),
                    'left-4': position.endsWith('left'),
                    'top-4': position.startsWith('top'),
                    'bottom-4': position.startsWith('bottom'),
                }), children: [_jsxs("div", { className: "flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700", children: [_jsxs("div", { className: "flex items-center space-x-2", children: [_jsx("h2", { className: "text-lg font-semibold text-gray-900 dark:text-white", children: "Performance Monitor" }), _jsx(Badge, { variant: performanceRating === 'good' ? 'success' : performanceRating === 'needs-improvement' ? 'warning' : 'destructive', className: "ml-2", children: performanceScore })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsxs(Button, { variant: "ghost", size: "sm", onClick: updateMetrics, className: "text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white", children: [_jsx(RefreshCw, { className: "w-4 h-4 mr-1" }), "Refresh"] }), _jsx(Button, { variant: "ghost", size: "sm", onClick: toggleMonitor, className: "text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white", children: "Close" })] })] }), _jsxs(Tabs, { defaultValue: "metrics", className: "flex-1 flex flex-col overflow-hidden", onValueChange: setActiveTab, value: activeTab, children: [_jsxs(TabsList, { className: "w-full rounded-none border-b border-gray-200 dark:border-gray-700", children: [_jsx(TabsTrigger, { value: "metrics", children: "Metrics" }), _jsx(TabsTrigger, { value: "measures", children: "Measures" }), _jsx(TabsTrigger, { value: "pwa", children: "PWA" }), _jsx(TabsTrigger, { value: "info", children: "Info" })] }), _jsx("div", { className: "flex-1 overflow-hidden", children: _jsxs(ScrollArea, { className: "h-full w-full p-4", children: [_jsx(TabsContent, { value: "metrics", className: "m-0", children: _jsxs("div", { className: "space-y-4", children: [_jsxs(Card, { children: [_jsx(CardHeader, { className: "pb-2", children: _jsx(CardTitle, { className: "text-sm font-medium", children: "Core Web Vitals" }) }), _jsx(CardContent, { className: "space-y-2", children: _jsxs("div", { className: "grid grid-cols-3 gap-4", children: [_jsxs("div", { className: "space-y-1", children: [_jsx("div", { className: "text-xs text-gray-500 dark:text-gray-400", children: "LCP" }), _jsx("div", { className: "text-sm font-medium", children: metrics.lcp ? `${metrics.lcp.toFixed(2)}ms` : 'N/A' }), _jsx("div", { className: "text-xs text-gray-500 dark:text-gray-400", children: metrics.lcp ? (_jsxs("span", { className: metrics.lcp <= 2500 ? 'text-green-500' : 'text-red-500', children: [metrics.lcp <= 2500 ? 'Good' : 'Poor', " (<2.5s)"] })) : null })] }), _jsxs("div", { className: "space-y-1", children: [_jsx("div", { className: "text-xs text-gray-500 dark:text-gray-400", children: "FID" }), _jsx("div", { className: "text-sm font-medium", children: metrics.fid ? `${metrics.fid.toFixed(2)}ms` : 'N/A' }), _jsx("div", { className: "text-xs text-gray-500 dark:text-gray-400", children: metrics.fid ? (_jsxs("span", { className: metrics.fid <= 100 ? 'text-green-500' : 'text-red-500', children: [metrics.fid <= 100 ? 'Good' : 'Poor', " (<100ms)"] })) : null })] }), _jsxs("div", { className: "space-y-1", children: [_jsx("div", { className: "text-xs text-gray-500 dark:text-gray-400", children: "CLS" }), _jsx("div", { className: "text-sm font-medium", children: metrics.cls ? metrics.cls.toFixed(4) : 'N/A' }), _jsx("div", { className: "text-xs text-gray-500 dark:text-gray-400", children: metrics.cls ? (_jsxs("span", { className: metrics.cls <= 0.1 ? 'text-green-500' : 'text-red-500', children: [metrics.cls <= 0.1 ? 'Good' : 'Poor', " (<0.1)"] })) : null })] })] }) })] }), _jsxs(Card, { children: [_jsx(CardHeader, { className: "pb-2", children: _jsx(CardTitle, { className: "text-sm font-medium", children: "Navigation Timing" }) }), _jsx(CardContent, { className: "p-0", children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Metric" }), _jsx(TableHead, { className: "text-right", children: "Value" })] }) }), _jsx(TableBody, { children: Object.entries(metrics)
                                                                                .filter(([key]) => key.startsWith('page_'))
                                                                                .map(([key, value]) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "py-1.5 text-sm", children: key.replace('page_', '').replace(/([A-Z])/g, ' $1').trim() }), _jsx(TableCell, { className: "py-1.5 text-right text-sm font-mono", children: formatValue(key, value) })] }, key))) })] }) })] })] }) }), _jsx(TabsContent, { value: "measures", className: "m-0", children: _jsx(Card, { children: _jsx(CardContent, { className: "p-0", children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Measure" }), _jsx(TableHead, { className: "text-right", children: "Duration" })] }) }), _jsx(TableBody, { children: Object.entries(measures).length > 0 ? (Object.entries(measures).map(([key, value]) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "py-1.5 text-sm", children: key }), _jsxs(TableCell, { className: "py-1.5 text-right text-sm font-mono", children: [value.toFixed(2), "ms"] })] }, key)))) : (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: 2, className: "text-center py-4 text-sm text-gray-500", children: "No custom measures recorded yet." }) })) })] }) }) }) }), _jsx(TabsContent, { value: "pwa", className: "m-0", children: _jsxs("div", { className: "space-y-4", children: [_jsxs(Card, { children: [_jsx(CardHeader, { className: "pb-2", children: _jsx(CardTitle, { className: "text-sm font-medium", children: "PWA Status" }) }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { className: "text-sm", children: "Installable" }), _jsx(Badge, { variant: pwaStatus.canInstall ? 'success' : 'outline', children: pwaStatus.canInstall ? 'Yes' : 'No' })] }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { className: "text-sm", children: "Installed" }), _jsx(Badge, { variant: pwaStatus.isInstalled ? 'success' : 'outline', children: pwaStatus.isInstalled ? 'Yes' : 'No' })] }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { className: "text-sm", children: "Service Worker" }), _jsx(Badge, { variant: navigator.serviceWorker?.controller ? 'success' : 'outline', children: navigator.serviceWorker?.controller ? 'Active' : 'Inactive' })] }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { className: "text-sm", children: "Offline Support" }), _jsx(Badge, { variant: navigator.onLine ? 'outline' : 'warning', children: navigator.onLine ? 'Online' : 'Offline' })] })] }), pwaStatus.canInstall && !pwaStatus.isInstalled && (_jsx(Button, { className: "w-full mt-4", onClick: () => {
                                                                            const pwa = getPWAInstallPrompt();
                                                                            pwa.prompt().catch(console.error);
                                                                        }, children: "Install App" }))] })] }), _jsxs(Card, { children: [_jsx(CardHeader, { className: "pb-2", children: _jsx(CardTitle, { className: "text-sm font-medium", children: "Storage" }) }), _jsx(CardContent, { children: _jsxs("div", { className: "space-y-2", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { className: "text-sm", children: "Cache API" }), _jsx("span", { className: "text-sm font-mono", children: window.caches ? 'Available' : 'Not Available' })] }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { className: "text-sm", children: "IndexedDB" }), _jsx("span", { className: "text-sm font-mono", children: window.indexedDB ? 'Available' : 'Not Available' })] }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { className: "text-sm", children: "Local Storage" }), _jsx("span", { className: "text-sm font-mono", children: window.localStorage ? 'Available' : 'Not Available' })] }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { className: "text-sm", children: "Session Storage" }), _jsx("span", { className: "text-sm font-mono", children: window.sessionStorage ? 'Available' : 'Not Available' })] })] }) })] })] }) }), _jsx(TabsContent, { value: "info", className: "m-0", children: _jsxs("div", { className: "space-y-4", children: [_jsxs(Card, { children: [_jsx(CardHeader, { className: "pb-2", children: _jsx(CardTitle, { className: "text-sm font-medium", children: "Environment" }) }), _jsx(CardContent, { className: "p-0", children: _jsx(Table, { children: _jsxs(TableBody, { children: [_jsxs(TableRow, { children: [_jsx(TableCell, { className: "py-1.5 text-sm", children: "Environment" }), _jsx(TableCell, { className: "py-1.5 text-right text-sm font-mono", children: process.env.NODE_ENV })] }), _jsxs(TableRow, { children: [_jsx(TableCell, { className: "py-1.5 text-sm", children: "User Agent" }), _jsx(TableCell, { className: "py-1.5 text-right text-xs font-mono", children: navigator.userAgent })] }), _jsxs(TableRow, { children: [_jsx(TableCell, { className: "py-1.5 text-sm", children: "Platform" }), _jsx(TableCell, { className: "py-1.5 text-right text-sm font-mono", children: navigator.platform })] }), _jsxs(TableRow, { children: [_jsx(TableCell, { className: "py-1.5 text-sm", children: "Language" }), _jsx(TableCell, { className: "py-1.5 text-right text-sm font-mono", children: navigator.language })] }), _jsxs(TableRow, { children: [_jsx(TableCell, { className: "py-1.5 text-sm", children: "Online" }), _jsx(TableCell, { className: "py-1.5 text-right", children: _jsx(Badge, { variant: navigator.onLine ? 'success' : 'destructive', children: navigator.onLine ? 'Online' : 'Offline' }) })] }), _jsxs(TableRow, { children: [_jsx(TableCell, { className: "py-1.5 text-sm", children: "Connection" }), _jsx(TableCell, { className: "py-1.5 text-right text-sm font-mono", children: navigator.connection?.effectiveType || 'Unknown' })] }), _jsxs(TableRow, { children: [_jsx(TableCell, { className: "py-1.5 text-sm", children: "Device Memory" }), _jsxs(TableCell, { className: "py-1.5 text-right text-sm font-mono", children: [navigator.deviceMemory || 'Unknown', " GB"] })] }), _jsxs(TableRow, { children: [_jsx(TableCell, { className: "py-1.5 text-sm", children: "Hardware Concurrency" }), _jsxs(TableCell, { className: "py-1.5 text-right text-sm font-mono", children: [navigator.hardwareConcurrency || 'Unknown', " cores"] })] })] }) }) })] }), _jsx("div", { className: "bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4 text-sm text-yellow-700 dark:text-yellow-300", children: _jsxs("div", { className: "flex items-start", children: [_jsx(Info, { className: "h-5 w-5 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" }), _jsxs("div", { children: [_jsx("h3", { className: "font-medium mb-1", children: "Development Tools" }), _jsx("p", { className: "text-yellow-600 dark:text-yellow-400", children: "This component is only visible in development mode. It will be automatically removed in production builds." })] })] }) })] }) })] }) })] })] }))] }));
}
// Utility function to combine class names
function cn(...classes) {
    return classes.filter(Boolean).join(' ');
}
// Export a hook to use the performance monitor in your app
export function usePerformanceMonitor(options = {}) {
    const [isOpen, setIsOpen] = React.useState(false);
    // Only render in development
    if (process.env.NODE_ENV === 'production' && !options.showInProduction) {
        return null;
    }
    return {
        isOpen,
        toggle: () => setIsOpen(!isOpen),
        show: () => setIsOpen(true),
        hide: () => setIsOpen(false),
        PerformanceMonitor: () => (_jsx(PerformanceMonitor, { isOpen: isOpen, onToggle: setIsOpen, ...options })),
    };
}
