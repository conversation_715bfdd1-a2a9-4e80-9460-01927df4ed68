import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { RefreshCw, AlertTriangle, Info } from 'lucide-react';
import { getMetrics, getMeasures, trackPageLoad } from '@/utils/performance';
import { getPWAInstallPrompt } from '@/utils/pwa';

interface PerformanceMonitorProps {
  /**
   * Whether the monitor is open
   * @default false
   */
  isOpen?: boolean;
  
  /**
   * Callback when the monitor is toggled
   */
  onToggle?: (isOpen: boolean) => void;
  
  /**
   * Position of the monitor
   * @default 'bottom-right'
   */
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  
  /**
   * Whether to show the monitor in production
   * @default false
   */
  showInProduction?: boolean;
}

/**
 * A development-only component that displays performance metrics
 */
export function PerformanceMonitor({
  isOpen: isOpenProp = false,
  onToggle,
  position = 'bottom-right',
  showInProduction = false,
}: PerformanceMonitorProps) {
  const [isOpen, setIsOpen] = useState(isOpenProp);
  const [activeTab, setActiveTab] = useState('metrics');
  const [metrics, setMetrics] = useState<Record<string, number>>({});
  const [measures, setMeasures] = useState<Record<string, number>>({});
  const [pwaStatus, setPwaStatus] = useState<{
    canInstall: boolean;
    isInstalled: boolean;
  }>({ canInstall: false, isInstalled: false });
  
  // Don't render in production unless explicitly enabled
  if (process.env.NODE_ENV === 'production' && !showInProduction) {
    return null;
  }
  
  // Toggle the monitor
  const toggleMonitor = () => {
    const newState = !isOpen;
    setIsOpen(newState);
    onToggle?.(newState);
    
    // Track page load when opening the monitor
    if (newState) {
      updateMetrics();
    }
  };
  
  // Update metrics and measures
  const updateMetrics = () => {
    setMetrics(getMetrics());
    setMeasures(getMeasures());
    
    // Check PWA status
    const pwa = getPWAInstallPrompt();
    setPwaStatus({
      canInstall: pwa.canInstall,
      isInstalled: pwa.isInstalled,
    });
  };
  
  // Initial load
  useEffect(() => {
    updateMetrics();
    
    // Track initial page load
    trackPageLoad();
    
    // Update metrics on window load
    const handleLoad = () => {
      setTimeout(updateMetrics, 1000);
    };
    
    window.addEventListener('load', handleLoad);
    return () => window.removeEventListener('load', handleLoad);
  }, []);
  
  // Format value with units
  const formatValue = (key: string, value: number) => {
    // Time values (ms)
    if (key.endsWith('Time') || key.endsWith('Delay') || key.endsWith('Duration') || key === 'fid') {
      return `${value.toFixed(2)}ms`;
    }
    
    // Size values (KB)
    if (key.endsWith('Size') || key.endsWith('Transfer')) {
      return `${(value / 1024).toFixed(2)} KB`;
    }
    
    // Percentage values
    if (key.endsWith('Percentage') || key === 'cls') {
      return `${(value * 100).toFixed(2)}%`;
    }
    
    // Count values
    return value.toString();
  };
  
  // Get performance score (0-100) based on Core Web Vitals
  const getPerformanceScore = () => {
    const lcp = metrics.lcp || 0;
    const fid = metrics.fid || 0;
    const cls = metrics.cls || 0;
    
    // Calculate scores for each metric (0-100)
    const lcpScore = Math.max(0, 100 - (Math.max(0, lcp - 2500) / 25));
    const fidScore = Math.max(0, 100 - (Math.max(0, fid - 100) / 2));
    const clsScore = Math.max(0, 100 - (cls * 10000));
    
    // Weighted average (LCP: 40%, FID: 30%, CLS: 30%)
    return Math.round((lcpScore * 0.4) + (fidScore * 0.3) + (clsScore * 0.3));
  };
  
  // Get performance rating (good, needs improvement, poor)
  const getPerformanceRating = (score: number) => {
    if (score >= 90) return 'good';
    if (score >= 50) return 'needs-improvement';
    return 'poor';
  };
  
  const performanceScore = getPerformanceScore();
  const performanceRating = getPerformanceRating(performanceScore);
  
  // Position classes
  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
  }[position];
  
  // Performance rating colors
  const ratingColors = {
    good: 'bg-green-500',
    'needs-improvement': 'bg-yellow-500',
    poor: 'bg-red-500',
  };
  
  return (
    <>
      {/* Toggle button */}
      <button
        onClick={toggleMonitor}
        className={cn(
          'fixed z-[9999] flex items-center justify-center w-12 h-12 rounded-full shadow-lg',
          'bg-white dark:bg-gray-800 text-gray-900 dark:text-white',
          'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
          'transition-all duration-200 ease-in-out',
          'hover:scale-105',
          isOpen ? 'opacity-0 pointer-events-none' : 'opacity-100',
          positionClasses
        )}
        aria-label="Open performance monitor"
        title="Performance Monitor"
      >
        <div className="relative">
          <div className={cn(
            'w-3 h-3 rounded-full absolute -top-1 -right-1',
            ratingColors[performanceRating]
          )} />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="w-6 h-6"
          >
            <path d="M12 2v4" />
            <path d="M12 18v4" />
            <path d="M4.93 4.93l2.83 2.83" />
            <path d="M16.24 16.24l2.83 2.83" />
            <path d="M2 12h4" />
            <path d="M18 12h4" />
            <path d="M4.93 19.07l2.83-2.83" />
            <path d="M16.24 7.76l2.83-2.83" />
          </svg>
        </div>
      </button>
      
      {/* Monitor panel */}
      {isOpen && (
        <div 
          className={cn(
            'fixed z-[9998] w-full max-w-md h-[80vh] max-h-[800px] bg-white dark:bg-gray-900',
            'rounded-lg shadow-2xl overflow-hidden flex flex-col',
            'border border-gray-200 dark:border-gray-700',
            positionClasses,
            {
              'right-4': position.endsWith('right'),
              'left-4': position.endsWith('left'),
              'top-4': position.startsWith('top'),
              'bottom-4': position.startsWith('bottom'),
            }
          )}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Performance Monitor
              </h2>
              <Badge 
                variant={performanceRating === 'good' ? 'success' : performanceRating === 'needs-improvement' ? 'warning' : 'destructive'}
                className="ml-2"
              >
                {performanceScore}
              </Badge>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={updateMetrics}
                className="text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
              >
                <RefreshCw className="w-4 h-4 mr-1" />
                Refresh
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleMonitor}
                className="text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
              >
                Close
              </Button>
            </div>
          </div>
          
          {/* Content */}
          <Tabs 
            defaultValue="metrics" 
            className="flex-1 flex flex-col overflow-hidden"
            onValueChange={setActiveTab}
            value={activeTab}
          >
            <TabsList className="w-full rounded-none border-b border-gray-200 dark:border-gray-700">
              <TabsTrigger value="metrics">Metrics</TabsTrigger>
              <TabsTrigger value="measures">Measures</TabsTrigger>
              <TabsTrigger value="pwa">PWA</TabsTrigger>
              <TabsTrigger value="info">Info</TabsTrigger>
            </TabsList>
            
            <div className="flex-1 overflow-hidden">
              <ScrollArea className="h-full w-full p-4">
                {/* Metrics tab */}
                <TabsContent value="metrics" className="m-0">
                  <div className="space-y-4">
                    {/* Core Web Vitals */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">
                          Core Web Vitals
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="grid grid-cols-3 gap-4">
                          <div className="space-y-1">
                            <div className="text-xs text-gray-500 dark:text-gray-400">LCP</div>
                            <div className="text-sm font-medium">
                              {metrics.lcp ? `${metrics.lcp.toFixed(2)}ms` : 'N/A'}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {metrics.lcp ? (
                                <span className={metrics.lcp <= 2500 ? 'text-green-500' : 'text-red-500'}>
                                  {metrics.lcp <= 2500 ? 'Good' : 'Poor'} (&lt;2.5s)
                                </span>
                              ) : null}
                            </div>
                          </div>
                          
                          <div className="space-y-1">
                            <div className="text-xs text-gray-500 dark:text-gray-400">FID</div>
                            <div className="text-sm font-medium">
                              {metrics.fid ? `${metrics.fid.toFixed(2)}ms` : 'N/A'}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {metrics.fid ? (
                                <span className={metrics.fid <= 100 ? 'text-green-500' : 'text-red-500'}>
                                  {metrics.fid <= 100 ? 'Good' : 'Poor'} (&lt;100ms)
                                </span>
                              ) : null}
                            </div>
                          </div>
                          
                          <div className="space-y-1">
                            <div className="text-xs text-gray-500 dark:text-gray-400">CLS</div>
                            <div className="text-sm font-medium">
                              {metrics.cls ? metrics.cls.toFixed(4) : 'N/A'}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {metrics.cls ? (
                                <span className={metrics.cls <= 0.1 ? 'text-green-500' : 'text-red-500'}>
                                  {metrics.cls <= 0.1 ? 'Good' : 'Poor'} (&lt;0.1)
                                </span>
                              ) : null}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    
                    {/* Navigation Timing */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">
                          Navigation Timing
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-0">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Metric</TableHead>
                              <TableHead className="text-right">Value</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {Object.entries(metrics)
                              .filter(([key]) => key.startsWith('page_'))
                              .map(([key, value]) => (
                                <TableRow key={key}>
                                  <TableCell className="py-1.5 text-sm">
                                    {key.replace('page_', '').replace(/([A-Z])/g, ' $1').trim()}
                                  </TableCell>
                                  <TableCell className="py-1.5 text-right text-sm font-mono">
                                    {formatValue(key, value)}
                                  </TableCell>
                                </TableRow>
                              ))}
                          </TableBody>
                        </Table>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
                
                {/* Measures tab */}
                <TabsContent value="measures" className="m-0">
                  <Card>
                    <CardContent className="p-0">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Measure</TableHead>
                            <TableHead className="text-right">Duration</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {Object.entries(measures).length > 0 ? (
                            Object.entries(measures).map(([key, value]) => (
                              <TableRow key={key}>
                                <TableCell className="py-1.5 text-sm">
                                  {key}
                                </TableCell>
                                <TableCell className="py-1.5 text-right text-sm font-mono">
                                  {value.toFixed(2)}ms
                                </TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <TableRow>
                              <TableCell colSpan={2} className="text-center py-4 text-sm text-gray-500">
                                No custom measures recorded yet.
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                {/* PWA tab */}
                <TabsContent value="pwa" className="m-0">
                  <div className="space-y-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">
                          PWA Status
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Installable</span>
                            <Badge variant={pwaStatus.canInstall ? 'success' : 'outline'}>
                              {pwaStatus.canInstall ? 'Yes' : 'No'}
                            </Badge>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Installed</span>
                            <Badge variant={pwaStatus.isInstalled ? 'success' : 'outline'}>
                              {pwaStatus.isInstalled ? 'Yes' : 'No'}
                            </Badge>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Service Worker</span>
                            <Badge variant={navigator.serviceWorker?.controller ? 'success' : 'outline'}>
                              {navigator.serviceWorker?.controller ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Offline Support</span>
                            <Badge variant={navigator.onLine ? 'outline' : 'warning'}>
                              {navigator.onLine ? 'Online' : 'Offline'}
                            </Badge>
                          </div>
                        </div>
                        
                        {pwaStatus.canInstall && !pwaStatus.isInstalled && (
                          <Button 
                            className="w-full mt-4"
                            onClick={() => {
                              const pwa = getPWAInstallPrompt();
                              pwa.prompt().catch(console.error);
                            }}
                          >
                            Install App
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">
                          Storage
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Cache API</span>
                            <span className="text-sm font-mono">
                              {window.caches ? 'Available' : 'Not Available'}
                            </span>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-sm">IndexedDB</span>
                            <span className="text-sm font-mono">
                              {window.indexedDB ? 'Available' : 'Not Available'}
                            </span>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Local Storage</span>
                            <span className="text-sm font-mono">
                              {window.localStorage ? 'Available' : 'Not Available'}
                            </span>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Session Storage</span>
                            <span className="text-sm font-mono">
                              {window.sessionStorage ? 'Available' : 'Not Available'}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
                
                {/* Info tab */}
                <TabsContent value="info" className="m-0">
                  <div className="space-y-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">
                          Environment
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-0">
                        <Table>
                          <TableBody>
                            <TableRow>
                              <TableCell className="py-1.5 text-sm">Environment</TableCell>
                              <TableCell className="py-1.5 text-right text-sm font-mono">
                                {process.env.NODE_ENV}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className="py-1.5 text-sm">User Agent</TableCell>
                              <TableCell className="py-1.5 text-right text-xs font-mono">
                                {navigator.userAgent}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className="py-1.5 text-sm">Platform</TableCell>
                              <TableCell className="py-1.5 text-right text-sm font-mono">
                                {navigator.platform}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className="py-1.5 text-sm">Language</TableCell>
                              <TableCell className="py-1.5 text-right text-sm font-mono">
                                {navigator.language}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className="py-1.5 text-sm">Online</TableCell>
                              <TableCell className="py-1.5 text-right">
                                <Badge variant={navigator.onLine ? 'success' : 'destructive'}>
                                  {navigator.onLine ? 'Online' : 'Offline'}
                                </Badge>
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className="py-1.5 text-sm">Connection</TableCell>
                              <TableCell className="py-1.5 text-right text-sm font-mono">
                                {(navigator as any).connection?.effectiveType || 'Unknown'}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className="py-1.5 text-sm">Device Memory</TableCell>
                              <TableCell className="py-1.5 text-right text-sm font-mono">
                                {(navigator as any).deviceMemory || 'Unknown'} GB
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className="py-1.5 text-sm">Hardware Concurrency</TableCell>
                              <TableCell className="py-1.5 text-right text-sm font-mono">
                                {navigator.hardwareConcurrency || 'Unknown'} cores
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </CardContent>
                    </Card>
                    
                    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4 text-sm text-yellow-700 dark:text-yellow-300">
                      <div className="flex items-start">
                        <Info className="h-5 w-5 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                        <div>
                          <h3 className="font-medium mb-1">Development Tools</h3>
                          <p className="text-yellow-600 dark:text-yellow-400">
                            This component is only visible in development mode. It will be automatically removed in production builds.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </ScrollArea>
            </div>
          </Tabs>
        </div>
      )}
    </>
  );
}

// Utility function to combine class names
function cn(...classes: (string | undefined)[]): string {
  return classes.filter(Boolean).join(' ');
}

// Export a hook to use the performance monitor in your app
export function usePerformanceMonitor(options: Omit<PerformanceMonitorProps, 'isOpen' | 'onToggle'> = {}) {
  const [isOpen, setIsOpen] = React.useState(false);
  
  // Only render in development
  if (process.env.NODE_ENV === 'production' && !options.showInProduction) {
    return null;
  }
  
  return {
    isOpen,
    toggle: () => setIsOpen(!isOpen),
    show: () => setIsOpen(true),
    hide: () => setIsOpen(false),
    PerformanceMonitor: () => (
      <PerformanceMonitor 
        isOpen={isOpen} 
        onToggle={setIsOpen} 
        {...options} 
      />
    ),
  };
}
