import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
const LanguageSwitcher = ({ className }) => {
    const { i18n } = useTranslation();
    const changeLanguage = (lng) => {
        i18n.changeLanguage(lng);
    };
    return (_jsxs("div", { className: cn("flex items-center space-x-1", className), children: [_jsx(Button, { variant: i18n.language === 'en' ? 'secondary' : 'ghost', size: "sm", onClick: () => changeLanguage('en'), className: cn('text-sm px-2 py-1', i18n.language === 'en' ? 'bg-blue-100 text-blue-800' : 'text-gray-700'), children: "EN" }), _jsx("span", { className: "text-gray-300", children: "|" }), _jsx(Button, { variant: i18n.language === 'mi' ? 'secondary' : 'ghost', size: "sm", onClick: () => changeLanguage('mi'), className: cn('text-sm px-2 py-1', i18n.language === 'mi' ? 'bg-blue-100 text-blue-800' : 'text-gray-700'), children: "MI" })] }));
};
export default LanguageSwitcher;
