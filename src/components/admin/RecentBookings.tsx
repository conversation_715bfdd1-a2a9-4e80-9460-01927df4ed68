import { useTranslation } from 'react-i18next';
import { Clock, User, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

type BookingStatus = 'confirmed' | 'pending' | 'cancelled' | 'completed';

export interface Booking {
  id: string;
  customer: {
    name: string;
    email: string;
    avatar?: string;
  };
  experience: string;
  date: string;
  time: string;
  guests: number;
  status: BookingStatus;
  amount: number;
}

interface RecentBookingsProps {
  bookings: Booking[];
}

export function RecentBookings({ bookings }: RecentBookingsProps) {
  const { t } = useTranslation();

  const getStatusVariant = (status: BookingStatus) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (bookings.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          {t('admin.noRecentBookings', 'No recent bookings found.')}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {bookings.map((booking) => (
        <div key={booking.id} className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Avatar className="h-9 w-9">
              <AvatarImage src={booking.customer.avatar} alt={booking.customer.name} />
              <AvatarFallback>
                {booking.customer.name
                  .split(' ')
                  .map((n) => n[0])
                  .join('')}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">
                {booking.customer.name}
              </p>
              <p className="text-sm text-muted-foreground">
                {booking.experience}
              </p>
              <div className="flex items-center text-xs text-muted-foreground mt-1">
                <Calendar className="h-3 w-3 mr-1" />
                {format(new Date(booking.date), 'MMM d, yyyy')}
                <span className="mx-1">·</span>
                <Clock className="h-3 w-3 mr-1" />
                {booking.time}
                <span className="mx-1">·</span>
                <User className="h-3 w-3 mr-1" />
                {booking.guests} {booking.guests > 1 ? t('common.guests', 'guests') : t('common.guest', 'guest')}
              </div>
            </div>
          </div>
          <div className="flex flex-col items-end space-y-1">
            <Badge className={cn('text-xs', getStatusVariant(booking.status))}>
              {t(`booking.status.${booking.status}`, booking.status)}
            </Badge>
            <div className="text-sm font-medium">
              ${booking.amount.toFixed(2)}
            </div>
          </div>
        </div>
      ))}
      <div className="flex justify-end pt-2">
        <Button variant="ghost" size="sm">
          {t('common.viewAll', 'View all')}
        </Button>
      </div>
    </div>
  );
}

export default RecentBookings;
