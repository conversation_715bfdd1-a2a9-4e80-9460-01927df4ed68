import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Bell, Menu, User, LogOut } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
export function Header({ onMenuClick, user, onLogout }) {
    const { t } = useTranslation();
    const [showProfileMenu, setShowProfileMenu] = useState(false);
    const handleLogout = () => {
        if (onLogout) {
            onLogout();
        }
    };
    return (_jsx("header", { className: "bg-white shadow-sm z-10", children: _jsxs("div", { className: "flex items-center justify-between h-16 px-6", children: [_jsxs("div", { className: "flex items-center", children: [_jsxs(<PERSON><PERSON>, { variant: "ghost", size: "icon", className: "md:hidden mr-2", onClick: onMenuClick, children: [_jsx(Menu, { className: "h-5 w-5" }), _jsx("span", { className: "sr-only", children: t('admin.toggleMenu', 'Toggle menu') })] }), _jsx("h1", { className: "text-lg font-semibold text-gray-900", children: t('admin.dashboard', 'Dashboard') })] }), _jsxs("div", { className: "flex items-center space-x-4", children: [_jsxs(Button, { variant: "ghost", size: "icon", className: "relative", children: [_jsx(Bell, { className: "h-5 w-5" }), _jsx("span", { className: "sr-only", children: t('admin.notifications', 'Notifications') }), _jsx("span", { className: "absolute top-1 right-1 h-2 w-2 rounded-full bg-red-500" })] }), _jsxs("div", { className: "relative", children: [_jsxs(Button, { variant: "ghost", className: "flex items-center space-x-2", onClick: () => setShowProfileMenu(!showProfileMenu), children: [_jsxs(Avatar, { className: "h-8 w-8", children: [_jsx(AvatarImage, { src: user?.avatar, alt: user?.name }), _jsx(AvatarFallback, { children: user?.name?.charAt(0) || _jsx(User, { className: "h-4 w-4" }) })] }), _jsx("span", { className: "hidden md:inline-flex text-sm font-medium", children: user?.name || t('admin.admin', 'Admin') })] }), showProfileMenu && (_jsx("div", { className: "absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5", children: _jsxs("div", { className: "py-1", children: [_jsx("a", { href: "/admin/profile", className: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", children: t('admin.profile', 'Profile') }), _jsxs("button", { onClick: handleLogout, className: "w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2", children: [_jsx(LogOut, { className: "h-4 w-4" }), _jsx("span", { children: t('admin.signOut', 'Sign out') })] })] }) }))] })] })] }) }));
}
