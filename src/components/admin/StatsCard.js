import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { ArrowUp, ArrowDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
export function StatsCard({ title, value, icon: Icon, trend = 0, isCurrency = false, loading = false, }) {
    const isPositive = trend >= 0;
    const trendText = isPositive ? `+${trend}%` : `${trend}%`;
    if (loading) {
        return (_jsx("div", { className: "rounded-xl border bg-card text-card-foreground shadow", children: _jsxs("div", { className: "p-6", children: [_jsxs("div", { className: "flex items-center justify-between space-y-2", children: [_jsx(Skeleton, { className: "h-4 w-1/2" }), _jsx(Skeleton, { className: "h-8 w-8 rounded-full" })] }), _jsxs("div", { className: "mt-4", children: [_jsx(Skeleton, { className: "h-8 w-3/4" }), _jsx(Skeleton, { className: "mt-2 h-4 w-1/2" })] })] }) }));
    }
    return (_jsx("div", { className: "rounded-xl border bg-card text-card-foreground shadow", children: _jsxs("div", { className: "p-6", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsx("h3", { className: "text-sm font-medium text-muted-foreground", children: title }), _jsx("div", { className: "rounded-lg p-2 bg-primary/10", children: _jsx(Icon, { className: "h-4 w-4 text-primary" }) })] }), _jsxs("div", { className: "mt-4", children: [_jsx("div", { className: "text-2xl font-bold", children: isCurrency && typeof value !== 'number' && !value.startsWith('$') ? `$${value}` : value }), trend !== undefined && (_jsxs("div", { className: cn('mt-2 flex items-center text-sm', isPositive ? 'text-green-600' : 'text-red-600'), children: [isPositive ? (_jsx(ArrowUp, { className: "h-4 w-4" })) : (_jsx(ArrowDown, { className: "h-4 w-4" })), _jsx("span", { className: "ml-1", children: trendText }), _jsx("span", { className: "ml-1 text-muted-foreground", children: "vs last period" })] }))] })] }) }));
}
export default StatsCard;
