import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Home,
  Calendar,
  Users,
  Settings,
  BookOpen,
  BarChart2,
  MessageSquare,
  LogOut,
  Menu as MenuIcon,
  X,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  items?: NavItem[];
}

interface SidebarProps {
  user: {
    name: string;
    email: string;
    avatar?: string;
  };
  onLogout: () => void;
}

export function Sidebar({ user, onLogout }: SidebarProps) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const navItems: NavItem[] = [
    {
      name: 'dashboard',
      href: '/admin',
      icon: <Home className="h-5 w-5" />,
    },
    {
      name: 'bookings',
      href: '/admin/bookings',
      icon: <Calendar className="h-5 w-5" />,
    },
    {
      name: 'availability',
      href: '/admin/availability',
      icon: <Calendar className="h-5 w-5" />,
    },
    {
      name: 'customers',
      href: '/admin/customers',
      icon: <Users className="h-5 w-5" />,
    },
    {
      name: 'blog',
      href: '/admin/blog',
      icon: <BookOpen className="h-5 w-5" />,
    },
    {
      name: 'testimonials',
      href: '/admin/testimonials',
      icon: <MessageSquare className="h-5 w-5" />,
    },
    {
      name: 'reports',
      href: '/admin/reports',
      icon: <BarChart2 className="h-5 w-5" />,
    },
    {
      name: 'settings',
      href: '/admin/settings',
      icon: <Settings className="h-5 w-5" />,
    },
  ];

  const handleLogout = () => {
    onLogout();
  };

  return (
    <>
      {/* Mobile menu button */}
      <div className="md:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="p-2 rounded-md text-gray-500 hover:bg-gray-100 focus:outline-none"
        >
          <span className="sr-only">
            {isOpen ? t('admin.closeMenu', 'Close menu') : t('admin.openMenu', 'Open menu')}
          </span>
          {isOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <MenuIcon className="h-6 w-6" />
          )}
        </button>
      </div>

      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-40 w-64 transform bg-white shadow-lg transition-transform duration-300 ease-in-out md:translate-x-0',
          isOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
        )}
      >
        <div className="flex h-full flex-col">
          <div className="flex h-16 items-center justify-center border-b px-4">
            <h2 className="text-xl font-bold text-gray-800">
              Sailing Serai
            </h2>
          </div>

          {/* User profile */}
          <div className="mt-auto border-t border-gray-200 pt-4">
            <div className="flex items-center px-4">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                  {user.avatar ? (
                    <img
                      className="h-10 w-10 rounded-full"
                      src={user.avatar}
                      alt={user.name}
                    />
                  ) : (
                    <span className="text-gray-600">
                      {user.name
                        .split(' ')
                        .map((n) => n[0])
                        .join('')}
                    </span>
                  )}
                </div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-700">{user.name}</p>
                <p className="text-xs text-gray-500">{user.email}</p>
              </div>
            </div>
          </div>

          <nav className="flex-1 space-y-1 overflow-y-auto px-2 py-4">
            {navItems.map((item) => (
              <NavLink
                key={item.href}
                to={item.href}
                className={({ isActive }) =>
                  cn(
                    'flex items-center px-4 py-2.5 text-sm font-medium rounded-md',
                    isActive
                      ? 'bg-blue-50 text-blue-600'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  )
                }
                onClick={() => setIsOpen(false)}
              >
                <span className="mr-3">{item.icon}</span>
                <span>{t(`admin.${item.name}`, item.name)}</span>
              </NavLink>
            ))}
          </nav>

          <div className="border-t p-4">
            <button
              onClick={handleLogout}
              className="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900"
            >
              <LogOut className="mr-3 h-5 w-5" />
              <span>{t('admin.signOut', 'Sign out')}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-30 bg-black bg-opacity-50 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
}
