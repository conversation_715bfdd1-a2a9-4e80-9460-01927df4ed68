import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useTranslation } from 'react-i18next';
import { Clock, User, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
export function RecentBookings({ bookings }) {
    const { t } = useTranslation();
    const getStatusVariant = (status) => {
        switch (status) {
            case 'confirmed':
                return 'bg-green-100 text-green-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            case 'completed':
                return 'bg-blue-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    if (bookings.length === 0) {
        return (_jsx("div", { className: "text-center py-8", children: _jsx("p", { className: "text-muted-foreground", children: t('admin.noRecentBookings', 'No recent bookings found.') }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [bookings.map((booking) => (_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex items-center space-x-4", children: [_jsxs(Avatar, { className: "h-9 w-9", children: [_jsx(AvatarImage, { src: booking.customer.avatar, alt: booking.customer.name }), _jsx(AvatarFallback, { children: booking.customer.name
                                            .split(' ')
                                            .map((n) => n[0])
                                            .join('') })] }), _jsxs("div", { className: "space-y-1", children: [_jsx("p", { className: "text-sm font-medium leading-none", children: booking.customer.name }), _jsx("p", { className: "text-sm text-muted-foreground", children: booking.experience }), _jsxs("div", { className: "flex items-center text-xs text-muted-foreground mt-1", children: [_jsx(Calendar, { className: "h-3 w-3 mr-1" }), format(new Date(booking.date), 'MMM d, yyyy'), _jsx("span", { className: "mx-1", children: "\u00B7" }), _jsx(Clock, { className: "h-3 w-3 mr-1" }), booking.time, _jsx("span", { className: "mx-1", children: "\u00B7" }), _jsx(User, { className: "h-3 w-3 mr-1" }), booking.guests, " ", booking.guests > 1 ? t('common.guests', 'guests') : t('common.guest', 'guest')] })] })] }), _jsxs("div", { className: "flex flex-col items-end space-y-1", children: [_jsx(Badge, { className: cn('text-xs', getStatusVariant(booking.status)), children: t(`booking.status.${booking.status}`, booking.status) }), _jsxs("div", { className: "text-sm font-medium", children: ["$", booking.amount.toFixed(2)] })] })] }, booking.id))), _jsx("div", { className: "flex justify-end pt-2", children: _jsx(Button, { variant: "ghost", size: "sm", children: t('common.viewAll', 'View all') }) })] }));
}
export default RecentBookings;
