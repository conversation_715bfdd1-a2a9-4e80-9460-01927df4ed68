import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Home, Calendar, Users, Settings, BookOpen, BarChart2, MessageSquare, LogOut, Menu as MenuIcon, X, } from 'lucide-react';
import { cn } from '@/lib/utils';
export function Sidebar({ user, onLogout }) {
    const { t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);
    const navItems = [
        {
            name: 'dashboard',
            href: '/admin',
            icon: _jsx(Home, { className: "h-5 w-5" }),
        },
        {
            name: 'bookings',
            href: '/admin/bookings',
            icon: _jsx(Calendar, { className: "h-5 w-5" }),
        },
        {
            name: 'availability',
            href: '/admin/availability',
            icon: _jsx(Calendar, { className: "h-5 w-5" }),
        },
        {
            name: 'customers',
            href: '/admin/customers',
            icon: _jsx(Users, { className: "h-5 w-5" }),
        },
        {
            name: 'blog',
            href: '/admin/blog',
            icon: _jsx(BookOpen, { className: "h-5 w-5" }),
        },
        {
            name: 'testimonials',
            href: '/admin/testimonials',
            icon: _jsx(MessageSquare, { className: "h-5 w-5" }),
        },
        {
            name: 'reports',
            href: '/admin/reports',
            icon: _jsx(BarChart2, { className: "h-5 w-5" }),
        },
        {
            name: 'settings',
            href: '/admin/settings',
            icon: _jsx(Settings, { className: "h-5 w-5" }),
        },
    ];
    const handleLogout = () => {
        onLogout();
    };
    return (_jsxs(_Fragment, { children: [_jsx("div", { className: "md:hidden fixed top-4 left-4 z-50", children: _jsxs("button", { onClick: () => setIsOpen(!isOpen), className: "p-2 rounded-md text-gray-500 hover:bg-gray-100 focus:outline-none", children: [_jsx("span", { className: "sr-only", children: isOpen ? t('admin.closeMenu', 'Close menu') : t('admin.openMenu', 'Open menu') }), isOpen ? (_jsx(X, { className: "h-6 w-6" })) : (_jsx(MenuIcon, { className: "h-6 w-6" }))] }) }), _jsx("div", { className: cn('fixed inset-y-0 left-0 z-40 w-64 transform bg-white shadow-lg transition-transform duration-300 ease-in-out md:translate-x-0', isOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'), children: _jsxs("div", { className: "flex h-full flex-col", children: [_jsx("div", { className: "flex h-16 items-center justify-center border-b px-4", children: _jsx("h2", { className: "text-xl font-bold text-gray-800", children: "Sailing Serai" }) }), _jsx("div", { className: "mt-auto border-t border-gray-200 pt-4", children: _jsxs("div", { className: "flex items-center px-4", children: [_jsx("div", { className: "flex-shrink-0", children: _jsx("div", { className: "h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center", children: user.avatar ? (_jsx("img", { className: "h-10 w-10 rounded-full", src: user.avatar, alt: user.name })) : (_jsx("span", { className: "text-gray-600", children: user.name
                                                    .split(' ')
                                                    .map((n) => n[0])
                                                    .join('') })) }) }), _jsxs("div", { className: "ml-3", children: [_jsx("p", { className: "text-sm font-medium text-gray-700", children: user.name }), _jsx("p", { className: "text-xs text-gray-500", children: user.email })] })] }) }), _jsx("nav", { className: "flex-1 space-y-1 overflow-y-auto px-2 py-4", children: navItems.map((item) => (_jsxs(NavLink, { to: item.href, className: ({ isActive }) => cn('flex items-center px-4 py-2.5 text-sm font-medium rounded-md', isActive
                                    ? 'bg-blue-50 text-blue-600'
                                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'), onClick: () => setIsOpen(false), children: [_jsx("span", { className: "mr-3", children: item.icon }), _jsx("span", { children: t(`admin.${item.name}`, item.name) })] }, item.href))) }), _jsx("div", { className: "border-t p-4", children: _jsxs("button", { onClick: handleLogout, className: "flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900", children: [_jsx(LogOut, { className: "mr-3 h-5 w-5" }), _jsx("span", { children: t('admin.signOut', 'Sign out') })] }) })] }) }), isOpen && (_jsx("div", { className: "fixed inset-0 z-30 bg-black bg-opacity-50 md:hidden", onClick: () => setIsOpen(false) }))] }));
}
