import { ArrowUp, ArrowDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ElementType;
  trend?: number;
  isCurrency?: boolean;
  loading?: boolean;
}

export function StatsCard({
  title,
  value,
  icon: Icon,
  trend = 0,
  isCurrency = false,
  loading = false,
}: StatsCardProps) {
  const isPositive = trend >= 0;
  const trendText = isPositive ? `+${trend}%` : `${trend}%`;
  
  if (loading) {
    return (
      <div className="rounded-xl border bg-card text-card-foreground shadow">
        <div className="p-6">
          <div className="flex items-center justify-between space-y-2">
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
          <div className="mt-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="mt-2 h-4 w-1/2" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-xl border bg-card text-card-foreground shadow">
      <div className="p-6">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-muted-foreground">
            {title}
          </h3>
          <div className="rounded-lg p-2 bg-primary/10">
            <Icon className="h-4 w-4 text-primary" />
          </div>
        </div>
        <div className="mt-4">
          <div className="text-2xl font-bold">
            {isCurrency && typeof value !== 'number' && !value.startsWith('$') ? `$${value}` : value}
          </div>
          {trend !== undefined && (
            <div
              className={cn(
                'mt-2 flex items-center text-sm',
                isPositive ? 'text-green-600' : 'text-red-600'
              )}
            >
              {isPositive ? (
                <ArrowUp className="h-4 w-4" />
              ) : (
                <ArrowDown className="h-4 w-4" />
              )}
              <span className="ml-1">{trendText}</span>
              <span className="ml-1 text-muted-foreground">vs last period</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default StatsCard;
