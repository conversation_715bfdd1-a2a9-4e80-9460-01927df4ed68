import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveC<PERSON>r, Legend, } from 'recharts';
import { format } from 'date-fns';
export function RevenueChart({ data }) {
    const { t } = useTranslation();
    // Format the data for the chart
    const chartData = useMemo(() => {
        return data.map((item) => ({
            ...item,
            date: format(new Date(item.date), 'MMM d'),
        }));
    }, [data]);
    // Format the Y-axis tick values
    const formatYAxis = (value) => {
        if (value >= 1000) {
            return `$${value / 1000}k`;
        }
        return `$${value}`;
    };
    // Format the tooltip values
    const formatTooltip = (value, name) => {
        if (name === 'revenue') {
            return [`$${value.toLocaleString()}`, t('admin.revenue', 'Revenue')];
        }
        if (name === 'bookings') {
            return [value, t('admin.bookings', 'Bookings')];
        }
        return [value, name];
    };
    return (_jsx("div", { className: "h-[350px] w-full", children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(LineChart, { data: chartData, margin: { top: 5, right: 20, left: 0, bottom: 5 }, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3", vertical: false }), _jsx(XAxis, { dataKey: "date", axisLine: false, tickLine: false, tick: { fill: '#6b7280' } }), _jsx(YAxis, { yAxisId: "left", orientation: "left", stroke: "#3b82f6", axisLine: false, tickLine: false, tickFormatter: formatYAxis, tick: { fill: '#6b7280' } }), _jsx(YAxis, { yAxisId: "right", orientation: "right", stroke: "#10b981", axisLine: false, tickLine: false, tick: { fill: '#6b7280' }, hide: true }), _jsx(Tooltip, { formatter: formatTooltip, labelFormatter: (label) => t('admin.onDate', 'On {{date}}', { date: label }), contentStyle: {
                            backgroundColor: 'white',
                            border: '1px solid #e5e7eb',
                            borderRadius: '0.5rem',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                            padding: '0.75rem',
                        } }), _jsx(Legend, {}), _jsx(Line, { yAxisId: "left", type: "monotone", dataKey: "revenue", stroke: "#3b82f6", strokeWidth: 2, dot: false, activeDot: { r: 6, strokeWidth: 0 }, name: t('admin.revenue', 'Revenue') }), _jsx(Line, { yAxisId: "right", type: "monotone", dataKey: "bookings", stroke: "#10b981", strokeWidth: 2, dot: false, strokeDasharray: "5 5", name: t('admin.bookings', 'Bookings') })] }) }) }));
}
export default RevenueChart;
