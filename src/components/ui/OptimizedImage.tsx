import { ImgHTMLAttributes, forwardRef, useState, useMemo } from 'react';

interface OptimizedImageProps extends Omit<ImgHTMLAttributes<HTMLImageElement>, 'src'> {
  src: string;
  alt: string;
  width: number;
  height: number;
  className?: string;
  loading?: 'eager' | 'lazy';
  sizes?: string;
  quality?: number;
}

export const OptimizedImage = forwardRef<HTMLImageElement, OptimizedImageProps>(({
  src,
  alt,
  width,
  height,
  className = '',
  loading = 'lazy',
  sizes = '(max-width: 768px) 100vw, 50vw',
  quality = 80,
  ...props
}, ref) => {
  const [imageLoaded, setImageLoaded] = useState(false);

  // Generate responsive image sources
  const { sources, originalSrcSet } = useMemo<{ sources: Array<{type: string, srcSet: string}>, originalSrcSet: string }>(() => {
    const srcWithoutExt = src.replace(/\.[^/.]+$/, '');
    const extension = src.split('.').pop()?.toLowerCase();
    const sources = [];
    
    // Skip if already a WebP or AVIF image
    if (extension === 'webp' || extension === 'avif') {
      return { sources: [], originalSrcSet: '' };
    }
    
    // AVIF (most efficient, but not supported everywhere)
    sources.push({
      type: 'image/avif',
      srcSet: `${srcWithoutExt}.avif?q=${quality} 1x, ${srcWithoutExt}@2x.avif?q=${quality} 2x`,
    });
    
    // WebP (good balance of quality and compatibility)
    sources.push({
      type: 'image/webp',
      srcSet: `${srcWithoutExt}.webp?q=${quality} 1x, ${srcWithoutExt}@2x.webp?q=${quality} 2x`,
    });
    
    // Original image srcSet
    const originalSrcSet = `${src} 1x, ${srcWithoutExt}@2x.${extension} 2x`;
    
    return { sources, originalSrcSet };
  }, [quality, src]);

  // Don't process SVG files
  const isSvg = src.toLowerCase().endsWith('.svg');
  const shouldUsePicture = !isSvg && sources.length > 0;

  const imageProps: ImgHTMLAttributes<HTMLImageElement> = {
    width,
    height,
    alt,
    loading: loading === 'eager' ? 'eager' : 'lazy',
    className: `${className} transition-opacity duration-300 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`,
    onLoad: () => setImageLoaded(true),
    ...props,
  };

  if (isSvg || !shouldUsePicture) {
    return (
      <img
        src={src}
        srcSet={originalSrcSet}
        {...imageProps}
      />
    );
  }

  return (
    <picture>
      {sources.map((source: {type: string, srcSet: string}, index: number) => (
        <source
          key={index}
          type={source.type}
          srcSet={source.srcSet}
          sizes={sizes}
        />
      ))}
      <source
        type={`image/${src.split('.').pop()}`}
        srcSet={originalSrcSet}
        sizes={sizes}
      />
      <img
        ref={ref}
        src={src}
        {...imageProps}
      />
    </picture>
  );
});

OptimizedImage.displayName = 'OptimizedImage';

export default OptimizedImage;
