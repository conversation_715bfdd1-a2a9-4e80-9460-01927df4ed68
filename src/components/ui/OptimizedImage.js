import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { forwardRef, useState, useMemo } from 'react';
export const OptimizedImage = forwardRef(({ src, alt, width, height, className = '', loading = 'lazy', sizes = '(max-width: 768px) 100vw, 50vw', quality = 80, ...props }, ref) => {
    const [imageLoaded, setImageLoaded] = useState(false);
    // Generate responsive image sources
    const { sources, originalSrcSet } = useMemo(() => {
        const srcWithoutExt = src.replace(/\.[^/.]+$/, '');
        const extension = src.split('.').pop()?.toLowerCase();
        const sources = [];
        // Skip if already a WebP or AVIF image
        if (extension === 'webp' || extension === 'avif') {
            return { sources: [], originalSrcSet: '' };
        }
        // AVIF (most efficient, but not supported everywhere)
        sources.push({
            type: 'image/avif',
            srcSet: `${srcWithoutExt}.avif?q=${quality} 1x, ${srcWithoutExt}@2x.avif?q=${quality} 2x`,
        });
        // WebP (good balance of quality and compatibility)
        sources.push({
            type: 'image/webp',
            srcSet: `${srcWithoutExt}.webp?q=${quality} 1x, ${srcWithoutExt}@2x.webp?q=${quality} 2x`,
        });
        // Original image srcSet
        const originalSrcSet = `${src} 1x, ${srcWithoutExt}@2x.${extension} 2x`;
        return { sources, originalSrcSet };
    }, [quality, src]);
    // Don't process SVG files
    const isSvg = src.toLowerCase().endsWith('.svg');
    const shouldUsePicture = !isSvg && sources.length > 0;
    const imageProps = {
        width,
        height,
        alt,
        loading: loading === 'eager' ? 'eager' : 'lazy',
        className: `${className} transition-opacity duration-300 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`,
        onLoad: () => setImageLoaded(true),
        ...props,
    };
    if (isSvg || !shouldUsePicture) {
        return (_jsx("img", { src: src, srcSet: originalSrcSet, ...imageProps }));
    }
    return (_jsxs("picture", { children: [sources.map((source, index) => (_jsx("source", { type: source.type, srcSet: source.srcSet, sizes: sizes }, index))), _jsx("source", { type: `image/${src.split('.').pop()}`, srcSet: originalSrcSet, sizes: sizes }), _jsx("img", { ref: ref, src: src, ...imageProps })] }));
});
OptimizedImage.displayName = 'OptimizedImage';
export default OptimizedImage;
