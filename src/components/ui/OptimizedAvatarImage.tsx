import * as React from "react"
import { AvatarImage as AvatarImagePrimitive } from "@radix-ui/react-avatar"
import { cn } from "@/lib/utils"
import { OptimizedImage, OptimizedImageProps } from "./OptimizedImage"

export interface OptimizedAvatarImageProps 
  extends React.ComponentPropsWithoutRef<typeof AvatarImagePrimitive>,
    Omit<OptimizedImageProps, 'as'> {
  // Additional props can be added here if needed
}

const OptimizedAvatarImage = React.forwardRef<
  React.ElementRef<typeof AvatarImagePrimitive>,
  OptimizedAvatarImageProps
>(({ className, ...props }, ref) => {
  // Extract OptimizedImage specific props
  const { 
    width = 40, // Default avatar size
    height = 40,
    sizes = '40px',
    loading = 'lazy',
    decoding = 'async',
    ...rest
  } = props

  return (
    <AvatarImagePrimitive
      ref={ref}
      asChild
      className={cn("aspect-square h-full w-full", className)}
    >
      <OptimizedImage
        width={width}
        height={height}
        sizes={sizes}
        loading={loading}
        decoding={decoding}
        className="object-cover"
        {...rest}
      />
    </AvatarImagePrimitive>
  )
})

OptimizedAvatarImage.displayName = "OptimizedAvatarImage"

export { OptimizedAvatarImage }
