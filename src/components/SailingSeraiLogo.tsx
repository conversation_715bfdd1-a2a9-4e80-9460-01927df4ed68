import { SVGProps } from 'react';

interface SailingSeraiLogoProps extends SVGProps<SVGSVGElement> {
  className?: string;
}

export function SailingSeraiLogo({ className, ...props }: SailingSeraiLogoProps) {
  return (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M12 2L4 8l8 6 8-6-8-6zM4 16l8 6 8-6M4 10l8 6 8-6"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
