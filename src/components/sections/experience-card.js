import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { OptimizedImage } from '@/components/ui/OptimizedImage';
import { ArrowRightIcon, YachtIcon, ClockIcon } from '../icons';
export function ExperienceCard({ title, description, duration, price, image, featured = false, className, }) {
    const { t } = useTranslation();
    return (_jsxs("div", { className: cn("group relative bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl", featured && "border-2 border-blue-500", className), children: [_jsxs("div", { className: "relative h-48 overflow-hidden", children: [_jsx(OptimizedImage, { src: image, alt: title, width: 400, height: 192, className: "w-full h-full object-cover transition-transform duration-500 group-hover:scale-105", loading: "lazy" }), featured && (_jsx("div", { className: "absolute top-4 right-4 bg-blue-500 text-white text-xs font-semibold px-3 py-1 rounded-full", children: t('experiences.featured', 'Featured') }))] }), _jsxs("div", { className: "p-6", children: [_jsxs("div", { className: "flex justify-between items-start mb-4", children: [_jsx("h3", { className: "text-xl font-bold text-gray-900", children: title }), _jsx("div", { className: "text-blue-600 font-bold", children: price })] }), _jsx("p", { className: "text-gray-600 mb-4", children: description }), _jsxs("div", { className: "flex items-center text-sm text-gray-500 mb-6", children: [_jsx(ClockIcon, { className: "h-4 w-4 mr-1" }), _jsx("span", { children: duration }), _jsx("span", { className: "mx-2", children: "\u2022" }), _jsx(YachtIcon, { className: "h-4 w-4 mr-1" }), _jsx("span", { children: t('experctions.maxGuests', 'Up to 6 guests') })] }), _jsxs("div", { className: "flex flex-col sm:flex-row gap-3", children: [_jsxs(Button, { className: "flex-1", children: [t('common.bookNow', 'Book Now'), _jsx(ArrowRightIcon, { className: "ml-2 h-4 w-4" })] }), _jsx(Button, { variant: "outline", className: "flex-1", children: t('common.learnMore', 'Learn More') })] })] })] }));
}
export default ExperienceCard;
