import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { OptimizedImage } from '@/components/ui/OptimizedImage';
import { ArrowRightIcon, YachtIcon, ClockIcon } from '../icons';

interface ExperienceCardProps {
  title: string;
  description: string;
  duration: string;
  price: string;
  image: string;
  featured?: boolean;
  className?: string;
}

export function ExperienceCard({
  title,
  description,
  duration,
  price,
  image,
  featured = false,
  className,
}: ExperienceCardProps) {
  const { t } = useTranslation();
  
  return (
    <div className={cn(
      "group relative bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl",
      featured && "border-2 border-blue-500",
      className
    )}>
      {/* Image */}
      <div className="relative h-48 overflow-hidden">
        <OptimizedImage 
          src={image}
          alt={title}
          width={400}
          height={192}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
          loading="lazy"
        />
        {featured && (
          <div className="absolute top-4 right-4 bg-blue-500 text-white text-xs font-semibold px-3 py-1 rounded-full">
            {t('experiences.featured', 'Featured')}
          </div>
        )}
      </div>
      
      {/* Content */}
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-xl font-bold text-gray-900">{title}</h3>
          <div className="text-blue-600 font-bold">{price}</div>
        </div>
        
        <p className="text-gray-600 mb-4">{description}</p>
        
        <div className="flex items-center text-sm text-gray-500 mb-6">
          <ClockIcon className="h-4 w-4 mr-1" />
          <span>{duration}</span>
          <span className="mx-2">•</span>
          <YachtIcon className="h-4 w-4 mr-1" />
          <span>{t('experctions.maxGuests', 'Up to 6 guests')}</span>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <Button className="flex-1">
            {t('common.bookNow', 'Book Now')}
            <ArrowRightIcon className="ml-2 h-4 w-4" />
          </Button>
          <Button variant="outline" className="flex-1">
            {t('common.learnMore', 'Learn More')}
          </Button>
        </div>
      </div>
    </div>
  );
}

export default ExperienceCard;
