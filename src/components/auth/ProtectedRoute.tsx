import { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'admin' | 'staff';
}

export function ProtectedRoute({ children, requiredRole = 'admin' }: ProtectedRouteProps) {
  const { isAuthenticated, user, isLoading, checkAuth } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);
  const location = useLocation();
  const { t } = useTranslation();

  useEffect(() => {
    const verifyAuth = async () => {
      try {
        const isAuth = await checkAuth();
        if (isAuth && user) {
          // Check if user has the required role
          const hasRole = user.role === requiredRole || user.role === 'admin';
          setIsAuthorized(hasRole);
        } else {
          setIsAuthorized(false);
        }
      } catch (error) {
        console.error('Auth verification error:', error);
        setIsAuthorized(false);
      }
    };

    verifyAuth();
  }, [checkAuth, user, requiredRole]);

  if (isLoading || isAuthorized === null) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
        <p className="mt-4 text-gray-600">
          {t('auth.verifying', 'Verifying authentication...')}
        </p>
      </div>
    );
  }

  if (!isAuthenticated) {
    // Redirect to login page, but save the current location they were trying to go to
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (!isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4">
        <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">
            {t('auth.unauthorized', 'Unauthorized Access')}
          </h2>
          <p className="text-gray-600 mb-6">
            {t('auth.insufficientPermissions', 'You do not have permission to access this page.')}
          </p>
          <a
            href="/admin"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {t('auth.returnToDashboard', 'Return to Dashboard')}
          </a>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

export default ProtectedRoute;
