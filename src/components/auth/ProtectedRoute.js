import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
export function ProtectedRoute({ children, requiredRole = 'admin' }) {
    const { isAuthenticated, user, isLoading, checkAuth } = useAuth();
    const [isAuthorized, setIsAuthorized] = useState(null);
    const location = useLocation();
    const { t } = useTranslation();
    useEffect(() => {
        const verifyAuth = async () => {
            try {
                const isAuth = await checkAuth();
                if (isAuth && user) {
                    // Check if user has the required role
                    const hasRole = user.role === requiredRole || user.role === 'admin';
                    setIsAuthorized(hasRole);
                }
                else {
                    setIsAuthorized(false);
                }
            }
            catch (error) {
                console.error('Auth verification error:', error);
                setIsAuthorized(false);
            }
        };
        verifyAuth();
    }, [checkAuth, user, requiredRole]);
    if (isLoading || isAuthorized === null) {
        return (_jsxs("div", { className: "flex flex-col items-center justify-center min-h-screen bg-gray-50", children: [_jsx(Loader2, { className: "h-12 w-12 animate-spin text-blue-600" }), _jsx("p", { className: "mt-4 text-gray-600", children: t('auth.verifying', 'Verifying authentication...') })] }));
    }
    if (!isAuthenticated) {
        // Redirect to login page, but save the current location they were trying to go to
        return _jsx(Navigate, { to: "/login", state: { from: location }, replace: true });
    }
    if (!isAuthorized) {
        return (_jsx("div", { className: "flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4", children: _jsxs("div", { className: "max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center", children: [_jsx("h2", { className: "text-2xl font-bold text-red-600 mb-4", children: t('auth.unauthorized', 'Unauthorized Access') }), _jsx("p", { className: "text-gray-600 mb-6", children: t('auth.insufficientPermissions', 'You do not have permission to access this page.') }), _jsx("a", { href: "/admin", className: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500", children: t('auth.returnToDashboard', 'Return to Dashboard') })] }) }));
    }
    return _jsx(_Fragment, { children: children });
}
export default ProtectedRoute;
