import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { BlogPost } from '@/types/blog';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Calendar, Clock, Tag, User } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Skeleton } from '@/components/ui/skeleton';
import { OptimizedImage } from '@/components/ui/OptimizedImage';

interface BlogPostDetailProps {
  post: BlogPost | null;
  isLoading?: boolean;
  error?: string | null;
  isAdmin?: boolean;
  onEdit?: () => void;
  onDelete?: () => void;
}

export const BlogPostDetail: React.FC<BlogPostDetailProps> = ({
  post,
  isLoading = false,
  error = null,
  isAdmin = false,
  onEdit,
  onDelete,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (window.confirm(t('blog.deleteConfirm', 'Are you sure you want to delete this post?'))) {
      try {
        setIsDeleting(true);
        onDelete?.();
      } catch (error) {
        console.error('Error deleting post:', error);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <Button
          variant="ghost"
          onClick={() => navigate(-1)}
          className="mb-6"
          disabled={isDeleting}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('common.back', 'Back')}
        </Button>
        
        <div className="space-y-6">
          <Skeleton className="h-10 w-3/4" />
          <div className="flex items-center space-x-4">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-4 w-20" />
          </div>
          <Skeleton className="h-64 w-full" />
          <div className="space-y-4">
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-5/6" />
            <Skeleton className="h-6 w-4/6" />
            <Skeleton className="h-6 w-5/6" />
            <Skeleton className="h-6 w-3/4" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8 text-center">
        <h2 className="text-2xl font-bold text-red-600 mb-4">
          {t('blog.errorLoadingPost', 'Error loading post')}
        </h2>
        <p className="text-muted-foreground mb-6">{error}</p>
        <Button onClick={() => navigate('/blog')}>
          {t('blog.backToBlog', 'Back to Blog')}
        </Button>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8 text-center">
        <h2 className="text-2xl font-bold mb-4">
          {t('blog.postNotFound', 'Post not found')}
        </h2>
        <p className="text-muted-foreground mb-6">
          {t('blog.postNotFoundDescription', 'The requested blog post could not be found.')}
        </p>
        <Button onClick={() => navigate('/blog')}>
          {t('blog.backToBlog', 'Back to Blog')}
        </Button>
      </div>
    );
  }

  // Calculate reading time (assuming 200 words per minute)
  const words = post.content.trim().split(/\s+/).length;
  const readingTime = Math.ceil(words / 200);

  return (
    <article className="max-w-4xl mx-auto px-4 py-8">
      <Button
        variant="ghost"
        onClick={() => navigate(-1)}
        className="mb-6"
        disabled={isDeleting}
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        {t('common.back', 'Back')}
      </Button>

      <header className="mb-8">
        {post.status === 'draft' && (
          <span className="inline-block px-3 py-1 text-sm font-medium bg-yellow-100 text-yellow-800 rounded-full mb-4">
            {t('blog.draft', 'Draft')}
          </span>
        )}
        
        <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">
          {post.title}
        </h1>
        
        <div className="flex flex-wrap items-center text-sm text-muted-foreground gap-4 mb-6">
          <div className="flex items-center">
            <User className="h-4 w-4 mr-1" />
            <span>{post.author.name}</span>
          </div>
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-1" />
            <time dateTime={post.publishedAt}>
              {format(new Date(post.publishedAt), 'MMMM d, yyyy')}
            </time>
          </div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-1" />
            <span>
              {readingTime} {t('blog.minRead', 'min read')}
            </span>
          </div>
        </div>

        {post.tags && post.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-6">
            {post.tags.map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary"
              >
                <Tag className="h-3 w-3 mr-1" />
                {tag}
              </span>
            ))}
          </div>
        )}

        {post.featuredImage && (
          <div className="rounded-lg overflow-hidden mb-8">
            <OptimizedImage
              src={post.featuredImage}
              alt={post.title}
              width={1200}
              height={630}
              sizes="(max-width: 768px) 100vw, 80vw"
              className="w-full h-auto max-h-[500px] object-cover"
              loading="eager"
            />
          </div>
        )}
      </header>

      <div className="prose prose-lg max-w-none dark:prose-invert">
        <div dangerouslySetInnerHTML={{ __html: post.content }} />
      </div>

      {isAdmin && (
        <div className="mt-12 pt-6 border-t flex justify-end space-x-4">
          <Button
            variant="outline"
            onClick={onEdit}
            disabled={isDeleting}
          >
            {t('common.edit', 'Edit')}
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <span className="mr-2">
                  <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                </span>
                {t('common.deleting', 'Deleting...')}
              </>
            ) : (
              t('common.delete', 'Delete')
            )}
          </Button>
        </div>
      )}
    </article>
  );
};

export default BlogPostDetail;
