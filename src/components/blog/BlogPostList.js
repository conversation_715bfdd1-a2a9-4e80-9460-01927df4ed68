import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { format } from 'date-fns';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
export const BlogPostList = ({ posts, onEdit, onDelete, adminView = false }) => {
    if (posts.length === 0) {
        return (_jsxs("div", { className: "text-center py-12", children: [_jsx("h3", { className: "text-lg font-medium", children: "No blog posts found" }), _jsx("p", { className: "text-muted-foreground mt-2", children: "Check back later for updates!" })] }));
    }
    return (_jsx("div", { className: "grid gap-6 md:grid-cols-2 lg:grid-cols-3", children: posts.map((post) => (_jsxs(Card, { className: "flex flex-col h-full", children: [post.featuredImage && (_jsx("div", { className: "h-48 overflow-hidden", children: _jsx("img", { src: post.featuredImage, alt: post.title, className: "w-full h-full object-cover" }) })), _jsx(CardHeader, { children: _jsxs("div", { className: "flex justify-between items-start", children: [_jsxs("div", { children: [_jsx(CardTitle, { className: "text-xl", children: adminView ? (_jsx(Link, { to: `/admin/blog/edit/${post.id}`, className: "hover:underline", children: post.title })) : (_jsx(Link, { to: `/blog/${post.slug}`, className: "hover:underline", children: post.title })) }), _jsxs(CardDescription, { className: "mt-1", children: [format(new Date(post.publishedAt), 'MMMM d, yyyy'), " \u2022 ", post.author.name] })] }), post.status === 'draft' && (_jsx("span", { className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800", children: "Draft" }))] }) }), _jsx(CardContent, { className: "flex-grow", children: _jsx("p", { className: "text-muted-foreground line-clamp-3", children: post.excerpt }) }), _jsxs(CardFooter, { className: "flex justify-between", children: [_jsx(Button, { asChild: true, variant: "link", className: "p-0 h-auto", children: _jsx(Link, { to: adminView ? `/admin/blog/edit/${post.id}` : `/blog/${post.slug}`, children: "Read more" }) }), adminView && onEdit && onDelete && (_jsxs("div", { className: "space-x-2", children: [_jsx(Button, { variant: "outline", size: "sm", onClick: () => onEdit(post), children: "Edit" }), _jsx(Button, { variant: "destructive", size: "sm", onClick: () => onDelete(post.id), children: "Delete" })] }))] })] }, post.id))) }));
};
export default BlogPostList;
