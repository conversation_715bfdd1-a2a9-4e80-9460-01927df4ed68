import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Loader2, Image as ImageIcon, X } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { uploadFeaturedImage } from '@/api/blog';
const blogPostSchema = z.object({
    title: z.string().min(1, 'Title is required').max(100, 'Title is too long'),
    slug: z.string().min(1, 'Slug is required').regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
    excerpt: z.string().min(1, 'Excerpt is required').max(300, 'Excerpt is too long'),
    content: z.string().min(1, 'Content is required'),
    featuredImage: z.string().optional(),
    tags: z.array(z.string()).optional(),
    status: z.enum(['draft', 'published', 'archived']).default('draft'),
});
export const BlogPostForm = ({ initialData, onSubmit, isSubmitting, }) => {
    const [isUploading, setIsUploading] = useState(false);
    const [imagePreview, setImagePreview] = useState(initialData?.featuredImage || null);
    const [tags, setTags] = useState(initialData?.tags || []);
    const [tagInput, setTagInput] = useState('');
    const { register, handleSubmit, control, setValue, watch, formState: { errors }, } = useForm({
        resolver: zodResolver(blogPostSchema),
        defaultValues: {
            title: initialData?.title || '',
            slug: initialData?.slug || '',
            excerpt: initialData?.excerpt || '',
            content: initialData?.content || '',
            featuredImage: initialData?.featuredImage || '',
            tags: initialData?.tags || [],
            status: initialData?.status || 'draft',
        },
    });
    // Watch slug to generate from title if empty
    const title = watch('title');
    useEffect(() => {
        if (!initialData?.slug && title) {
            const slug = title
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '');
            setValue('slug', slug, { shouldValidate: true });
        }
    }, [title, setValue, initialData]);
    // Handle image upload
    const handleImageUpload = async (e) => {
        const file = e.target.files?.[0];
        if (!file)
            return;
        // Check file type
        if (!file.type.startsWith('image/')) {
            toast({
                title: 'Invalid file type',
                description: 'Please upload an image file (JPEG, PNG, etc.)',
                variant: 'destructive',
            });
            return;
        }
        // Check file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            toast({
                title: 'File too large',
                description: 'Please upload an image smaller than 5MB',
                variant: 'destructive',
            });
            return;
        }
        try {
            setIsUploading(true);
            const result = await uploadFeaturedImage(file);
            setValue('featuredImage', result.url, { shouldValidate: true });
            setImagePreview(URL.createObjectURL(file));
            toast({
                title: 'Image uploaded successfully',
                variant: 'default',
            });
        }
        catch (error) {
            console.error('Error uploading image:', error);
            toast({
                title: 'Error uploading image',
                description: 'Please try again',
                variant: 'destructive',
            });
        }
        finally {
            setIsUploading(false);
        }
    };
    // Handle tag input
    const handleAddTag = (e) => {
        if (e.key === 'Enter' || e.key === ',') {
            e.preventDefault();
            const newTag = tagInput.trim().toLowerCase();
            if (newTag && !tags.includes(newTag)) {
                const newTags = [...tags, newTag];
                setTags(newTags);
                setValue('tags', newTags, { shouldValidate: true });
                setTagInput('');
            }
        }
    };
    const handleRemoveTag = (tagToRemove) => {
        const newTags = tags.filter((tag) => tag !== tagToRemove);
        setTags(newTags);
        setValue('tags', newTags, { shouldValidate: true });
    };
    const handleFormSubmit = (data) => {
        onSubmit({
            ...data,
            tags: tags,
        });
    };
    return (_jsx("form", { onSubmit: handleSubmit(handleFormSubmit), className: "space-y-6", children: _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-6", children: [_jsxs("div", { className: "md:col-span-2 space-y-6", children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "title", children: "Title *" }), _jsx(Input, { id: "title", ...register('title'), placeholder: "Enter post title", disabled: isSubmitting }), errors.title && (_jsx("p", { className: "text-sm text-red-500 mt-1", children: errors.title.message }))] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "slug", children: "Slug *" }), _jsx(Input, { id: "slug", ...register('slug'), placeholder: "post-slug", disabled: isSubmitting }), errors.slug && (_jsx("p", { className: "text-sm text-red-500 mt-1", children: errors.slug.message })), _jsx("p", { className: "text-xs text-muted-foreground mt-1", children: "This is the URL-friendly version of the title" })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "excerpt", children: "Excerpt *" }), _jsx(Textarea, { id: "excerpt", ...register('excerpt'), placeholder: "A short excerpt for the blog post", rows: 3, disabled: isSubmitting }), errors.excerpt && (_jsx("p", { className: "text-sm text-red-500 mt-1", children: errors.excerpt.message }))] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "content", children: "Content *" }), _jsx(Textarea, { id: "content", ...register('content'), placeholder: "Write your blog post content here...", rows: 10, className: "font-mono text-sm", disabled: isSubmitting }), errors.content && (_jsx("p", { className: "text-sm text-red-500 mt-1", children: errors.content.message }))] })] }), _jsxs("div", { className: "space-y-6", children: [_jsxs("div", { children: [_jsx(Label, { children: "Featured Image" }), _jsxs("div", { className: "mt-2", children: [imagePreview ? (_jsxs("div", { className: "relative group", children: [_jsx("img", { src: imagePreview, alt: "Preview", className: "w-full h-48 object-cover rounded-md" }), _jsx("button", { type: "button", onClick: () => {
                                                        setImagePreview(null);
                                                        setValue('featuredImage', '', { shouldValidate: true });
                                                    }, className: "absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity", children: _jsx(X, { className: "h-4 w-4" }) })] })) : (_jsxs("div", { className: "border-2 border-dashed rounded-md p-6 text-center", children: [_jsx("div", { className: "flex justify-center", children: _jsx(ImageIcon, { className: "h-10 w-10 text-muted-foreground" }) }), _jsxs("div", { className: "mt-2", children: [_jsx(Label, { htmlFor: "featured-image", className: "cursor-pointer text-primary hover:text-primary/80", children: "Upload an image" }), _jsx("input", { id: "featured-image", type: "file", accept: "image/*", className: "sr-only", onChange: handleImageUpload, disabled: isUploading || isSubmitting })] }), _jsx("p", { className: "text-xs text-muted-foreground mt-1", children: "JPG, PNG up to 5MB" })] })), isUploading && (_jsxs("div", { className: "mt-2 text-sm text-muted-foreground flex items-center", children: [_jsx(Loader2, { className: "h-4 w-4 animate-spin mr-2" }), "Uploading..."] }))] })] }), _jsxs("div", { children: [_jsx(Label, { children: "Tags" }), _jsxs("div", { className: "mt-2", children: [_jsx("div", { className: "flex flex-wrap gap-2 mb-2", children: tags.map((tag) => (_jsxs("span", { className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary", children: [tag, _jsx("button", { type: "button", onClick: () => handleRemoveTag(tag), className: "ml-1.5 text-primary/70 hover:text-primary", children: _jsx(X, { className: "h-3 w-3" }) })] }, tag))) }), _jsx(Input, { placeholder: "Add tags...", value: tagInput, onChange: (e) => setTagInput(e.target.value), onKeyDown: handleAddTag, onBlur: (e) => {
                                                if (tagInput.trim()) {
                                                    handleAddTag({
                                                        key: 'Enter',
                                                        preventDefault: () => { },
                                                    });
                                                }
                                            }, disabled: isSubmitting }), _jsx("p", { className: "text-xs text-muted-foreground mt-1", children: "Press enter or comma to add a tag" })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { children: "Status" }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Controller, { name: "status", control: control, render: ({ field }) => (_jsx(Switch, { id: "status", checked: field.value === 'published', onCheckedChange: (checked) => field.onChange(checked ? 'published' : 'draft'), disabled: isSubmitting })) }), _jsx(Label, { htmlFor: "status", className: "!m-0", children: watch('status') === 'published' ? 'Published' : 'Draft' })] })] }), _jsx("div", { className: "pt-4", children: _jsx(Button, { type: "submit", className: "w-full", disabled: isSubmitting, children: isSubmitting ? (_jsxs(_Fragment, { children: [_jsx(Loader2, { className: "mr-2 h-4 w-4 animate-spin" }), initialData ? 'Updating...' : 'Publishing...'] })) : initialData ? ('Update Post') : ('Publish Post') }) })] })] }) }));
};
export default BlogPostForm;
