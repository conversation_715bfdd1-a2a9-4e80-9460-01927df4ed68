import React from 'react';
import { BlogPost } from '@/types/blog';
import { format } from 'date-fns';
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { OptimizedImage } from '@/components/ui/OptimizedImage';

interface BlogPostListProps {
  posts: BlogPost[];
  onEdit?: (post: BlogPost) => void;
  onDelete?: (postId: string) => void;
  adminView?: boolean;
}

export const BlogPostList: React.FC<BlogPostListProps> = ({ 
  posts, 
  onEdit, 
  onDelete, 
  adminView = false 
}) => {
  if (posts.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium">No blog posts found</h3>
        <p className="text-muted-foreground mt-2">Check back later for updates!</p>
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {posts.map((post) => (
        <Card key={post.id} className="flex flex-col h-full">
          {post.featuredImage && (
            <div className="h-48 overflow-hidden">
              <OptimizedImage
                src={post.featuredImage}
                alt={post.title}
                width={400}
                height={192}
                className="w-full h-full object-cover"
                loading="lazy"
              />
            </div>
          )}
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-xl">
                  {adminView ? (
                    <Link to={`/admin/blog/edit/${post.id}`} className="hover:underline">
                      {post.title}
                    </Link>
                  ) : (
                    <Link to={`/blog/${post.slug}`} className="hover:underline">
                      {post.title}
                    </Link>
                  )}
                </CardTitle>
                <CardDescription className="mt-1">
                  {format(new Date(post.publishedAt), 'MMMM d, yyyy')} • {post.author.name}
                </CardDescription>
              </div>
              {post.status === 'draft' && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  Draft
                </span>
              )}
            </div>
          </CardHeader>
          <CardContent className="flex-grow">
            <p className="text-muted-foreground line-clamp-3">{post.excerpt}</p>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button asChild variant="link" className="p-0 h-auto">
              <Link to={adminView ? `/admin/blog/edit/${post.id}` : `/blog/${post.slug}`}>
                Read more
              </Link>
            </Button>
            {adminView && onEdit && onDelete && (
              <div className="space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => onEdit(post)}
                >
                  Edit
                </Button>
                <Button 
                  variant="destructive" 
                  size="sm"
                  onClick={() => onDelete(post.id)}
                >
                  Delete
                </Button>
              </div>
            )}
          </CardFooter>
        </Card>
      ))}
    </div>
  );
};

export default BlogPostList;
