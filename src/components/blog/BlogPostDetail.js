import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Calendar, Clock, Tag, User } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Skeleton } from '@/components/ui/skeleton';
export const BlogPostDetail = ({ post, isLoading = false, error = null, isAdmin = false, onEdit, onDelete, }) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [isDeleting, setIsDeleting] = useState(false);
    const handleDelete = async () => {
        if (window.confirm(t('blog.deleteConfirm', 'Are you sure you want to delete this post?'))) {
            try {
                setIsDeleting(true);
                onDelete?.();
            }
            catch (error) {
                console.error('Error deleting post:', error);
            }
            finally {
                setIsDeleting(false);
            }
        }
    };
    if (isLoading) {
        return (_jsxs("div", { className: "max-w-4xl mx-auto px-4 py-8", children: [_jsxs(Button, { variant: "ghost", onClick: () => navigate(-1), className: "mb-6", disabled: isDeleting, children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), t('common.back', 'Back')] }), _jsxs("div", { className: "space-y-6", children: [_jsx(Skeleton, { className: "h-10 w-3/4" }), _jsxs("div", { className: "flex items-center space-x-4", children: [_jsx(Skeleton, { className: "h-4 w-24" }), _jsx(Skeleton, { className: "h-4 w-32" }), _jsx(Skeleton, { className: "h-4 w-20" })] }), _jsx(Skeleton, { className: "h-64 w-full" }), _jsxs("div", { className: "space-y-4", children: [_jsx(Skeleton, { className: "h-6 w-full" }), _jsx(Skeleton, { className: "h-6 w-5/6" }), _jsx(Skeleton, { className: "h-6 w-4/6" }), _jsx(Skeleton, { className: "h-6 w-5/6" }), _jsx(Skeleton, { className: "h-6 w-3/4" })] })] })] }));
    }
    if (error) {
        return (_jsxs("div", { className: "max-w-4xl mx-auto px-4 py-8 text-center", children: [_jsx("h2", { className: "text-2xl font-bold text-red-600 mb-4", children: t('blog.errorLoadingPost', 'Error loading post') }), _jsx("p", { className: "text-muted-foreground mb-6", children: error }), _jsx(Button, { onClick: () => navigate('/blog'), children: t('blog.backToBlog', 'Back to Blog') })] }));
    }
    if (!post) {
        return (_jsxs("div", { className: "max-w-4xl mx-auto px-4 py-8 text-center", children: [_jsx("h2", { className: "text-2xl font-bold mb-4", children: t('blog.postNotFound', 'Post not found') }), _jsx("p", { className: "text-muted-foreground mb-6", children: t('blog.postNotFoundDescription', 'The requested blog post could not be found.') }), _jsx(Button, { onClick: () => navigate('/blog'), children: t('blog.backToBlog', 'Back to Blog') })] }));
    }
    // Calculate reading time (assuming 200 words per minute)
    const words = post.content.trim().split(/\s+/).length;
    const readingTime = Math.ceil(words / 200);
    return (_jsxs("article", { className: "max-w-4xl mx-auto px-4 py-8", children: [_jsxs(Button, { variant: "ghost", onClick: () => navigate(-1), className: "mb-6", disabled: isDeleting, children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), t('common.back', 'Back')] }), _jsxs("header", { className: "mb-8", children: [post.status === 'draft' && (_jsx("span", { className: "inline-block px-3 py-1 text-sm font-medium bg-yellow-100 text-yellow-800 rounded-full mb-4", children: t('blog.draft', 'Draft') })), _jsx("h1", { className: "text-3xl md:text-4xl font-bold tracking-tight mb-4", children: post.title }), _jsxs("div", { className: "flex flex-wrap items-center text-sm text-muted-foreground gap-4 mb-6", children: [_jsxs("div", { className: "flex items-center", children: [_jsx(User, { className: "h-4 w-4 mr-1" }), _jsx("span", { children: post.author.name })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Calendar, { className: "h-4 w-4 mr-1" }), _jsx("time", { dateTime: post.publishedAt, children: format(new Date(post.publishedAt), 'MMMM d, yyyy') })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Clock, { className: "h-4 w-4 mr-1" }), _jsxs("span", { children: [readingTime, " ", t('blog.minRead', 'min read')] })] })] }), post.tags && post.tags.length > 0 && (_jsx("div", { className: "flex flex-wrap gap-2 mb-6", children: post.tags.map((tag) => (_jsxs("span", { className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary", children: [_jsx(Tag, { className: "h-3 w-3 mr-1" }), tag] }, tag))) })), post.featuredImage && (_jsx("div", { className: "rounded-lg overflow-hidden mb-8", children: _jsx("img", { src: post.featuredImage, alt: post.title, className: "w-full h-auto max-h-[500px] object-cover" }) }))] }), _jsx("div", { className: "prose prose-lg max-w-none dark:prose-invert", children: _jsx("div", { dangerouslySetInnerHTML: { __html: post.content } }) }), isAdmin && (_jsxs("div", { className: "mt-12 pt-6 border-t flex justify-end space-x-4", children: [_jsx(Button, { variant: "outline", onClick: onEdit, disabled: isDeleting, children: t('common.edit', 'Edit') }), _jsx(Button, { variant: "destructive", onClick: handleDelete, disabled: isDeleting, children: isDeleting ? (_jsxs(_Fragment, { children: [_jsx("span", { className: "mr-2", children: _jsxs("svg", { className: "animate-spin h-4 w-4", viewBox: "0 0 24 24", children: [_jsx("circle", { className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4" }), _jsx("path", { className: "opacity-75", fill: "currentColor", d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" })] }) }), t('common.deleting', 'Deleting...')] })) : (t('common.delete', 'Delete')) })] }))] }));
};
export default BlogPostDetail;
