import React, { useEffect, useRef, useState, useCallback } from 'react';
import { X, ChevronLeft, ChevronRight, Maximize2, Minimize2, Download, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { LightboxImage } from '@/types/gallery';
import { cn } from '@/lib/utils';
import { OptimizedImage } from '@/components/ui/OptimizedImage';

interface LightboxProps {
  isOpen: boolean;
  onClose: () => void;
  images: LightboxImage[];
  initialIndex?: number;
  onIndexChange?: (index: number) => void;
  showThumbnails?: boolean;
  showCaption?: boolean;
  showDownload?: boolean;
  showFullscreen?: boolean;
  className?: string;
}

export const Lightbox: React.FC<LightboxProps> = ({
  isOpen,
  onClose,
  images,
  initialIndex = 0,
  onIndexChange,
  showThumbnails = true,
  showCaption = true,
  showDownload = true,
  showFullscreen = true,
  className = '',
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [touchStartX, setTouchStartX] = useState(0);
  const [touchEndX, setTouchEndX] = useState(0);
  const imageRef = useRef<HTMLImageElement>(null);
  const dialogRef = useRef<HTMLDivElement>(null);
  
  const currentImage = images[currentIndex];
  const hasMultipleImages = images.length > 1;
  
  // Handle keyboard navigation
  useEffect(() => {
    if (!isOpen) return;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          if (hasMultipleImages) goToPrevious();
          break;
        case 'ArrowRight':
          if (hasMultipleImages) goToNext();
          break;
        case 'f':
        case 'F':
          toggleFullscreen();
          break;
        case 'i':
        case 'I':
          setShowInfo(prev => !prev);
          break;
        case ' ':
          // Prevent space from scrolling the page
          e.preventDefault();
          break;
        default:
          break;
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentIndex, hasMultipleImages, onClose]);
  
  // Handle fullscreen change
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);
  
  // Update current index when initialIndex changes
  useEffect(() => {
    setCurrentIndex(initialIndex);
  }, [initialIndex]);
  
  // Notify parent of index change
  useEffect(() => {
    if (onIndexChange) {
      onIndexChange(currentIndex);
    }
  }, [currentIndex, onIndexChange]);
  
  // Reset loading state when image changes
  useEffect(() => {
    setIsLoading(true);
  }, [currentImage?.id]);
  
  // Navigation functions
  const goToPrevious = useCallback(() => {
    if (!hasMultipleImages) return;
    setCurrentIndex(prev => (prev <= 0 ? images.length - 1 : prev - 1));
  }, [hasMultipleImages, images.length]);
  
  const goToNext = useCallback(() => {
    if (!hasMultipleImages) return;
    setCurrentIndex(prev => (prev >= images.length - 1 ? 0 : prev + 1));
  }, [hasMultipleImages, images.length]);
  
  const goToImage = (index: number) => {
    if (index >= 0 && index < images.length) {
      setCurrentIndex(index);
    }
  };
  
  // Toggle fullscreen
  const toggleFullscreen = async () => {
    if (!document.fullscreenElement) {
      await dialogRef.current?.requestFullscreen().catch(console.error);
    } else {
      await document.exitFullscreen().catch(console.error);
    }
  };
  
  // Handle image load
  const handleImageLoad = () => {
    setIsLoading(false);
  };
  
  // Handle touch events for swipe navigation
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX);
    setTouchEndX(e.touches[0].clientX);
  };
  
  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEndX(e.touches[0].clientX);
  };
  
  const handleTouchEnd = () => {
    if (!touchStartX || !touchEndX) return;
    
    const diff = touchStartX - touchEndX;
    const swipeThreshold = 50; // Minimum distance for swipe
    
    if (Math.abs(diff) > swipeThreshold) {
      if (diff > 0) {
        goToNext();
      } else {
        goToPrevious();
      }
    }
    
    // Reset touch coordinates
    setTouchStartX(0);
    setTouchEndX(0);
  };
  
  // Download image
  const downloadImage = async () => {
    if (!currentImage) return;
    
    try {
      const response = await fetch(currentImage.src);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `sailing-serai-${currentImage.id}.jpg`;
      document.body.appendChild(a);
      a.click();
      
      window.URL.revokeObjectURL(url);
      a.remove();
    } catch (error) {
      console.error('Error downloading image:', error);
    }
  };
  
  if (!isOpen || !currentImage) return null;
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent 
        ref={dialogRef}
        className={cn(
          'fixed inset-0 w-full h-full max-w-none rounded-none p-0 bg-black/95',
          'border-0 flex flex-col',
          'focus:outline-none',
          className
        )}
        onInteractOutside={(e) => {
          // Prevent closing when clicking on navigation buttons
          const target = e.target as HTMLElement;
          if (target.closest('.lightbox-nav-button')) {
            e.preventDefault();
          }
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-b from-black/80 to-transparent z-10">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white hover:bg-white/10 focus:ring-2 focus:ring-white/20"
              aria-label="Close lightbox"
            >
              <X className="h-6 w-6" />
            </Button>
            
            {hasMultipleImages && (
              <div className="text-white text-sm font-medium">
                {currentIndex + 1} / {images.length}
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {showInfo && currentImage.metadata && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowInfo(false)}
                className="text-white hover:bg-white/10 focus:ring-2 focus:ring-white/20"
                aria-label="Hide info"
              >
                <Info className="h-5 w-5" />
              </Button>
            )}
            
            {showDownload && (
              <Button
                variant="ghost"
                size="icon"
                onClick={downloadImage}
                className="text-white hover:bg-white/10 focus:ring-2 focus:ring-white/20"
                aria-label="Download image"
              >
                <Download className="h-5 w-5" />
              </Button>
            )}
            
            {showFullscreen && (
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleFullscreen}
                className="text-white hover:bg-white/10 focus:ring-2 focus:ring-white/20"
                aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
              >
                {isFullscreen ? (
                  <Minimize2 className="h-5 w-5" />
                ) : (
                  <Maximize2 className="h-5 w-5" />
                )}
              </Button>
            )}
          </div>
        </div>
        
        {/* Main content */}
        <div 
          className="flex-1 flex flex-col items-center justify-center relative overflow-hidden"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {/* Loading indicator */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
            </div>
          )}
          
          {/* Main image */}
          <div className="relative w-full h-full flex items-center justify-center">
            <div className={cn(
              'max-w-full max-h-[calc(100vh-200px)] md:max-h-[calc(100vh-250px)]',
              'flex items-center justify-center',
              isLoading ? 'opacity-0' : 'opacity-100'
            )}>
              <OptimizedImage
                ref={imageRef as React.RefObject<HTMLImageElement>}
                src={currentImage.src}
                alt={currentImage.alt}
                width={currentImage.width || 1200}
                height={currentImage.height || 800}
                className="object-contain transition-opacity duration-300 w-full h-full"
                onLoad={handleImageLoad}
                loading="eager"
                draggable={false}
              />
            </div>
            
            {/* Image info overlay */}
            {showInfo && currentImage.metadata && (
              <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white p-4 text-sm">
                <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium mb-2">Image Details</h3>
                    <div className="space-y-1">
                      {currentImage.metadata.camera && (
                        <div>Camera: {currentImage.metadata.camera}</div>
                      )}
                      {currentImage.metadata.lens && (
                        <div>Lens: {currentImage.metadata.lens}</div>
                      )}
                      {currentImage.metadata.focalLength && (
                        <div>Focal Length: {currentImage.metadata.focalLength}</div>
                      )}
                      {currentImage.metadata.aperture && (
                        <div>Aperture: {currentImage.metadata.aperture}</div>
                      )}
                      {currentImage.metadata.shutterSpeed && (
                        <div>Shutter Speed: {currentImage.metadata.shutterSpeed}</div>
                      )}
                      {currentImage.metadata.iso && (
                        <div>ISO: {currentImage.metadata.iso}</div>
                      )}
                    </div>
                  </div>
                  
                  {currentImage.metadata.location && (
                    <div>
                      <h3 className="font-medium mb-2">Location</h3>
                      <div>{currentImage.metadata.location.name || 'Unknown location'}</div>
                      {currentImage.metadata.location.coordinates && (
                        <div className="text-xs text-gray-400 mt-1">
                          {currentImage.metadata.location.coordinates.join(', ')}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          
          {/* Navigation arrows */}
          {hasMultipleImages && (
            <>
              <Button
                variant="ghost"
                size="icon"
                onClick={goToPrevious}
                className={cn(
                  'absolute left-4 top-1/2 -translate-y-1/2',
                  'bg-black/50 hover:bg-black/70 text-white',
                  'h-12 w-12 rounded-full',
                  'lightbox-nav-button',
                  'focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent',
                  'transition-transform hover:scale-110',
                  'hidden sm:flex' // Hide on mobile
                )}
                aria-label="Previous image"
              >
                <ChevronLeft className="h-6 w-6" />
              </Button>
              
              <Button
                variant="ghost"
                size="icon"
                onClick={goToNext}
                className={cn(
                  'absolute right-4 top-1/2 -translate-y-1/2',
                  'bg-black/50 hover:bg-black/70 text-white',
                  'h-12 w-12 rounded-full',
                  'lightbox-nav-button',
                  'focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent',
                  'transition-transform hover:scale-110',
                  'hidden sm:flex' // Hide on mobile
                )}
                aria-label="Next image"
              >
                <ChevronRight className="h-6 w-6" />
              </Button>
              
              {/* Mobile swipe indicators */}
              <div className="sm:hidden absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
                {images.map((_, i) => (
                  <div
                    key={i}
                    className={cn(
                      'h-2 w-2 rounded-full transition-all',
                      i === currentIndex ? 'bg-white w-6' : 'bg-white/50 w-2'
                    )}
                  />
                ))}
              </div>
            </>
          )}
        </div>
        
        {/* Thumbnails */}
        {showThumbnails && hasMultipleImages && images.length > 1 && (
          <div className="bg-gradient-to-t from-black/90 to-transparent p-4 overflow-x-auto">
            <div className="flex space-x-2 justify-center">
              {images.map((img, i) => (
                <button
                  key={img.id}
                  onClick={() => goToImage(i)}
                  className={cn(
                    'relative flex-shrink-0 w-16 h-16 rounded overflow-hidden',
                    'transition-all duration-200',
                    'focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black',
                    i === currentIndex ? 'ring-2 ring-white ring-offset-2 ring-offset-black scale-105' : 'opacity-70 hover:opacity-100'
                  )}
                  aria-label={`Go to image ${i + 1}`}
                >
                  <img
                    src={img.src}
                    alt=""
                    className="w-full h-full object-cover"
                    loading="lazy"
                    width={64}
                    height={64}
                  />
                </button>
              ))}
            </div>
          </div>
        )}
        
        {/* Caption */}
        {showCaption && (currentImage.caption || currentImage.alt) && (
          <div className="bg-black/80 text-white p-4 text-center">
            <div className="max-w-4xl mx-auto">
              {currentImage.caption || currentImage.alt}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default Lightbox;
