import React from 'react';
import { Search, Filter, X, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { GalleryFilters as GalleryFiltersType, GalleryCategory, TripType } from '@/types/gallery';

interface GalleryFiltersProps {
  filters: GalleryFiltersType;
  categories: GalleryCategory[];
  tripTypes: TripType[];
  onFilterChange: (filters: Partial<GalleryFiltersType>) => void;
  onReset: () => void;
  className?: string;
  isLoading?: boolean;
}

export const GalleryFilters: React.FC<GalleryFiltersProps> = ({
  filters,
  categories,
  tripTypes,
  onFilterChange,
  onReset,
  className = '',
  isLoading = false,
}) => {
  const hasActiveFilters = 
    filters.category || 
    filters.tripType || 
    filters.searchQuery || 
    filters.sortBy !== 'newest';
  
  const handleCategoryChange = (value: string) => {
    onFilterChange({ 
      ...filters, 
      category: value === 'all' ? undefined : value 
    });
  };
  
  const handleTripTypeChange = (value: string) => {
    onFilterChange({ 
      ...filters, 
      tripType: value === 'all' ? undefined : value 
    });
  };
  
  const handleSortChange = (value: string) => {
    onFilterChange({ 
      ...filters, 
      sortBy: value as GalleryFiltersType['sortBy'] 
    });
  };
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFilterChange({ 
      ...filters, 
      searchQuery: e.target.value || undefined 
    });
  };
  
  const clearFilters = () => {
    onReset();
  };
  
  const getCategoryName = (id: string) => {
    return categories.find(cat => cat.id === id)?.name || id;
  };
  
  const getTripTypeName = (id: string) => {
    return tripTypes.find(type => type.id === id)?.name || id;
  };
  
  return (
    <div className={cn('space-y-4', className)}>
      {/* Search and sort row */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search images..."
            value={filters.searchQuery || ''}
            onChange={handleSearchChange}
            className="pl-9 w-full"
            disabled={isLoading}
          />
        </div>
        
        <div className="flex items-center gap-2">
          <Select
            value={filters.sortBy || 'newest'}
            onValueChange={handleSortChange}
            disabled={isLoading}
          >
            <SelectTrigger className="w-[180px]">
              <Filter className="h-4 w-4 mr-2 text-muted-foreground" />
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="oldest">Oldest First</SelectItem>
              <SelectItem value="featured">Featured</SelectItem>
              <SelectItem value="random">Random</SelectItem>
            </SelectContent>
          </Select>
          
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearFilters}
              disabled={isLoading}
              className="whitespace-nowrap"
            >
              <X className="h-4 w-4 mr-1" />
              Clear Filters
            </Button>
          )}
        </div>
      </div>
      
      {/* Active filters */}
      {(filters.category || filters.tripType) && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-muted-foreground">Filters:</span>
          
          {filters.category && (
            <Badge 
              variant="secondary" 
              className="flex items-center gap-1 px-2 py-1 text-sm font-normal"
            >
              {getCategoryName(filters.category)}
              <button 
                onClick={() => onFilterChange({ ...filters, category: undefined })}
                className="rounded-full hover:bg-muted p-0.5"
                aria-label={`Remove ${getCategoryName(filters.category)} filter`}
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filters.tripType && (
            <Badge 
              variant="secondary" 
              className="flex items-center gap-1 px-2 py-1 text-sm font-normal"
            >
              {getTripTypeName(filters.tripType)}
              <button 
                onClick={() => onFilterChange({ ...filters, tripType: undefined })}
                className="rounded-full hover:bg-muted p-0.5"
                aria-label={`Remove ${getTripTypeName(filters.tripType)} filter`}
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {(filters.category || filters.tripType) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="h-auto p-0 text-sm text-muted-foreground hover:text-foreground"
            >
              Clear all
            </Button>
          )}
        </div>
      )}
      
      {/* Category and trip type filters */}
      <div className="flex flex-wrap gap-2">
        <Select
          value={filters.category || 'all'}
          onValueChange={handleCategoryChange}
          disabled={isLoading}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue>
              {filters.category ? getCategoryName(filters.category) : 'All Categories'}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                <div className="flex items-center justify-between">
                  <span>{category.name}</span>
                  <Badge variant="outline" className="ml-2">
                    {category.imageCount}
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select
          value={filters.tripType || 'all'}
          onValueChange={handleTripTypeChange}
          disabled={isLoading}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue>
              {filters.tripType ? getTripTypeName(filters.tripType) : 'All Trips'}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Trips</SelectItem>
            {tripTypes.map((tripType) => (
              <SelectItem key={tripType.id} value={tripType.id}>
                <div className="flex items-center justify-between">
                  <span>{tripType.name}</span>
                  <Badge variant="outline" className="ml-2">
                    {tripType.imageCount}
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        {isLoading && (
          <div className="flex items-center text-sm text-muted-foreground ml-2">
            <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            Loading...
          </div>
        )}
      </div>
    </div>
  );
};

export default GalleryFilters;
