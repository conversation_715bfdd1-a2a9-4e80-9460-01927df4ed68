import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { GalleryImage, GalleryFilters as GalleryFiltersType, GalleryApiResponse } from '@/types/gallery';
import { getGalleryImages, getCategories, getTripTypes } from '@/api/gallery';
import { ImageGrid } from './ImageGrid';
import { Lightbox } from './Lightbox';
import { GalleryFilters } from './GalleryFilters';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

export const Gallery: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [categories, setCategories] = useState<GalleryCategory[]>([]);
  const [tripTypes, setTripTypes] = useState<TripType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [totalImages, setTotalImages] = useState(0);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  
  // Initialize filters from URL params or use defaults
  const [filters, setFilters] = useState<GalleryFiltersType>({
    category: searchParams.get('category') || undefined,
    tripType: searchParams.get('tripType') || undefined,
    searchQuery: searchParams.get('q') || undefined,
    sortBy: (searchParams.get('sort') as GalleryFiltersType['sortBy']) || 'newest',
  });
  
  const itemsPerPage = 24; // Number of images to load per page
  
  // Fetch gallery data
  const fetchGalleryData = useCallback(async (resetPagination = false) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const currentPage = resetPagination ? 1 : page;
      
      // Update URL params
      const params = new URLSearchParams();
      if (filters.category) params.set('category', filters.category);
      if (filters.tripType) params.set('tripType', filters.tripType);
      if (filters.searchQuery) params.set('q', filters.searchQuery);
      if (filters.sortBy && filters.sortBy !== 'newest') params.set('sort', filters.sortBy);
      
      // Only push to history if params have changed
      if (params.toString() !== searchParams.toString()) {
        setSearchParams(params, { replace: true });
      }
      
      // Fetch images with current filters and pagination
      const response: GalleryApiResponse = await getGalleryImages(
        {
          ...filters,
          // Only include search query if it's not empty
          searchQuery: filters.searchQuery?.trim() || undefined,
        },
        currentPage,
        itemsPerPage
      );
      
      // If this is the first page or we're resetting, replace the images
      // Otherwise, append to the existing images for infinite scroll
      setImages(prevImages => 
        resetPagination || currentPage === 1 
          ? response.images 
          : [...prevImages, ...response.images]
      );
      
      setTotalImages(response.total);
      setHasMore(currentPage < response.totalPages);
      
      // Only fetch categories and trip types if we don't have them yet
      if (categories.length === 0) {
        const [categoriesData, tripTypesData] = await Promise.all([
          getCategories(),
          getTripTypes(),
        ]);
        
        setCategories(categoriesData);
        setTripTypes(tripTypesData);
      }
      
    } catch (err) {
      console.error('Error fetching gallery data:', err);
      setError('Failed to load gallery. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  }, [filters, page, itemsPerPage, categories.length, searchParams, setSearchParams]);
  
  // Initial data fetch
  useEffect(() => {
    fetchGalleryData(true);
    
    // Cleanup function to cancel any pending requests
    return () => {
      // Add any cleanup code if needed (e.g., cancel fetch requests)
    };
  }, [fetchGalleryData]);
  
  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<GalleryFiltersType>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
    }));
    
    // Reset to first page when filters change
    if (page !== 1) {
      setPage(1);
    }
    
    // If we're already on page 1, trigger a refetch
    if (page === 1) {
      fetchGalleryData(true);
    }
  };
  
  // Reset all filters
  const resetFilters = () => {
    setFilters({
      category: undefined,
      tripType: undefined,
      searchQuery: undefined,
      sortBy: 'newest',
    });
    
    if (page !== 1) {
      setPage(1);
    } else {
      fetchGalleryData(true);
    }
  };
  
  // Handle image click to open lightbox
  const handleImageClick = (image: GalleryImage, index: number) => {
    setCurrentImageIndex(images.findIndex(img => img.id === image.id));
    setLightboxOpen(true);
  };
  
  // Handle lightbox navigation
  const handleLightboxIndexChange = (index: number) => {
    setCurrentImageIndex(index);
  };
  
  // Handle infinite scroll
  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      setPage(prev => prev + 1);
    }
  };
  
  // Set up infinite scroll effect
  useEffect(() => {
    if (page > 1) {
      fetchGalleryData(false);
    }
  }, [page, fetchGalleryData]);
  
  // Prepare lightbox images
  const lightboxImages = images.map((img, index) => ({
    ...img,
    index,
    nextId: index < images.length - 1 ? images[index + 1]?.id : null,
    prevId: index > 0 ? images[index - 1]?.id : null,
  }));
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-7xl mx-auto">
        <header className="mb-8 text-center">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            Sailing Gallery
          </h1>
          <p className="text-xl text-muted-foreground">
            Explore our collection of stunning sailing moments
          </p>
        </header>
        
        {/* Filters */}
        <div className="mb-8">
          <GalleryFilters
            filters={filters}
            categories={categories}
            tripTypes={tripTypes}
            onFilterChange={handleFilterChange}
            onReset={resetFilters}
            isLoading={isLoading}
          />
        </div>
        
        {/* Error message */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
            {error}
            <Button
              variant="ghost"
              size="sm"
              className="ml-4"
              onClick={() => fetchGalleryData(true)}
            >
              Try Again
            </Button>
          </div>
        )}
        
        {/* Image grid */}
        <div className="mb-8">
          {images.length > 0 ? (
            <>
              <ImageGrid
                images={images}
                onImageClick={handleImageClick}
                loading={isLoading && page === 1}
                className="mb-8"
                columns={{
                  sm: 2,
                  md: 3,
                  lg: 4,
                }}
                gap={{
                  sm: 'gap-3',
                  md: 'gap-4',
                  lg: 'gap-5',
                }}
              />
              
              {/* Load more button */}
              {hasMore && (
                <div className="text-center mt-8">
                  <Button
                    onClick={handleLoadMore}
                    disabled={isLoading}
                    className="min-w-[150px]"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Loading...
                      </>
                    ) : (
                      'Load More'
                    )}
                  </Button>
                </div>
              )}
              
              {/* Image count */}
              <div className="text-center text-sm text-muted-foreground mt-4">
                Showing {images.length} of {totalImages} images
              </div>
            </>
          ) : !isLoading ? (
            <div className="text-center py-12">
              <div className="text-5xl mb-4">🖼️</div>
              <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                No images found
              </h3>
              <p className="text-muted-foreground mb-4">
                Try adjusting your search or filter criteria
              </p>
              <Button variant="outline" onClick={resetFilters}>
                Clear all filters
              </Button>
            </div>
          ) : null}
        </div>
        
        {/* Lightbox */}
        {lightboxImages.length > 0 && (
          <Lightbox
            isOpen={lightboxOpen}
            onClose={() => setLightboxOpen(false)}
            images={lightboxImages}
            initialIndex={currentImageIndex}
            onIndexChange={handleLightboxIndexChange}
            showThumbnails={true}
            showCaption={true}
            showDownload={true}
            showFullscreen={true}
          />
        )}
      </div>
    </div>
  );
};

export default Gallery;
