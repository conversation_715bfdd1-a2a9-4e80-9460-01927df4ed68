import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Search, Filter, X, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
export const GalleryFilters = ({ filters, categories, tripTypes, onFilterChange, onReset, className = '', isLoading = false, }) => {
    const hasActiveFilters = filters.category ||
        filters.tripType ||
        filters.searchQuery ||
        filters.sortBy !== 'newest';
    const handleCategoryChange = (value) => {
        onFilterChange({
            ...filters,
            category: value === 'all' ? undefined : value
        });
    };
    const handleTripTypeChange = (value) => {
        onFilterChange({
            ...filters,
            tripType: value === 'all' ? undefined : value
        });
    };
    const handleSortChange = (value) => {
        onFilterChange({
            ...filters,
            sortBy: value
        });
    };
    const handleSearchChange = (e) => {
        onFilterChange({
            ...filters,
            searchQuery: e.target.value || undefined
        });
    };
    const clearFilters = () => {
        onReset();
    };
    const getCategoryName = (id) => {
        return categories.find(cat => cat.id === id)?.name || id;
    };
    const getTripTypeName = (id) => {
        return tripTypes.find(type => type.id === id)?.name || id;
    };
    return (_jsxs("div", { className: cn('space-y-4', className), children: [_jsxs("div", { className: "flex flex-col sm:flex-row gap-4", children: [_jsxs("div", { className: "relative flex-1", children: [_jsx(Search, { className: "absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" }), _jsx(Input, { type: "search", placeholder: "Search images...", value: filters.searchQuery || '', onChange: handleSearchChange, className: "pl-9 w-full", disabled: isLoading })] }), _jsxs("div", { className: "flex items-center gap-2", children: [_jsxs(Select, { value: filters.sortBy || 'newest', onValueChange: handleSortChange, disabled: isLoading, children: [_jsxs(SelectTrigger, { className: "w-[180px]", children: [_jsx(Filter, { className: "h-4 w-4 mr-2 text-muted-foreground" }), _jsx(SelectValue, { placeholder: "Sort by" })] }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "newest", children: "Newest First" }), _jsx(SelectItem, { value: "oldest", children: "Oldest First" }), _jsx(SelectItem, { value: "featured", children: "Featured" }), _jsx(SelectItem, { value: "random", children: "Random" })] })] }), hasActiveFilters && (_jsxs(Button, { variant: "outline", size: "sm", onClick: clearFilters, disabled: isLoading, className: "whitespace-nowrap", children: [_jsx(X, { className: "h-4 w-4 mr-1" }), "Clear Filters"] }))] })] }), (filters.category || filters.tripType) && (_jsxs("div", { className: "flex flex-wrap items-center gap-2", children: [_jsx("span", { className: "text-sm text-muted-foreground", children: "Filters:" }), filters.category && (_jsxs(Badge, { variant: "secondary", className: "flex items-center gap-1 px-2 py-1 text-sm font-normal", children: [getCategoryName(filters.category), _jsx("button", { onClick: () => onFilterChange({ ...filters, category: undefined }), className: "rounded-full hover:bg-muted p-0.5", "aria-label": `Remove ${getCategoryName(filters.category)} filter`, children: _jsx(X, { className: "h-3 w-3" }) })] })), filters.tripType && (_jsxs(Badge, { variant: "secondary", className: "flex items-center gap-1 px-2 py-1 text-sm font-normal", children: [getTripTypeName(filters.tripType), _jsx("button", { onClick: () => onFilterChange({ ...filters, tripType: undefined }), className: "rounded-full hover:bg-muted p-0.5", "aria-label": `Remove ${getTripTypeName(filters.tripType)} filter`, children: _jsx(X, { className: "h-3 w-3" }) })] })), (filters.category || filters.tripType) && (_jsx(Button, { variant: "ghost", size: "sm", onClick: clearFilters, className: "h-auto p-0 text-sm text-muted-foreground hover:text-foreground", children: "Clear all" }))] })), _jsxs("div", { className: "flex flex-wrap gap-2", children: [_jsxs(Select, { value: filters.category || 'all', onValueChange: handleCategoryChange, disabled: isLoading, children: [_jsx(SelectTrigger, { className: "w-[180px]", children: _jsx(SelectValue, { children: filters.category ? getCategoryName(filters.category) : 'All Categories' }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "all", children: "All Categories" }), categories.map((category) => (_jsx(SelectItem, { value: category.id, children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { children: category.name }), _jsx(Badge, { variant: "outline", className: "ml-2", children: category.imageCount })] }) }, category.id)))] })] }), _jsxs(Select, { value: filters.tripType || 'all', onValueChange: handleTripTypeChange, disabled: isLoading, children: [_jsx(SelectTrigger, { className: "w-[180px]", children: _jsx(SelectValue, { children: filters.tripType ? getTripTypeName(filters.tripType) : 'All Trips' }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "all", children: "All Trips" }), tripTypes.map((tripType) => (_jsx(SelectItem, { value: tripType.id, children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { children: tripType.name }), _jsx(Badge, { variant: "outline", className: "ml-2", children: tripType.imageCount })] }) }, tripType.id)))] })] }), isLoading && (_jsxs("div", { className: "flex items-center text-sm text-muted-foreground ml-2", children: [_jsx(RefreshCw, { className: "h-4 w-4 animate-spin mr-2" }), "Loading..."] }))] })] }));
};
export default GalleryFilters;
