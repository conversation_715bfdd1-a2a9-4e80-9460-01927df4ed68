import React, { useEffect, useRef, useState } from 'react';
import { GalleryImage } from '@/types/gallery';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { OptimizedImage } from '@/components/ui/OptimizedImage';

interface ImageGridProps {
  images: GalleryImage[];
  onImageClick?: (image: GalleryImage, index: number) => void;
  loading?: boolean;
  className?: string;
  columns?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: {
    sm?: string;
    md?: string;
    lg?: string;
  };
  itemClassName?: string;
  loadingCount?: number;
}

const DEFAULT_COLUMNS = {
  sm: 2,
  md: 3,
  lg: 4,
  xl: 5,
};

const DEFAULT_GAP = {
  sm: 'gap-3',
  md: 'gap-4',
  lg: 'gap-5',
};

export const ImageGrid: React.FC<ImageGridProps> = ({
  images,
  onImageClick,
  loading = false,
  className = '',
  columns = {},
  gap = {},
  itemClassName = '',
  loadingCount = 12,
}) => {
  const gridRef = useRef<HTMLDivElement>(null);
  const [visibleIndices, setVisibleIndices] = useState<Set<number>>(new Set());
  const [isMounted, setIsMounted] = useState(false);
  
  // Merge default and custom breakpoints
  const breakpoints = {
    sm: columns.sm || DEFAULT_COLUMNS.sm,
    md: columns.md || DEFAULT_COLUMNS.md,
    lg: columns.lg || DEFAULT_COLUMNS.lg,
    xl: columns.xl || DEFAULT_COLUMNS.xl,
  };
  
  // Merge default and custom gaps
  const gapClasses = {
    sm: gap.sm || DEFAULT_GAP.sm,
    md: gap.md || DEFAULT_GAP.md,
    lg: gap.lg || DEFAULT_GAP.lg,
  };
  
  // Set up intersection observer for lazy loading
  useEffect(() => {
    setIsMounted(true);
    
    if (loading || !isMounted || !gridRef.current) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0', 10);
            setVisibleIndices((prev) => {
              const next = new Set(prev);
              next.add(index);
              return next;
            });
            observer.unobserve(entry.target);
          }
        });
      },
      {
        root: null,
        rootMargin: '200px',
        threshold: 0.01,
      }
    );
    
    const imageElements = gridRef.current.querySelectorAll('.gallery-image-item');
    imageElements.forEach((el) => observer.observe(el));
    
    return () => {
      imageElements.forEach((el) => observer.unobserve(el));
      observer.disconnect();
    };
  }, [images, loading, isMounted]);
  
  // Handle image click
  const handleClick = (image: GalleryImage, index: number) => {
    if (onImageClick) {
      onImageClick(image, index);
    }
  };
  
  // Generate grid column classes
  const gridCols = {
    sm: `sm:grid-cols-${breakpoints.sm}`,
    md: `md:grid-cols-${breakpoints.md}`,
    lg: `lg:grid-cols-${breakpoints.lg}`,
    xl: `xl:grid-cols-${breakpoints.xl}`,
  };
  
  // Loading skeleton
  if (loading) {
    return (
      <div 
        ref={gridRef}
        className={cn(
          'grid grid-cols-1',
          gridCols.sm,
          gridCols.md,
          gridCols.lg,
          gridCols.xl,
          gapClasses.sm,
          gapClasses.md,
          gapClasses.lg,
          className
        )}
      >
        {Array.from({ length: loadingCount }).map((_, i) => (
          <div 
            key={`skeleton-${i}`} 
            className={cn(
              'aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden',
              itemClassName
            )}
          >
            <Skeleton className="h-full w-full" />
          </div>
        ))}
      </div>
    );
  }
  
  // Calculate aspect ratio wrapper styles
  const getAspectRatioStyle = (aspectRatio: number) => {
    return { paddingBottom: `${(1 / aspectRatio) * 100}%` };
  };
  
  return (
    <div 
      ref={gridRef}
      className={cn(
        'grid grid-cols-1',
        gridCols.sm,
        gridCols.md,
        gridCols.lg,
        gridCols.xl,
        gapClasses.sm,
        gapClasses.md,
        gapClasses.lg,
        'w-full',
        className
      )}
    >
      {images.map((image, index) => {
        const isVisible = visibleIndices.has(index);
        
        return (
          <div 
            key={image.id}
            className={cn(
              'gallery-image-item',
              'group relative',
              'transition-transform duration-300 hover:scale-[1.02]',
              'rounded-lg overflow-hidden',
              'bg-gray-100 dark:bg-gray-800',
              'cursor-pointer',
              'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
              'dark:focus:ring-offset-gray-900',
              itemClassName
            )}
            data-index={index}
            onClick={() => handleClick(image, index)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleClick(image, index);
              }
            }}
            tabIndex={0}
            role="button"
            aria-label={`View ${image.alt}`}
          >
            <div 
              className="relative w-full"
              style={getAspectRatioStyle(image.aspectRatio || 1.5)}
            >
              {isVisible ? (
                <>
                  <OptimizedImage
                    src={image.src}
                    alt={image.alt}
                    width={image.width || 400}
                    height={image.height || Math.round(400 / (image.aspectRatio || 1.5))}
                    className="absolute inset-0 w-full h-full object-cover transition-opacity duration-300"
                    sizes="(max-width: 640px) 100vw, 
                           (max-width: 1024px) 50vw, 
                           (max-width: 1280px) 33.33vw, 
                           25vw"
                    loading="lazy"
                    decoding="async"
                  />
                  
                  {/* Image overlay with caption */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-4">
                    {image.caption && (
                      <div className="text-white text-sm font-medium line-clamp-2">
                        {image.caption}
                      </div>
                    )}
                  </div>
                  
                  {/* Featured badge */}
                  {image.featured && (
                    <div className="absolute top-2 right-2 bg-yellow-400 text-yellow-900 text-xs font-bold px-2 py-1 rounded-full">
                      Featured
                    </div>
                  )}
                </>
              ) : (
                <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse" />
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ImageGrid;
