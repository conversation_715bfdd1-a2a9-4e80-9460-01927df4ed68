import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect, useRef, useCallback } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { trackImageLoad } from '@/utils/performance';
/**
 * A component that delays rendering its children until it's visible in the viewport
 */
export function LazyLoad({ as: Tag = 'div', isVisible: isVisibleProp, rootMargin = '200px 0px 200px 0px', threshold = 0.01, unmountWhenNotVisible = false, placeholder, onVisible, children, ...props }) {
    const [isVisible, setIsVisible] = useState(isVisibleProp || false);
    const [hasBeenVisible, setHasBeenVisible] = useState(false);
    const elementRef = useRef(null);
    const observerRef = useRef(null);
    // Handle visibility change
    const handleVisibilityChange = useCallback((entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
            setIsVisible(true);
            setHasBeenVisible(true);
            onVisible?.();
            // Disconnect observer if we don't need it anymore
            if (unmountWhenNotVisible === false) {
                observerRef.current?.disconnect();
            }
        }
        else if (unmountWhenNotVisible) {
            setIsVisible(false);
        }
    }, [onVisible, unmountWhenNotVisible]);
    // Set up IntersectionObserver
    useEffect(() => {
        // Skip if visibility is controlled by props
        if (typeof isVisibleProp !== 'undefined') {
            setIsVisible(isVisibleProp);
            if (isVisibleProp) {
                setHasBeenVisible(true);
            }
            return;
        }
        // Skip if already visible or if running on the server
        if (isVisible || typeof window === 'undefined')
            return;
        // Create observer
        observerRef.current = new IntersectionObserver(handleVisibilityChange, {
            rootMargin,
            threshold,
        });
        // Start observing
        const currentElement = elementRef.current;
        if (currentElement) {
            observerRef.current.observe(currentElement);
        }
        // Clean up
        return () => {
            if (observerRef.current && currentElement) {
                observerRef.current.unobserve(currentElement);
            }
            observerRef.current?.disconnect();
        };
    }, [isVisible, isVisibleProp, rootMargin, threshold, handleVisibilityChange]);
    // Default placeholder
    const defaultPlaceholder = (_jsx("div", { className: "w-full h-full bg-gray-100 dark:bg-gray-800 rounded-md animate-pulse", children: _jsx("span", { className: "sr-only", children: "Loading..." }) }));
    // Determine what to render
    const shouldRenderChildren = (isVisible || hasBeenVisible) && !unmountWhenNotVisible;
    const showPlaceholder = !isVisible && !hasBeenVisible;
    return (_jsxs(Tag, { ref: elementRef, ...props, children: [showPlaceholder && (placeholder || defaultPlaceholder), shouldRenderChildren && children] }));
}
/**
 * A component that lazy loads images when they enter the viewport
 */
export function LazyImage({ src, alt, width, height, aspectRatio, showSkeleton = true, placeholder, rootMargin = '200px 0px 200px 0px', onLoad, onError, className, style, ...props }) {
    const [isLoaded, setIsLoaded] = useState(false);
    const [hasError, setHasError] = useState(false);
    const loadStartTime = useRef(0);
    // Handle image load
    const handleLoad = useCallback((event) => {
        const loadTime = performance.now() - loadStartTime.current;
        setIsLoaded(true);
        trackImageLoad(src, loadTime);
        onLoad?.(event);
    }, [onLoad, src]);
    // Handle image error
    const handleError = useCallback((event) => {
        setHasError(true);
        onError?.(event);
    }, [onError]);
    // Start tracking load time when the image is about to load
    const handleVisible = useCallback(() => {
        loadStartTime.current = performance.now();
    }, []);
    // Default placeholder (skeleton)
    const defaultPlaceholder = showSkeleton ? (_jsx(Skeleton, { className: cn('w-full h-full bg-gray-100 dark:bg-gray-800', className), style: {
            aspectRatio: aspectRatio ? String(aspectRatio) : undefined,
            width,
            height,
            ...style,
        } })) : null;
    // Error state
    if (hasError) {
        return (_jsx("div", { className: cn('flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-400', 'rounded-md overflow-hidden', className), style: {
                aspectRatio: aspectRatio ? String(aspectRatio) : undefined,
                width,
                height,
                ...style,
            }, children: _jsx("span", { className: "text-sm", children: "Failed to load image" }) }));
    }
    return (_jsx(LazyLoad, { as: "div", rootMargin: rootMargin, placeholder: placeholder || defaultPlaceholder, onVisible: handleVisible, className: cn('relative overflow-hidden', className), style: {
            aspectRatio: aspectRatio ? String(aspectRatio) : undefined,
            width,
            height,
            ...style,
        }, children: _jsx("img", { src: src, alt: alt, loading: "lazy", decoding: "async", onLoad: handleLoad, onError: handleError, className: cn('w-full h-full object-cover transition-opacity duration-300', {
                'opacity-0': !isLoaded,
                'opacity-100': isLoaded,
            }), ...props }) }));
}
// Utility function to combine class names
function cn(...classes) {
    return classes.filter(Boolean).join(' ');
}
