import React, { Suspense, useEffect, useRef, useState } from 'react';
import { useLocation, useNavigationType } from 'react-router-dom';
import { lazyWithRetry, withSuspense } from '@/utils/lazyWithRetry';
import { trackRouteChange } from '@/utils/performance';
import { Skeleton } from '@/components/ui/skeleton';

interface RouteLoaderProps<T extends React.ComponentType> {
  /**
   * Function that returns a dynamic import for the route component
   * Example: () => import('./pages/Home')
   */
  load: () => Promise<{ default: T }>;
  
  /**
   * Fallback component to show while loading
   * @default A skeleton loader
   */
  fallback?: React.ReactNode;
  
  /**
   * Whether to track route changes
   * @default true
   */
  trackPerformance?: boolean;
  
  /**
   * Additional props to pass to the loaded component
   */
  componentProps?: Omit<React.ComponentProps<T>, keyof T>;
}

// Default fallback component
const DefaultFallback = () => (
  <div className="flex flex-col space-y-4 p-4">
    <Skeleton className="h-8 w-3/4" />
    <Skeleton className="h-4 w-full" />
    <Skeleton className="h-4 w-5/6" />
    <Skeleton className="h-4 w-4/5" />
  </div>
);

/**
 * A component that handles lazy loading of route components with code splitting
 */
function RouteLoader<T extends React.ComponentType>({
  load,
  fallback = <DefaultFallback />,
  trackPerformance = true,
  componentProps = {},
}: RouteLoaderProps<T>) {
  const location = useLocation();
  const navigationType = useNavigationType();
  const previousPathname = useRef(location.pathname);
  const [isPending, setIsPending] = useState(false);
  
  // Lazy load the component with retry and suspense
  const LazyComponent = React.useMemo(
    () => withSuspense(lazyWithRetry(load), fallback),
    [load, fallback]
  );
  
  // Track route changes for performance monitoring
  useEffect(() => {
    if (trackPerformance && previousPathname.current !== location.pathname) {
      trackRouteChange(location.pathname, previousPathname.current);
      previousPathname.current = location.pathname;
    }
    
    // Handle pending state for navigation
    if (navigationType === 'PUSH' || navigationType === 'REPLACE') {
      setIsPending(true);
      const timer = setTimeout(() => setIsPending(false), 300);
      return () => clearTimeout(timer);
    }
    
    return () => {};
  }, [location.pathname, navigationType, trackPerformance]);
  
  // Preload the next route when hovering over links
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const handleMouseOver = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a[href^="/"]');
      
      if (link) {
        const href = link.getAttribute('href');
        if (href && href.startsWith('/')) {
          // Preload the route component when hovering over internal links
          load().catch(console.error);
        }
      }
    };
    
    document.addEventListener('mouseover', handleMouseOver);
    return () => document.removeEventListener('mouseover', handleMouseOver);
  }, [load]);
  
  // Show fallback while navigating to a new route
  if (isPending) {
    return <>{fallback}</>;
  }
  
  return <LazyComponent {...componentProps} />;
}

export { RouteLoader };

/**
 * A higher-order component that creates a route with lazy loading
 * @param loader - Function that returns a dynamic import for the route component
 * @param options - Additional options
 * @returns A component that can be used as a route element
 */
export function createLazyRoute<T extends React.ComponentType>(
  loader: () => Promise<{ default: T }>,
  options: Omit<RouteLoaderProps<T>, 'load'> = {}
) {
  return (props: Omit<React.ComponentProps<T>, keyof T>) => (
    <RouteLoader 
      load={loader} 
      fallback={options.fallback} 
      trackPerformance={options.trackPerformance}
      componentProps={props}
    />
  );
}

/**
 * A component that shows a loading indicator while child routes are loading
 */
export function SuspenseFallback({ children }: { children: React.ReactNode }) {
  return (
    <Suspense fallback={<DefaultFallback />}>
      {children}
    </Suspense>
  );
}
