import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { trackImageLoad } from '@/utils/performance';

interface LazyLoadProps extends React.HTMLAttributes<HTMLElement> {
  /**
   * The element type to render (default: 'div')
   */
  as?: keyof JSX.IntrinsicElements;
  
  /**
   * Whether the component is currently visible in the viewport
   * If not provided, IntersectionObserver will be used automatically
   */
  isVisible?: boolean;
  
  /**
   * Root margin for the IntersectionObserver
   * @default '200px 0px 200px 0px'
   */
  rootMargin?: string;
  
  /**
   * Threshold for the IntersectionObserver
   * @default 0.01
   */
  threshold?: number | number[];
  
  /**
   * Whether to unmount the children when not visible
   * @default false
   */
  unmountWhenNotVisible?: boolean;
  
  /**
   * Custom placeholder to show while loading
   */
  placeholder?: React.ReactNode;
  
  /**
   * Callback when the component becomes visible
   */
  onVisible?: () => void;
  
  /**
   * Children to render when visible
   */
  children: React.ReactNode;
}

/**
 * A component that delays rendering its children until it's visible in the viewport
 */
export function LazyLoad({
  as: Tag = 'div',
  isVisible: isVisibleProp,
  rootMargin = '200px 0px 200px 0px',
  threshold = 0.01,
  unmountWhenNotVisible = false,
  placeholder,
  onVisible,
  children,
  ...props
}: LazyLoadProps) {
  const [isVisible, setIsVisible] = useState(isVisibleProp || false);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);
  const elementRef = useRef<HTMLElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Handle visibility change
  const handleVisibilityChange = useCallback((entries: IntersectionObserverEntry[]) => {
    const [entry] = entries;
    
    if (entry.isIntersecting) {
      setIsVisible(true);
      setHasBeenVisible(true);
      onVisible?.();
      
      // Disconnect observer if we don't need it anymore
      if (unmountWhenNotVisible === false) {
        observerRef.current?.disconnect();
      }
    } else if (unmountWhenNotVisible) {
      setIsVisible(false);
    }
  }, [onVisible, unmountWhenNotVisible]);

  // Set up IntersectionObserver
  useEffect(() => {
    // Skip if visibility is controlled by props
    if (typeof isVisibleProp !== 'undefined') {
      setIsVisible(isVisibleProp);
      if (isVisibleProp) {
        setHasBeenVisible(true);
      }
      return;
    }
    
    // Skip if already visible or if running on the server
    if (isVisible || typeof window === 'undefined') return;
    
    // Create observer
    observerRef.current = new IntersectionObserver(handleVisibilityChange, {
      rootMargin,
      threshold,
    });
    
    // Start observing
    const currentElement = elementRef.current;
    if (currentElement) {
      observerRef.current.observe(currentElement);
    }
    
    // Clean up
    return () => {
      if (observerRef.current && currentElement) {
        observerRef.current.unobserve(currentElement);
      }
      observerRef.current?.disconnect();
    };
  }, [isVisible, isVisibleProp, rootMargin, threshold, handleVisibilityChange]);

  // Default placeholder
  const defaultPlaceholder = (
    <div className="w-full h-full bg-gray-100 dark:bg-gray-800 rounded-md animate-pulse">
      <span className="sr-only">Loading...</span>
    </div>
  );

  // Determine what to render
  const shouldRenderChildren = (isVisible || hasBeenVisible) && !unmountWhenNotVisible;
  const showPlaceholder = !isVisible && !hasBeenVisible;

  return (
    <Tag ref={elementRef} {...props}>
      {showPlaceholder && (placeholder || defaultPlaceholder)}
      {shouldRenderChildren && children}
    </Tag>
  );
}

interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  /**
   * Source URL of the image
   */
  src: string;
  
  /**
   * Alternative text for the image
   */
  alt: string;
  
  /**
   * Width of the image (for placeholder)
   */
  width?: number | string;
  
  /**
   * Height of the image (for placeholder)
   */
  height?: number | string;
  
  /**
   * Aspect ratio of the image (e.g., '16/9')
   */
  aspectRatio?: string;
  
  /**
   * Whether to show a skeleton loader
   * @default true
   */
  showSkeleton?: boolean;
  
  /**
   * Custom placeholder element
   */
  placeholder?: React.ReactNode;
  
  /**
   * Root margin for the IntersectionObserver
   * @default '200px 0px 200px 0px'
   */
  rootMargin?: string;
  
  /**
   * Callback when the image is loaded
   */
  onLoad?: (event: React.SyntheticEvent<HTMLImageElement>) => void;
  
  /**
   * Callback when there's an error loading the image
   */
  onError?: (event: React.SyntheticEvent<HTMLImageElement>) => void;
}

/**
 * A component that lazy loads images when they enter the viewport
 */
export function LazyImage({
  src,
  alt,
  width,
  height,
  aspectRatio,
  showSkeleton = true,
  placeholder,
  rootMargin = '200px 0px 200px 0px',
  onLoad,
  onError,
  className,
  style,
  ...props
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const loadStartTime = useRef(0);
  
  // Handle image load
  const handleLoad = useCallback((event: React.SyntheticEvent<HTMLImageElement>) => {
    const loadTime = performance.now() - loadStartTime.current;
    setIsLoaded(true);
    trackImageLoad(src, loadTime);
    onLoad?.(event);
  }, [onLoad, src]);
  
  // Handle image error
  const handleError = useCallback((event: React.SyntheticEvent<HTMLImageElement>) => {
    setHasError(true);
    onError?.(event);
  }, [onError]);
  
  // Start tracking load time when the image is about to load
  const handleVisible = useCallback(() => {
    loadStartTime.current = performance.now();
  }, []);
  
  // Default placeholder (skeleton)
  const defaultPlaceholder = showSkeleton ? (
    <Skeleton 
      className={cn(
        'w-full h-full bg-gray-100 dark:bg-gray-800',
        className
      )}
      style={{
        aspectRatio: aspectRatio ? String(aspectRatio) : undefined,
        width,
        height,
        ...style,
      }}
    />
  ) : null;
  
  // Error state
  if (hasError) {
    return (
      <div 
        className={cn(
          'flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-400',
          'rounded-md overflow-hidden',
          className
        )}
        style={{
          aspectRatio: aspectRatio ? String(aspectRatio) : undefined,
          width,
          height,
          ...style,
        }}
      >
        <span className="text-sm">Failed to load image</span>
      </div>
    );
  }
  
  return (
    <LazyLoad
      as="div"
      rootMargin={rootMargin}
      placeholder={placeholder || defaultPlaceholder}
      onVisible={handleVisible}
      className={cn(
        'relative overflow-hidden',
        className
      )}
      style={{
        aspectRatio: aspectRatio ? String(aspectRatio) : undefined,
        width,
        height,
        ...style,
      }}
    >
      <img
        src={src}
        alt={alt}
        loading="lazy"
        decoding="async"
        onLoad={handleLoad}
        onError={handleError}
        className={cn(
          'w-full h-full object-cover transition-opacity duration-300',
          {
            'opacity-0': !isLoaded,
            'opacity-100': isLoaded,
          }
        )}
        {...props}
      />
    </LazyLoad>
  );
}

// Utility function to combine class names
function cn(...classes: (string | undefined)[]): string {
  return classes.filter(Boolean).join(' ');
}
