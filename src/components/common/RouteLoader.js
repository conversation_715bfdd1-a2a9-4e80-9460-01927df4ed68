import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import React, { Suspense, useEffect, useRef, useState } from 'react';
import { useLocation, useNavigationType } from 'react-router-dom';
import { lazyWithRetry, withSuspense } from '@/utils/lazyWithRetry';
import { trackRouteChange } from '@/utils/performance';
import { Skeleton } from '@/components/ui/skeleton';
// Default fallback component
const DefaultFallback = () => (_jsxs("div", { className: "flex flex-col space-y-4 p-4", children: [_jsx(Skeleton, { className: "h-8 w-3/4" }), _jsx(Skeleton, { className: "h-4 w-full" }), _jsx(Skeleton, { className: "h-4 w-5/6" }), _jsx(Skeleton, { className: "h-4 w-4/5" })] }));
/**
 * A component that handles lazy loading of route components with code splitting
 */
function RouteLoader({ load, fallback = _jsx(DefaultFallback, {}), trackPerformance = true, componentProps = {}, }) {
    const location = useLocation();
    const navigationType = useNavigationType();
    const previousPathname = useRef(location.pathname);
    const [isPending, setIsPending] = useState(false);
    // Lazy load the component with retry and suspense
    const LazyComponent = React.useMemo(() => withSuspense(lazyWithRetry(load), fallback), [load, fallback]);
    // Track route changes for performance monitoring
    useEffect(() => {
        if (trackPerformance && previousPathname.current !== location.pathname) {
            trackRouteChange(location.pathname, previousPathname.current);
            previousPathname.current = location.pathname;
        }
        // Handle pending state for navigation
        if (navigationType === 'PUSH' || navigationType === 'REPLACE') {
            setIsPending(true);
            const timer = setTimeout(() => setIsPending(false), 300);
            return () => clearTimeout(timer);
        }
        return () => { };
    }, [location.pathname, navigationType, trackPerformance]);
    // Preload the next route when hovering over links
    useEffect(() => {
        if (typeof window === 'undefined')
            return;
        const handleMouseOver = (event) => {
            const target = event.target;
            const link = target.closest('a[href^="/"]');
            if (link) {
                const href = link.getAttribute('href');
                if (href && href.startsWith('/')) {
                    // Preload the route component when hovering over internal links
                    load().catch(console.error);
                }
            }
        };
        document.addEventListener('mouseover', handleMouseOver);
        return () => document.removeEventListener('mouseover', handleMouseOver);
    }, [load]);
    // Show fallback while navigating to a new route
    if (isPending) {
        return _jsx(_Fragment, { children: fallback });
    }
    return _jsx(LazyComponent, { ...componentProps });
}
export { RouteLoader };
/**
 * A higher-order component that creates a route with lazy loading
 * @param loader - Function that returns a dynamic import for the route component
 * @param options - Additional options
 * @returns A component that can be used as a route element
 */
export function createLazyRoute(loader, options = {}) {
    return (props) => (_jsx(RouteLoader, { load: loader, fallback: options.fallback, trackPerformance: options.trackPerformance, componentProps: props }));
}
/**
 * A component that shows a loading indicator while child routes are loading
 */
export function SuspenseFallback({ children }) {
    return (_jsx(Suspense, { fallback: _jsx(DefaultFallback, {}), children: children }));
}
