import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Star, Loader2, X, Upload } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import { uploadImage } from '@/api/upload';
// Form validation schema
const testimonialFormSchema = z.object({
    content: z.string().min(10, 'Testimonial must be at least 10 characters'),
    rating: z.number().min(1, 'Rating is required').max(5, 'Maximum rating is 5'),
    authorName: z.string().min(2, 'Name is required'),
    authorRole: z.string().optional(),
    authorLocation: z.string().optional(),
    featured: z.boolean().default(false),
    status: z.enum(['pending', 'approved', 'rejected']).default('pending'),
    source: z.string().optional(),
    testimonialDate: z.string().optional(),
    response: z.object({
        content: z.string(),
        author: z.string(),
    }).optional(),
});
export const TestimonialForm = ({ initialData, onSubmit, isSubmitting, isAdmin = false, onCancel, }) => {
    const [isUploading, setIsUploading] = useState(false);
    const [avatarPreview, setAvatarPreview] = useState(initialData?.author.avatar || null);
    const [showResponse, setShowResponse] = useState(!!initialData?.response);
    const { register, handleSubmit, control, formState: { errors }, setValue, reset, watch, } = useForm({
        resolver: zodResolver(testimonialFormSchema),
        defaultValues: {
            content: initialData?.content || '',
            rating: initialData?.rating || 5,
            authorName: initialData?.author.name || '',
            authorRole: initialData?.author.role || '',
            authorLocation: initialData?.author.location || '',
            featured: initialData?.featured || false,
            status: initialData?.status || 'pending',
            source: initialData?.source || '',
            testimonialDate: initialData?.testimonialDate || new Date().toISOString().split('T')[0],
            response: initialData?.response ? {
                content: initialData.response.content,
                author: initialData.response.author,
            } : undefined,
        },
    });
    // Watch the rating value to update the star display
    const rating = watch('rating');
    // Handle image upload
    const handleImageUpload = async (e) => {
        const file = e.target.files?.[0];
        if (!file)
            return;
        // Validate file type
        if (!file.type.startsWith('image/')) {
            toast({
                title: 'Invalid file type',
                description: 'Please upload an image file (JPEG, PNG, etc.)',
                variant: 'destructive',
            });
            return;
        }
        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            toast({
                title: 'File too large',
                description: 'Maximum file size is 5MB',
                variant: 'destructive',
            });
            return;
        }
        try {
            setIsUploading(true);
            // In a real app, you would upload the image to your server
            const imageUrl = await uploadImage(file);
            // For now, we'll just create a local URL for preview
            const previewUrl = URL.createObjectURL(file);
            setAvatarPreview(previewUrl);
            toast({
                title: 'Image uploaded',
                description: 'Profile picture updated successfully',
            });
        }
        catch (error) {
            console.error('Error uploading image:', error);
            toast({
                title: 'Upload failed',
                description: 'There was an error uploading your image. Please try again.',
                variant: 'destructive',
            });
        }
        finally {
            setIsUploading(false);
        }
    };
    // Handle form submission
    const onSubmitHandler = async (data) => {
        try {
            await onSubmit({
                ...data,
                authorAvatar: avatarPreview || undefined,
            });
            // Reset form if this is a new testimonial
            if (!initialData) {
                reset();
                setAvatarPreview(null);
            }
            toast({
                title: initialData ? 'Testimonial updated' : 'Testimonial submitted',
                description: initialData
                    ? 'Your changes have been saved.'
                    : 'Thank you for your feedback! Your testimonial is pending approval.',
            });
        }
        catch (error) {
            console.error('Error submitting testimonial:', error);
            toast({
                title: 'Something went wrong',
                description: 'There was an error submitting your testimonial. Please try again.',
                variant: 'destructive',
            });
        }
    };
    // Render star rating input
    const renderStarRating = () => {
        return (_jsxs("div", { className: "flex items-center space-x-1", children: [[1, 2, 3, 4, 5].map((value) => (_jsx("button", { type: "button", onClick: () => setValue('rating', value, { shouldValidate: true }), className: "focus:outline-none", "aria-label": `Rate ${value} out of 5`, children: _jsx(Star, { className: `h-8 w-8 ${value <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}` }) }, value))), _jsxs("span", { className: "ml-2 text-sm text-muted-foreground", children: [rating, ".0 out of 5"] })] }));
    };
    return (_jsxs("form", { onSubmit: handleSubmit(onSubmitHandler), className: "space-y-6", children: [_jsxs("div", { className: "space-y-4", children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "rating", children: "Your Rating *" }), _jsxs("div", { className: "mt-1", children: [_jsx(Controller, { name: "rating", control: control, render: ({ field }) => (_jsxs("div", { children: [_jsx("input", { type: "hidden", ...field }), renderStarRating()] })) }), errors.rating && (_jsx("p", { className: "mt-1 text-sm text-red-600", children: errors.rating.message }))] })] }), _jsxs("div", { children: [_jsxs(Label, { htmlFor: "content", children: ["Your Testimonial *", _jsx("span", { className: "ml-1 text-sm text-muted-foreground", children: "(Min. 10 characters)" })] }), _jsx(Textarea, { id: "content", rows: 5, className: "mt-1", placeholder: "Share your experience with us...", ...register('content') }), errors.content && (_jsx("p", { className: "mt-1 text-sm text-red-600", children: errors.content.message }))] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "authorName", children: "Your Name *" }), _jsx(Input, { id: "authorName", className: "mt-1", placeholder: "John Doe", ...register('authorName') }), errors.authorName && (_jsx("p", { className: "mt-1 text-sm text-red-600", children: errors.authorName.message }))] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "authorRole", children: "Your Role (Optional)" }), _jsx(Input, { id: "authorRole", className: "mt-1", placeholder: "e.g., Travel Enthusiast, Photographer", ...register('authorRole') })] })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "authorLocation", children: "Location (Optional)" }), _jsx(Input, { id: "authorLocation", className: "mt-1", placeholder: "City, Country", ...register('authorLocation') })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "source", children: "Where did you hear about us? (Optional)" }), _jsx(Input, { id: "source", className: "mt-1", placeholder: "e.g., Google, TripAdvisor, Friend", ...register('source') })] })] }), _jsxs("div", { children: [_jsx(Label, { children: "Profile Picture (Optional)" }), _jsxs("div", { className: "mt-1 flex items-center", children: [_jsx("div", { className: "relative h-16 w-16 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-800", children: avatarPreview ? (_jsxs(_Fragment, { children: [_jsx("img", { src: avatarPreview, alt: "Profile preview", className: "h-full w-full object-cover" }), _jsx("button", { type: "button", onClick: () => setAvatarPreview(null), className: "absolute top-0 right-0 p-1 bg-red-500 text-white rounded-full -mt-1 -mr-1 hover:bg-red-600", "aria-label": "Remove profile picture", children: _jsx(X, { className: "h-3 w-3" }) })] })) : (_jsx("div", { className: "h-full w-full flex items-center justify-center text-gray-400", children: _jsx(User, { className: "h-8 w-8" }) })) }), _jsxs("div", { className: "ml-4", children: [_jsx("input", { type: "file", id: "avatar", accept: "image/*", className: "hidden", onChange: handleImageUpload, disabled: isUploading }), _jsxs("label", { htmlFor: "avatar", className: cn('inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500', isUploading && 'opacity-50 cursor-not-allowed'), children: [isUploading ? (_jsx(Loader2, { className: "h-4 w-4 mr-2 animate-spin" })) : (_jsx(Upload, { className: "h-4 w-4 mr-2" })), avatarPreview ? 'Change' : 'Upload', " Photo"] })] })] })] }), isAdmin && (_jsx(_Fragment, { children: _jsxs("div", { className: "border-t border-gray-200 dark:border-gray-700 pt-4 mt-4", children: [_jsx("h3", { className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-4", children: "Admin Controls" }), _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { children: [_jsx(Label, { children: "Status" }), _jsxs("div", { className: "mt-1 space-y-2", children: [_jsxs("div", { className: "flex items-center space-x-2", children: [_jsx("input", { type: "radio", id: "status-pending", value: "pending", className: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300", ...register('status') }), _jsx(Label, { htmlFor: "status-pending", className: "font-normal", children: "Pending Review" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx("input", { type: "radio", id: "status-approved", value: "approved", className: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300", ...register('status') }), _jsx(Label, { htmlFor: "status-approved", className: "font-normal", children: "Approved" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx("input", { type: "radio", id: "status-rejected", value: "rejected", className: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300", ...register('status') }), _jsx(Label, { htmlFor: "status-rejected", className: "font-normal", children: "Rejected" })] })] })] }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "featured", className: "font-normal", children: "Featured Testimonial" }), _jsx("p", { className: "text-sm text-muted-foreground", children: "Show this testimonial in featured sections" })] }), _jsx(Controller, { name: "featured", control: control, render: ({ field }) => (_jsx(Switch, { id: "featured", checked: field.value, onCheckedChange: field.onChange })) })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "testimonialDate", children: "Testimonial Date" }), _jsx(Input, { id: "testimonialDate", type: "date", className: "mt-1 max-w-xs", ...register('testimonialDate') })] }), _jsxs("div", { children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsx(Label, { children: "Add Response" }), _jsx(Switch, { checked: showResponse, onCheckedChange: setShowResponse })] }), showResponse && (_jsxs("div", { className: "mt-2 space-y-2 pl-4 border-l-2 border-blue-200 dark:border-blue-800", children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "response.content", children: "Response Text" }), _jsx(Textarea, { id: "response.content", rows: 3, className: "mt-1", placeholder: "Your response to this testimonial...", ...register('response.content') })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "response.author", children: "Response Author" }), _jsx(Input, { id: "response.author", className: "mt-1", placeholder: "Your name", ...register('response.author') })] })] }))] })] }), ")}"] }) })), "div> )}"] }), _jsxs("div", { className: "flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700", children: [onCancel && (_jsx(Button, { type: "button", variant: "outline", onClick: onCancel, disabled: isSubmitting, children: "Cancel" })), _jsx(Button, { type: "submit", disabled: isSubmitting, children: isSubmitting ? (_jsxs(_Fragment, { children: [_jsx(Loader2, { className: "mr-2 h-4 w-4 animate-spin" }), initialData ? 'Saving...' : 'Submitting...'] })) : initialData ? ('Save Changes') : ('Submit Testimonial') })] })] }));
};
export default TestimonialForm;
