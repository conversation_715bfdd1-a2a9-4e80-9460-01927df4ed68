import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Star, StarHalf, MapPin, MessageSquare, Clock } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    for (let i = 1; i <= 5; i++) {
        if (i <= fullStars) {
            stars.push(_jsx(Star, { className: "h-5 w-5 fill-yellow-400 text-yellow-400" }, i));
        }
        else if (i === fullStars + 1 && hasHalfStar) {
            stars.push(_jsx(StarHalf, { className: "h-5 w-5 fill-yellow-400 text-yellow-400" }, i));
        }
        else {
            stars.push(_jsx(Star, { className: "h-5 w-5 text-gray-200 dark:text-gray-700" }, i));
        }
    }
    return stars;
};
export const TestimonialCard = ({ testimonial, className, showStatus = false, onClick, }) => {
    const statusColors = {
        approved: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
        pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
        rejected: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    };
    return (_jsxs("div", { className: cn('bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden', 'transition-all duration-200 hover:shadow-md', onClick && 'cursor-pointer hover:border-blue-200 dark:hover:border-blue-800', className), onClick: onClick, children: [_jsxs("div", { className: "p-6", children: [_jsxs("div", { className: "flex justify-between items-start mb-4", children: [_jsxs("div", { className: "flex items-center space-x-1", children: [renderStars(testimonial.rating), _jsx("span", { className: "ml-2 text-sm font-medium text-gray-900 dark:text-white", children: testimonial.rating.toFixed(1) })] }), testimonial.source && (_jsx(Badge, { variant: "outline", className: "text-xs", children: testimonial.source }))] }), _jsxs("p", { className: "text-gray-600 dark:text-gray-300 mb-4 italic", children: ["\"", testimonial.content, "\""] }), testimonial.response && (_jsxs("div", { className: "mt-4 pl-4 border-l-2 border-blue-200 dark:border-blue-800", children: [_jsxs("div", { className: "flex items-center text-sm text-blue-600 dark:text-blue-400 mb-1", children: [_jsx(MessageSquare, { className: "h-4 w-4 mr-1" }), _jsx("span", { className: "font-medium", children: "Response from Sailing Serai" })] }), _jsx("p", { className: "text-sm text-gray-600 dark:text-gray-400", children: testimonial.response.content })] })), _jsxs("div", { className: "mt-6 flex items-center", children: [_jsxs(Avatar, { className: "h-10 w-10", children: [_jsx(AvatarImage, { src: testimonial.author.avatar, alt: testimonial.author.name }), _jsx(AvatarFallback, { className: "bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300", children: testimonial.author.name
                                            .split(' ')
                                            .map(n => n[0])
                                            .join('')
                                            .toUpperCase() })] }), _jsxs("div", { className: "ml-3", children: [_jsx("h4", { className: "text-sm font-medium text-gray-900 dark:text-white", children: testimonial.author.name }), _jsxs("div", { className: "flex items-center text-xs text-gray-500 dark:text-gray-400", children: [testimonial.author.location && (_jsxs("div", { className: "flex items-center", children: [_jsx(MapPin, { className: "h-3 w-3 mr-1" }), _jsx("span", { children: testimonial.author.location })] })), testimonial.testimonialDate && (_jsxs("div", { className: "flex items-center ml-3", children: [_jsx(Clock, { className: "h-3 w-3 mr-1" }), _jsx("time", { dateTime: testimonial.testimonialDate, children: format(new Date(testimonial.testimonialDate), 'MMM d, yyyy') })] }))] })] }), showStatus && testimonial.status !== 'approved' && (_jsx(Badge, { className: cn('ml-auto text-xs', statusColors[testimonial.status] || 'bg-gray-100 text-gray-800'), children: testimonial.status.charAt(0).toUpperCase() + testimonial.status.slice(1) }))] })] }), testimonial.featured && (_jsxs("div", { className: "bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300 text-xs font-medium px-3 py-1.5 flex items-center justify-center", children: [_jsx(Star, { className: "h-3.5 w-3.5 mr-1.5" }), "Featured Testimonial"] }))] }));
};
export default TestimonialCard;
