import React from 'react';
import { Testimonial } from '@/types/testimonial';
import { Star, StarHalf, MapPin, MessageSquare, Clock } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { OptimizedAvatarImage } from '@/components/ui/OptimizedAvatarImage';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';

interface TestimonialCardProps {
  testimonial: Testimonial;
  className?: string;
  showStatus?: boolean;
  onClick?: () => void;
}

const renderStars = (rating: number) => {
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;

  for (let i = 1; i <= 5; i++) {
    if (i <= fullStars) {
      stars.push(<Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />);
    } else if (i === fullStars + 1 && hasHalfStar) {
      stars.push(<StarHalf key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />);
    } else {
      stars.push(<Star key={i} className="h-5 w-5 text-gray-200 dark:text-gray-700" />);
    }
  }

  return stars;
};

export const TestimonialCard: React.FC<TestimonialCardProps> = ({
  testimonial,
  className,
  showStatus = false,
  onClick,
}) => {
  const statusColors = {
    approved: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
    rejected: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
  };

  return (
    <div 
      className={cn(
        'bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden',
        'transition-all duration-200 hover:shadow-md',
        onClick && 'cursor-pointer hover:border-blue-200 dark:hover:border-blue-800',
        className
      )}
      onClick={onClick}
    >
      <div className="p-6">
        {/* Header with rating and source */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center space-x-1">
            {renderStars(testimonial.rating)}
            <span className="ml-2 text-sm font-medium text-gray-900 dark:text-white">
              {testimonial.rating.toFixed(1)}
            </span>
          </div>
          
          {testimonial.source && (
            <Badge variant="outline" className="text-xs">
              {testimonial.source}
            </Badge>
          )}
        </div>

        {/* Testimonial content */}
        <p className="text-gray-600 dark:text-gray-300 mb-4 italic">"{testimonial.content}"</p>

        {/* Response from business */}
        {testimonial.response && (
          <div className="mt-4 pl-4 border-l-2 border-blue-200 dark:border-blue-800">
            <div className="flex items-center text-sm text-blue-600 dark:text-blue-400 mb-1">
              <MessageSquare className="h-4 w-4 mr-1" />
              <span className="font-medium">Response from Sailing Serai</span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">{testimonial.response.content}</p>
          </div>
        )}

        {/* Author info */}
        <div className="mt-6 flex items-center">
          <Avatar className="h-10 w-10">
            <OptimizedAvatarImage 
              src={testimonial.author.avatar} 
              alt={testimonial.author.name}
              width={40}
              height={40}
              sizes="40px"
              loading="lazy"
            />
            <AvatarFallback className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300">
              {testimonial.author.name
                .split(' ')
                .map(n => n[0])
                .join('')
                .toUpperCase()}
            </AvatarFallback>
          </Avatar>
          
          <div className="ml-3">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">
              {testimonial.author.name}
            </h4>
            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
              {testimonial.author.location && (
                <div className="flex items-center">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span>{testimonial.author.location}</span>
                </div>
              )}
              {testimonial.testimonialDate && (
                <div className="flex items-center ml-3">
                  <Clock className="h-3 w-3 mr-1" />
                  <time dateTime={testimonial.testimonialDate}>
                    {format(new Date(testimonial.testimonialDate), 'MMM d, yyyy')}
                  </time>
                </div>
              )}
            </div>
          </div>
          
          {showStatus && testimonial.status !== 'approved' && (
            <Badge 
              className={cn(
                'ml-auto text-xs',
                statusColors[testimonial.status] || 'bg-gray-100 text-gray-800'
              )}
            >
              {testimonial.status.charAt(0).toUpperCase() + testimonial.status.slice(1)}
            </Badge>
          )}
        </div>
      </div>
      
      {/* Featured badge */}
      {testimonial.featured && (
        <div className="bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300 text-xs font-medium px-3 py-1.5 flex items-center justify-center">
          <Star className="h-3.5 w-3.5 mr-1.5" />
          Featured Testimonial
        </div>
      )}
    </div>
  );
};

export default TestimonialCard;
