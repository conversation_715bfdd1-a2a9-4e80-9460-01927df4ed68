import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { TestimonialCard } from './TestimonialCard';
export const TestimonialCarousel = ({ testimonials, autoPlay = true, interval = 5000, showControls = true, showDots = true, className, cardClassName, maxItems = 3, }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isPaused, setIsPaused] = useState(false);
    const [touchStart, setTouchStart] = useState(0);
    const [touchEnd, setTouchEnd] = useState(0);
    // Handle window resize to adjust number of visible items
    const [visibleItems, setVisibleItems] = useState(window.innerWidth < 768 ? 1 : window.innerWidth < 1024 ? 2 : Math.min(3, maxItems));
    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth < 768) {
                setVisibleItems(1);
            }
            else if (window.innerWidth < 1024) {
                setVisibleItems(Math.min(2, maxItems));
            }
            else {
                setVisibleItems(Math.min(3, maxItems));
            }
        };
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [maxItems]);
    // Auto-advance slides
    useEffect(() => {
        if (!autoPlay || isPaused || testimonials.length <= visibleItems)
            return;
        const timer = setInterval(() => {
            setCurrentIndex((prevIndex) => prevIndex >= Math.ceil(testimonials.length / visibleItems) - 1 ? 0 : prevIndex + 1);
        }, interval);
        return () => clearInterval(timer);
    }, [autoPlay, interval, isPaused, testimonials.length, visibleItems]);
    const nextSlide = () => {
        setCurrentIndex((prevIndex) => prevIndex >= Math.ceil(testimonials.length / visibleItems) - 1 ? 0 : prevIndex + 1);
    };
    const prevSlide = () => {
        setCurrentIndex((prevIndex) => prevIndex <= 0 ? Math.ceil(testimonials.length / visibleItems) - 1 : prevIndex - 1);
    };
    const goToSlide = (index) => {
        setCurrentIndex(index);
    };
    // Handle touch events for mobile swipe
    const handleTouchStart = (e) => {
        setTouchStart(e.targetTouches[0].clientX);
    };
    const handleTouchMove = (e) => {
        setTouchEnd(e.targetTouches[0].clientX);
    };
    const handleTouchEnd = () => {
        if (touchStart - touchEnd > 50) {
            // Swipe left
            nextSlide();
        }
        if (touchStart - touchEnd < -50) {
            // Swipe right
            prevSlide();
        }
    };
    // Don't render if no testimonials
    if (!testimonials.length)
        return null;
    // Calculate the number of slides needed
    const totalSlides = Math.ceil(testimonials.length / visibleItems);
    const startIndex = currentIndex * visibleItems;
    const visibleTestimonials = testimonials.slice(startIndex, startIndex + visibleItems);
    // If there's only one slide, don't show controls or dots
    const singleSlide = totalSlides <= 1;
    return (_jsxs("div", { className: cn('relative w-full', className), onMouseEnter: () => setIsPaused(true), onMouseLeave: () => setIsPaused(false), children: [_jsx("div", { className: "overflow-hidden", onTouchStart: handleTouchStart, onTouchMove: handleTouchMove, onTouchEnd: handleTouchEnd, children: _jsx(AnimatePresence, { initial: false, mode: "wait", children: _jsx(motion.div, { initial: { opacity: 0, x: 100 }, animate: { opacity: 1, x: 0 }, exit: { opacity: 0, x: -100 }, transition: { duration: 0.3, ease: 'easeInOut' }, className: "grid gap-6", style: {
                            gridTemplateColumns: `repeat(${visibleItems}, minmax(0, 1fr))`,
                        }, children: visibleTestimonials.map((testimonial) => (_jsx("div", { className: cn('w-full', cardClassName), children: _jsx(TestimonialCard, { testimonial: testimonial }) }, testimonial.id))) }, currentIndex) }) }), showControls && !singleSlide && (_jsxs(_Fragment, { children: [_jsx(Button, { variant: "outline", size: "icon", onClick: prevSlide, className: "absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 z-10 bg-white dark:bg-gray-800 shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 h-10 w-10 rounded-full", "aria-label": "Previous testimonial", children: _jsx(ChevronLeft, { className: "h-5 w-5" }) }), _jsx(Button, { variant: "outline", size: "icon", onClick: nextSlide, className: "absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 z-10 bg-white dark:bg-gray-800 shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 h-10 w-10 rounded-full", "aria-label": "Next testimonial", children: _jsx(ChevronRight, { className: "h-5 w-5" }) })] })), showDots && !singleSlide && (_jsx("div", { className: "flex justify-center mt-6 space-x-2", children: Array.from({ length: totalSlides }).map((_, index) => (_jsx("button", { onClick: () => goToSlide(index), className: cn('h-2.5 w-2.5 rounded-full transition-colors', currentIndex === index
                        ? 'bg-blue-600 dark:bg-blue-500'
                        : 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600'), "aria-label": `Go to slide ${index + 1}` }, index))) })), testimonials.length > 0 && (_jsx("div", { className: "mt-6 text-center", children: _jsxs("div", { className: "inline-flex items-center bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300 px-4 py-2 rounded-full", children: [_jsx(Star, { className: "h-5 w-5 fill-current mr-1.5" }), _jsxs("span", { className: "font-medium", children: [(testimonials.reduce((sum, t) => sum + t.rating, 0) / testimonials.length).toFixed(1), _jsxs("span", { className: "text-sm text-blue-500 dark:text-blue-400 ml-1", children: ["(", testimonials.length, " ", testimonials.length === 1 ? 'review' : 'reviews', ")"] })] })] }) }))] }));
};
export default TestimonialCarousel;
