import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

const LanguageSwitcher = ({ className }: { className?: string }) => {
  const { i18n } = useTranslation();
  
  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <div className={cn("flex items-center space-x-1", className)}>
      <Button
        variant={i18n.language === 'en' ? 'secondary' : 'ghost'}
        size="sm"
        onClick={() => changeLanguage('en')}
        className={cn(
          'text-sm px-2 py-1',
          i18n.language === 'en' ? 'bg-blue-100 text-blue-800' : 'text-gray-700'
        )}
      >
        EN
      </Button>
      <span className="text-gray-300">|</span>
      <Button
        variant={i18n.language === 'mi' ? 'secondary' : 'ghost'}
        size="sm"
        onClick={() => changeLanguage('mi')}
        className={cn(
          'text-sm px-2 py-1',
          i18n.language === 'mi' ? 'bg-blue-100 text-blue-800' : 'text-gray-700'
        )}
      >
        MI
      </Button>
    </div>
  );
};

export default LanguageSwitcher;
