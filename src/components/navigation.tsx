import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { 
  HomeIcon, 
  UserIcon, 
  PhoneIcon, 
  LogInIcon, 
  LogOutIcon, 
  YachtIcon, 
  AnchorIcon, 
  MenuIcon, 
  XIcon 
} from './icons';
import LanguageSwitcher from './language-switcher';

interface NavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export function Navigation({ activeTab, onTabChange }: NavigationProps) {
  const { t } = useTranslation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when a nav item is clicked
  const handleNavClick = (tab: string) => {
    onTabChange(tab);
    setIsMobileMenuOpen(false);
  };

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isMobileMenuOpen && !target.closest('.mobile-menu-container')) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobileMenuOpen]);
  
  const navItems = [
    { id: 'home', label: t('nav.home'), icon: <HomeIcon className="mr-2 h-4 w-4" /> },
    { id: 'destinations', label: t('nav.destinations'), icon: <AnchorIcon className="mr-2 h-4 w-4" /> },
    { id: 'yachts', label: t('nav.yachts'), icon: <YachtIcon className="mr-2 h-4 w-4" /> },
    { id: 'about', label: t('nav.about'), icon: <UserIcon className="mr-2 h-4 w-4" /> },
    { id: 'contact', label: t('nav.contact'), icon: <PhoneIcon className="mr-2 h-4 w-4" /> },
  ];
  
  const isAuthenticated = false; // This would come from your auth context in a real app

  return (
    <nav className={`fixed w-full z-50 transition-all duration-300 ${
      isScrolled ? 'bg-white shadow-md' : 'bg-white/90 backdrop-blur-sm'
    }`}>
      <div className="container mx-auto px-4">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <a href="#" className="text-xl font-bold text-blue-600 hover:text-blue-700 transition-colors duration-200">
              {t('app.name', { defaultValue: 'Sailing Serai' })}
            </a>
          </div>
          
          {/* Mobile menu button */}
          <div className="flex items-center md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:bg-blue-50 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
              aria-expanded={isMobileMenuOpen}
              aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}
            >
              {isMobileMenuOpen ? (
                <XIcon className="block h-6 w-6" aria-hidden="true" />
              ) : (
                <MenuIcon className="block h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => (
              <Button
                key={item.id}
                variant={activeTab === item.id ? 'secondary' : 'ghost'}
                className={cn(
                  'px-3 py-2 text-sm font-medium flex items-center transition-colors duration-200',
                  activeTab === item.id 
                    ? 'text-blue-700 bg-blue-50 font-medium' 
                    : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                )}
                onClick={() => handleNavClick(item.id)}
                aria-current={activeTab === item.id ? 'page' : undefined}
              >
                {item.icon}
                <span>{item.label}</span>
              </Button>
            ))}
            
            <div className="flex items-center space-x-2 ml-2">
              {isAuthenticated ? (
                <>
                  <Button variant="ghost" size="sm" className="flex items-center text-gray-700">
                    <UserIcon className="h-4 w-4 mr-2" />
                    {t('nav.profile')}
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center border-gray-300">
                    <LogOutIcon className="h-4 w-4 mr-1" />
                    {t('nav.logout')}
                  </Button>
                </>
              ) : (
                <Button variant="outline" size="sm" className="flex items-center border-gray-300">
                  <LogInIcon className="h-4 w-4 mr-1" />
                  {t('nav.login')}
                </Button>
              )}
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                {t('nav.bookNow')}
              </Button>
              <div className="ml-2">
                <LanguageSwitcher />
              </div>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        <div 
          className={`md:hidden mobile-menu-container transition-all duration-200 ease-out overflow-hidden ${
            isMobileMenuOpen ? 'max-h-screen py-3' : 'max-h-0 py-0'
          }`}
          aria-hidden={!isMobileMenuOpen}
        >
          <div className="pt-1 pb-2 space-y-1">
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleNavClick(item.id)}
                className={`w-full text-left px-4 py-3 rounded-md text-base font-medium flex items-center transition-colors duration-200 ${
                  activeTab === item.id 
                    ? 'bg-blue-50 text-blue-700 font-medium' 
                    : 'text-gray-900 hover:bg-blue-50 hover:text-blue-700'
                }`}
                aria-current={activeTab === item.id ? 'page' : undefined}
              >
                {React.cloneElement(item.icon, { className: 'h-5 w-5 mr-3' })}
                <span>{item.label}</span>
              </button>
            ))}
            
            <div className="pt-3 pb-2 border-t border-gray-100 mt-2">
              <div className="flex items-center justify-between px-4 py-2 mb-2">
                <div className="text-sm font-medium text-gray-500">
                  {t('common.language', 'Language')}
                </div>
                <LanguageSwitcher />
              </div>
              
              {isAuthenticated ? (
                <>
                  <button
                    className="w-full text-left px-4 py-3 rounded-md text-base font-medium text-gray-900 hover:bg-blue-50 hover:text-blue-700 flex items-center transition-colors duration-200"
                    onClick={() => handleNavClick('profile')}
                  >
                    <UserIcon className="h-5 w-5 mr-3" />
                    {t('nav.profile')}
                  </button>
                  <button
                    className="w-full text-left px-4 py-3 rounded-md text-base font-medium text-gray-900 hover:bg-blue-50 hover:text-blue-700 flex items-center transition-colors duration-200"
                    onClick={() => handleNavClick('logout')}
                  >
                    <LogOutIcon className="h-5 w-5 mr-3" />
                    {t('nav.logout')}
                  </button>
                </>
              ) : (
                <button
                  className="w-full text-left px-4 py-3 rounded-md text-base font-medium text-gray-900 hover:bg-blue-50 hover:text-blue-700 flex items-center transition-colors duration-200"
                  onClick={() => handleNavClick('login')}
                >
                  <LogInIcon className="h-5 w-5 mr-3" />
                  {t('nav.login')}
                </button>
              )}
              
              <button
                className="w-full mt-3 px-4 py-3 rounded-md text-base font-medium text-white bg-blue-600 hover:bg-blue-700 active:bg-blue-800 flex items-center justify-center transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                onClick={() => handleNavClick('book-now')}
              >
                {t('nav.bookNow')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}

export default Navigation;
