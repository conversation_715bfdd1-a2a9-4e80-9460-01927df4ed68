import { jsx as _jsx } from "react/jsx-runtime";
export function SailingSeraiLogo({ className, ...props }) {
    return (_jsx("svg", { className: className, viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", ...props, children: _jsx("path", { d: "M12 2L4 8l8 6 8-6-8-6zM4 16l8 6 8-6M4 10l8 6 8-6", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round" }) }));
}
