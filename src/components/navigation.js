import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { HomeIcon, UserIcon, PhoneIcon, LogInIcon, LogOutIcon, YachtIcon, AnchorIcon, MenuIcon, XIcon } from './icons';
import LanguageSwitcher from './language-switcher';
export function Navigation({ activeTab, onTabChange }) {
    const { t } = useTranslation();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [isScrolled, setIsScrolled] = useState(false);
    // Handle scroll effect for navbar
    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 10);
        };
        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);
    // Close mobile menu when a nav item is clicked
    const handleNavClick = (tab) => {
        onTabChange(tab);
        setIsMobileMenuOpen(false);
    };
    // Close mobile menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            const target = event.target;
            if (isMobileMenuOpen && !target.closest('.mobile-menu-container')) {
                setIsMobileMenuOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [isMobileMenuOpen]);
    const navItems = [
        { id: 'home', label: t('nav.home'), icon: _jsx(HomeIcon, { className: "mr-2 h-4 w-4" }) },
        { id: 'destinations', label: t('nav.destinations'), icon: _jsx(AnchorIcon, { className: "mr-2 h-4 w-4" }) },
        { id: 'yachts', label: t('nav.yachts'), icon: _jsx(YachtIcon, { className: "mr-2 h-4 w-4" }) },
        { id: 'about', label: t('nav.about'), icon: _jsx(UserIcon, { className: "mr-2 h-4 w-4" }) },
        { id: 'contact', label: t('nav.contact'), icon: _jsx(PhoneIcon, { className: "mr-2 h-4 w-4" }) },
    ];
    const isAuthenticated = false; // This would come from your auth context in a real app
    return (_jsx("nav", { className: `fixed w-full z-50 transition-all duration-300 ${isScrolled ? 'bg-white shadow-md' : 'bg-white/90 backdrop-blur-sm'}`, children: _jsxs("div", { className: "container mx-auto px-4", children: [_jsxs("div", { className: "flex justify-between h-16", children: [_jsx("div", { className: "flex items-center", children: _jsx("span", { className: "text-xl font-bold text-blue-600", children: t('app.name', { defaultValue: 'Sailing Serai' }) }) }), _jsx("div", { className: "flex items-center md:hidden", children: _jsxs("button", { onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen), className: "inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 focus:outline-none", "aria-expanded": "false", children: [_jsx("span", { className: "sr-only", children: "Open main menu" }), isMobileMenuOpen ? (_jsx(XIcon, { className: "block h-6 w-6", "aria-hidden": "true" })) : (_jsx(MenuIcon, { className: "block h-6 w-6", "aria-hidden": "true" }))] }) }), _jsxs("div", { className: "hidden md:flex items-center space-x-1", children: [navItems.map((item) => (_jsxs(Button, { variant: activeTab === item.id ? 'secondary' : 'ghost', className: cn('px-3 py-2 rounded-md text-sm font-medium flex items-center', activeTab === item.id ? 'text-blue-700' : 'text-gray-700'), onClick: () => onTabChange(item.id), children: [item.icon, _jsx("span", { children: item.label })] }, item.id))), _jsxs("div", { className: "flex items-center space-x-2 ml-2", children: [isAuthenticated ? (_jsxs(_Fragment, { children: [_jsxs(Button, { variant: "ghost", size: "sm", className: "flex items-center text-gray-700", children: [_jsx(UserIcon, { className: "h-4 w-4 mr-2" }), t('nav.profile')] }), _jsxs(Button, { variant: "outline", size: "sm", className: "flex items-center border-gray-300", children: [_jsx(LogOutIcon, { className: "h-4 w-4 mr-1" }), t('nav.logout')] })] })) : (_jsxs(Button, { variant: "outline", size: "sm", className: "flex items-center border-gray-300", children: [_jsx(LogInIcon, { className: "h-4 w-4 mr-1" }), t('nav.login')] })), _jsx(Button, { size: "sm", className: "bg-blue-600 hover:bg-blue-700", children: t('nav.bookNow') }), _jsx("div", { className: "ml-2", children: _jsx(LanguageSwitcher, {}) })] })] })] }), _jsx("div", { className: `md:hidden mobile-menu-container transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-96 py-4' : 'max-h-0 overflow-hidden py-0'}`, children: _jsxs("div", { className: "pt-2 pb-3 space-y-1", children: [navItems.map((item) => (_jsxs("button", { onClick: () => handleNavClick(item.id), className: `w-full text-left px-3 py-2 rounded-md text-base font-medium flex items-center ${activeTab === item.id
                                    ? 'bg-blue-50 text-blue-700'
                                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'}`, children: [item.icon, _jsx("span", { className: "ml-2", children: item.label })] }, item.id))), _jsxs("div", { className: "pt-4 pb-2 border-t border-gray-200", children: [_jsxs("div", { className: "flex items-center justify-between px-3 py-2", children: [_jsx("div", { className: "text-sm font-medium text-gray-500", children: t('common.language', 'Language') }), _jsx(LanguageSwitcher, {})] }), isAuthenticated ? (_jsxs(_Fragment, { children: [_jsxs("button", { className: "w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 flex items-center", children: [_jsx(UserIcon, { className: "h-4 w-4 mr-2" }), t('nav.profile')] }), _jsxs("button", { className: "w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 flex items-center", children: [_jsx(LogOutIcon, { className: "h-4 w-4 mr-2" }), t('nav.logout')] })] })) : (_jsxs("button", { className: "w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 flex items-center", children: [_jsx(LogInIcon, { className: "h-4 w-4 mr-2" }), t('nav.login')] })), _jsx("button", { className: "w-full mt-2 px-3 py-2 rounded-md text-base font-medium text-white bg-blue-600 hover:bg-blue-700 flex items-center justify-center", children: t('nav.bookNow') })] })] }) })] }) }));
}
export default Navigation;
