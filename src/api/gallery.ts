import { GalleryApiResponse, GalleryFilters, GalleryImage, ImageUploadResponse } from '@/types/gallery';

// Mock data for development
const mockGalleryImages: GalleryImage[] = [
  {
    id: '1',
    src: '/images/gallery/sunset-sail-1.jpg',
    srcSet: {
      '400w': '/images/gallery/sunset-sail-1-400w.jpg',
      '800w': '/images/gallery/sunset-sail-1-800w.jpg',
      '1200w': '/images/gallery/sunset-sail-1-1200w.jpg',
      '1600w': '/images/gallery/sunset-sail-1-1600w.jpg',
    },
    alt: 'Beautiful sunset over the ocean with sailboat silhouette',
    width: 1920,
    height: 1280,
    aspectRatio: 1.5,
    caption: 'Golden hour sailing in the Bay of Islands',
    categories: ['sunsets', 'sailing'],
    tripTypes: ['day-cruise', 'sunset-cruise'],
    featured: true,
    createdAt: '2025-05-15T10:30:00Z',
    updatedAt: '2025-05-15T10:30:00Z',
    metadata: {
      camera: 'Sony A7R IV',
      lens: '24-70mm f/2.8',
      focalLength: '35mm',
      aperture: 'f/8',
      shutterSpeed: '1/250s',
      iso: 100,
      location: {
        name: 'Bay of Islands, New Zealand',
        coordinates: [-174.076812, -35.209389],
      },
    },
  },
  // Add more mock images as needed
];

const mockCategories = [
  { id: 'sunsets', name: 'Sunsets', slug: 'sunsets', imageCount: 24 },
  { id: 'wildlife', name: 'Marine Wildlife', slug: 'wildlife', imageCount: 18 },
  { id: 'landscapes', name: 'Coastal Landscapes', slug: 'landscapes', imageCount: 32 },
  { id: 'activities', name: 'Onboard Activities', slug: 'activities', imageCount: 15 },
  { id: 'crew', name: 'Crew & Guests', slug: 'crew', imageCount: 12 },
];

const mockTripTypes = [
  { id: 'day-cruise', name: 'Day Cruises', slug: 'day-cruise', imageCount: 28 },
  { id: 'sunset-cruise', name: 'Sunset Cruises', slug: 'sunset-cruise', imageCount: 15 },
  { id: 'private-charter', name: 'Private Charters', slug: 'private-charter', imageCount: 12 },
  { id: 'whale-watching', name: 'Whale Watching', slug: 'whale-watching', imageCount: 8 },
  { id: 'special-events', name: 'Special Events', slug: 'special-events', imageCount: 5 },
];

/**
 * Simulate API delay
 */
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Get gallery images with filtering and pagination
 */
export const getGalleryImages = async (
  filters: Partial<GalleryFilters> = {},
  page = 1,
  limit = 12
): Promise<GalleryApiResponse> => {
  // Simulate API call delay
  await delay(500);
  
  // Apply filters
  let filteredImages = [...mockGalleryImages];
  
  if (filters.category) {
    filteredImages = filteredImages.filter(image => 
      image.categories.includes(filters.category as string)
    );
  }
  
  if (filters.tripType) {
    filteredImages = filteredImages.filter(image => 
      image.tripTypes.includes(filters.tripType as string)
    );
  }
  
  if (filters.searchQuery) {
    const query = filters.searchQuery.toLowerCase();
    filteredImages = filteredImages.filter(
      image =>
        image.alt.toLowerCase().includes(query) ||
        image.caption?.toLowerCase().includes(query) ||
        image.categories.some(cat => cat.toLowerCase().includes(query)) ||
        image.tripTypes.some(type => type.toLowerCase().includes(query))
    );
  }
  
  // Apply sorting
  switch (filters.sortBy) {
    case 'oldest':
      filteredImages.sort((a, b) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );
      break;
    case 'featured':
      filteredImages.sort((a, b) => (a.featured === b.featured ? 0 : a.featured ? -1 : 1));
      break;
    case 'random':
      filteredImages = filteredImages.sort(() => Math.random() - 0.5);
      break;
    case 'newest':
    default:
      filteredImages.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
      break;
  }
  
  // Apply pagination
  const startIndex = (page - 1) * limit;
  const paginatedImages = filteredImages.slice(startIndex, startIndex + limit);
  
  return {
    images: paginatedImages,
    categories: mockCategories,
    tripTypes: mockTripTypes,
    total: filteredImages.length,
    page,
    limit,
    totalPages: Math.ceil(filteredImages.length / limit),
  };
};

/**
 * Get a single image by ID
 */
export const getImageById = async (id: string): Promise<GalleryImage | null> => {
  await delay(300);
  const image = mockGalleryImages.find(img => img.id === id);
  return image || null;
};

/**
 * Get related images (for lightbox navigation)
 */
export const getRelatedImages = async (
  currentId: string, 
  filters: Partial<GalleryFilters> = {}
): Promise<{ prevId: string | null; nextId: string | null }> => {
  const response = await getGalleryImages(filters, 1, 100); // Get all filtered images
  const index = response.images.findIndex(img => img.id === currentId);
  
  if (index === -1) {
    return { prevId: null, nextId: null };
  }
  
  return {
    prevId: index > 0 ? response.images[index - 1]?.id || null : null,
    nextId: index < response.images.length - 1 ? response.images[index + 1]?.id || null : null,
  };
};

/**
 * Upload a new image to the gallery
 */
export const uploadImage = async (
  file: File,
  metadata: Partial<GalleryImage> = {}
): Promise<ImageUploadResponse> => {
  // In a real implementation, this would upload to a server
  await delay(1000);
  
  // Simulate successful upload
  const imageUrl = URL.createObjectURL(file);
  
  return {
    id: `img-${Date.now()}`,
    url: imageUrl,
    width: 1920, // Would be determined from the actual image
    height: 1280,
    format: file.type.split('/')[1] || 'jpg',
    size: file.size,
    metadata: {
      ...metadata,
      originalName: file.name,
      uploadedAt: new Date().toISOString(),
    },
  };
};

/**
 * Update image metadata
 */
export const updateImage = async (
  id: string, 
  updates: Partial<GalleryImage>
): Promise<GalleryImage> => {
  await delay(500);
  
  const index = mockGalleryImages.findIndex(img => img.id === id);
  
  if (index === -1) {
    throw new Error('Image not found');
  }
  
  // In a real app, this would update the database
  const updatedImage = {
    ...mockGalleryImages[index],
    ...updates,
    updatedAt: new Date().toISOString(),
  };
  
  // Update the mock data
  mockGalleryImages[index] = updatedImage;
  
  return updatedImage;
};

/**
 * Delete an image
 */
export const deleteImage = async (id: string): Promise<boolean> => {
  await delay(500);
  
  const index = mockGalleryImages.findIndex(img => img.id === id);
  
  if (index === -1) {
    throw new Error('Image not found');
  }
  
  // In a real app, this would delete from the database
  mockGalleryImages.splice(index, 1);
  
  return true;
};

/**
 * Get all categories
 */
export const getCategories = async () => {
  await delay(300);
  return mockCategories;
};

/**
 * Get all trip types
 */
export const getTripTypes = async () => {
  await delay(300);
  return mockTripTypes;
};
