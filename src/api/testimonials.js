// Mock data for development
const mockTestimonials = [
    {
        id: '1',
        content: 'Absolutely amazing experience! The crew was professional and the views were breathtaking. Will definitely be back!',
        rating: 5,
        author: {
            id: 'user1',
            name: '<PERSON>',
            location: 'Auckland, New Zealand',
            avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
        },
        status: 'approved',
        featured: true,
        createdAt: '2025-06-15T10:30:00Z',
        updatedAt: '2025-06-15T10:30:00Z',
        testimonialDate: '2025-06-10',
        source: 'Google',
    },
    {
        id: '2',
        content: 'Great day out on the water. The boat was comfortable and the staff were very knowledgeable about the area.',
        rating: 4,
        author: {
            id: 'user2',
            name: '<PERSON>',
            location: 'Sydney, Australia',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        },
        status: 'approved',
        featured: true,
        createdAt: '2025-06-20T14:15:00Z',
        updatedAt: '2025-06-20T14:15:00Z',
        testimonialDate: '2025-06-18',
        source: 'TripAdvisor',
    },
    {
        id: '3',
        content: 'The sunset cruise was the highlight of our trip. Highly recommend!',
        rating: 5,
        author: {
            id: 'user3',
            name: '<PERSON>',
            location: 'London, UK',
            avatar: 'https://randomuser.me/api/portraits/women/65.jpg',
        },
        status: 'pending',
        featured: false,
        createdAt: '2025-07-01T09:45:00Z',
        updatedAt: '2025-07-01T09:45:00Z',
        testimonialDate: '2025-06-28',
    },
];
// Simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
// Get all testimonials with optional filters
export const getTestimonials = async (filters = {}) => {
    await delay(500); // Simulate network delay
    let result = [...mockTestimonials];
    // Apply filters
    if (filters.status && filters.status !== 'all') {
        result = result.filter(testimonial => testimonial.status === filters.status);
    }
    if (filters.rating) {
        result = result.filter(testimonial => testimonial.rating === filters.rating);
    }
    if (filters.featured !== undefined) {
        result = result.filter(testimonial => testimonial.featured === filters.featured);
    }
    if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        result = result.filter(testimonial => testimonial.content.toLowerCase().includes(searchTerm) ||
            testimonial.author.name.toLowerCase().includes(searchTerm) ||
            (testimonial.author.location?.toLowerCase().includes(searchTerm) ?? false));
    }
    // Apply sorting
    if (filters.sortBy) {
        switch (filters.sortBy) {
            case 'newest':
                result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
                break;
            case 'oldest':
                result.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
                break;
            case 'highest':
                result.sort((a, b) => b.rating - a.rating);
                break;
            case 'lowest':
                result.sort((a, b) => a.rating - b.rating);
                break;
        }
    }
    return result;
};
// Get a single testimonial by ID
export const getTestimonialById = async (id) => {
    await delay(300);
    return mockTestimonials.find(testimonial => testimonial.id === id);
};
// Create a new testimonial
export const createTestimonial = async (data) => {
    await delay(500);
    const newTestimonial = {
        id: `testimonial-${Date.now()}`,
        content: data.content,
        rating: data.rating,
        author: {
            id: `user-${Date.now()}`,
            name: data.authorName,
            role: data.authorRole,
            location: data.authorLocation,
        },
        status: data.status,
        featured: data.featured,
        source: data.source,
        testimonialDate: data.testimonialDate || new Date().toISOString().split('T')[0],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    };
    if (data.response) {
        newTestimonial.response = {
            content: data.response.content,
            author: data.response.author,
            date: new Date().toISOString(),
        };
    }
    // In a real app, this would be an API call
    mockTestimonials.unshift(newTestimonial);
    return newTestimonial;
};
// Update an existing testimonial
export const updateTestimonial = async (id, data) => {
    await delay(500);
    const index = mockTestimonials.findIndex(t => t.id === id);
    if (index === -1) {
        throw new Error('Testimonial not found');
    }
    const updatedTestimonial = {
        ...mockTestimonials[index],
        ...(data.content && { content: data.content }),
        ...(data.rating && { rating: data.rating }),
        ...(data.status && { status: data.status }),
        ...(data.featured !== undefined && { featured: data.featured }),
        ...(data.testimonialDate && { testimonialDate: data.testimonialDate }),
        ...(data.source && { source: data.source }),
        updatedAt: new Date().toISOString(),
        author: {
            ...mockTestimonials[index].author,
            ...(data.authorName && { name: data.authorName }),
            ...(data.authorRole !== undefined && { role: data.authorRole }),
            ...(data.authorLocation !== undefined && { location: data.authorLocation }),
        },
    };
    if (data.response) {
        updatedTestimonial.response = {
            content: data.response.content,
            author: data.response.author,
            date: updatedTestimonial.response?.date || new Date().toISOString(),
        };
    }
    else if (data.response === null) {
        delete updatedTestimonial.response;
    }
    // In a real app, this would be an API call
    mockTestimonials[index] = updatedTestimonial;
    return updatedTestimonial;
};
// Delete a testimonial
export const deleteTestimonial = async (id) => {
    await delay(300);
    const index = mockTestimonials.findIndex(t => t.id === id);
    if (index !== -1) {
        mockTestimonials.splice(index, 1);
    }
};
// Get featured testimonials for the homepage
export const getFeaturedTestimonials = async (limit = 3) => {
    await delay(300);
    return mockTestimonials
        .filter(testimonial => testimonial.status === 'approved' && testimonial.featured)
        .slice(0, limit);
};
// Submit a new testimonial (public form)
export const submitTestimonial = async (data) => {
    return createTestimonial({
        ...data,
        status: 'pending', // New submissions need approval
        featured: false, // Default to not featured
    });
};
