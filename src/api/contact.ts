import { z } from 'zod';

// Define the contact form schema for the API
const contactFormSchema = z.object({
  name: z.string().min(2),
  email: z.string().email(),
  phone: z.string().optional(),
  subject: z.string().min(5),
  message: z.string().min(10),
  privacyPolicy: z.boolean(),
});

type ContactFormData = z.infer<typeof contactFormSchema>;

// Environment variables (would be set in your .env file)
const RECAPTCHA_SECRET_KEY = process.env.NEXT_PUBLIC_RECAPTCHA_SECRET_KEY || '';
const CONTACT_EMAIL = process.env.CONTACT_EMAIL || '<EMAIL>';
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';

/**
 * Verify reCAPTCHA token with Google's API
 */
async function verifyRecaptcha(token: string): Promise<boolean> {
  if (!token) return false;
  
  // In development, you might want to skip reCAPTCHA verification
  if (process.env.NODE_ENV === 'development' && !process.env.FORCE_RECAPTCHA) {
    console.log('Skipping reCAPTCHA verification in development');
    return true;
  }
  
  try {
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `secret=${encodeURIComponent(RECAPTCHA_SECRET_KEY)}&response=${encodeURIComponent(token)}`,
    });
    
    const data = await response.json();
    return data.success === true && data.score > 0.5; // Only accept scores above 0.5
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    return false;
  }
}

/**
 * Send contact form email using a serverless function or email service
 */
async function sendContactEmail(formData: ContactFormData): Promise<boolean> {
  try {
    // In a real implementation, this would call your backend API
    // or an email service like SendGrid, Mailchimp, etc.
    
    // For now, we'll simulate a successful email send
    console.log('Sending contact form email:', formData);
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock response - in a real app, this would be the response from your email service
    return true;
  } catch (error) {
    console.error('Error sending contact email:', error);
    return false;
  }
}

/**
 * Process contact form submission
 */
export async function submitContactForm(formData: unknown): Promise<{
  success: boolean;
  message: string;
  errors?: Record<string, string>;
}> {
  try {
    // Validate form data
    const validation = contactFormSchema.safeParse(formData);
    
    if (!validation.success) {
      // Format validation errors
      const errors: Record<string, string> = {};
      validation.error.issues.forEach(issue => {
        const path = issue.path.join('.');
        errors[path] = issue.message;
      });
      
      return {
        success: false,
        message: 'Validation failed',
        errors,
      };
    }
    
    // Verify reCAPTCHA
    const recaptchaToken = (formData as any).recaptchaToken;
    const isRecaptchaValid = await verifyRecaptcha(recaptchaToken);
    
    if (!isRecaptchaValid) {
      return {
        success: false,
        message: 'reCAPTCHA verification failed. Please try again.',
        errors: { recaptcha: 'Verification failed' },
      };
    }
    
    // Send email
    const emailSent = await sendContactEmail(validation.data);
    
    if (!emailSent) {
      throw new Error('Failed to send email');
    }
    
    // Log the submission (in a real app, you might save this to a database)
    console.log('Contact form submitted successfully:', validation.data);
    
    return {
      success: true,
      message: 'Thank you for your message! We will get back to you soon.',
    };
  } catch (error) {
    console.error('Error processing contact form:', error);
    
    return {
      success: false,
      message: 'An unexpected error occurred. Please try again later or contact us directly.',
    };
  }
}

/**
 * Mock function for development/testing
 */
export const mockSubmitContactForm = async (formData: unknown) => {
  console.log('Mock contact form submission:', formData);
  
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Simulate random success/failure for testing
  const isSuccess = Math.random() > 0.3; // 70% success rate for testing
  
  if (isSuccess) {
    return {
      success: true,
      message: 'Thank you for your message! We will get back to you soon.',
    };
  } else {
    return {
      success: false,
      message: 'Failed to send message. Please try again later.',
      errors: {
        server: 'An unexpected error occurred. Please try again.'
      }
    };
  }
};

// For server-side usage
if (typeof window === 'undefined') {
  // This code will only run on the server
  // You can add server-specific code here if needed
}
