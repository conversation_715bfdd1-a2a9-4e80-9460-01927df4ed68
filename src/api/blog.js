// Helper function to handle API responses
const handleResponse = async (response) => {
    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Something went wrong');
    }
    return response.json();
};
// Get all blog posts
export const getBlogPosts = async () => {
    const response = await fetch('/api/blog/posts');
    return handleResponse(response);
};
// Get a single blog post by slug
export const getBlogPostBySlug = async (slug) => {
    const response = await fetch(`/api/blog/posts/${slug}`);
    return handleResponse(response);
};
// Create a new blog post
export const createBlogPost = async (postData) => {
    const response = await fetch('/api/blog/posts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData),
    });
    return handleResponse(response);
};
// Update an existing blog post
export const updateBlogPost = async (id, postData) => {
    const response = await fetch(`/api/blog/posts/${id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData),
    });
    return handleResponse(response);
};
// Delete a blog post
export const deleteBlogPost = async (id) => {
    const response = await fetch(`/api/blog/posts/${id}`, {
        method: 'DELETE',
    });
    return handleResponse(response);
};
// Upload a featured image for a blog post
export const uploadFeaturedImage = async (file) => {
    const formData = new FormData();
    formData.append('file', file);
    const response = await fetch('/api/blog/upload', {
        method: 'POST',
        body: formData,
    });
    return handleResponse(response);
};
