import { BlogPost, BlogPostFormData } from '@/types/blog';

// Helper function to handle API responses
const handleResponse = async (response: Response) => {
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Something went wrong');
  }
  return response.json();
};

// Get all blog posts
export const getBlogPosts = async (): Promise<BlogPost[]> => {
  const response = await fetch('/api/blog/posts');
  return handleResponse(response);
};

// Get a single blog post by slug
export const getBlogPostBySlug = async (slug: string): Promise<BlogPost> => {
  const response = await fetch(`/api/blog/posts/${slug}`);
  return handleResponse(response);
};

// Create a new blog post
export const createBlogPost = async (postData: BlogPostFormData): Promise<BlogPost> => {
  const response = await fetch('/api/blog/posts', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(postData),
  });
  return handleResponse(response);
};

// Update an existing blog post
export const updateBlogPost = async (id: string, postData: Partial<BlogPostFormData>): Promise<BlogPost> => {
  const response = await fetch(`/api/blog/posts/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(postData),
  });
  return handleResponse(response);
};

// Delete a blog post
export const deleteBlogPost = async (id: string): Promise<void> => {
  const response = await fetch(`/api/blog/posts/${id}`, {
    method: 'DELETE',
  });
  return handleResponse(response);
};

// Upload a featured image for a blog post
export const uploadFeaturedImage = async (file: File): Promise<{ url: string }> => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('/api/blog/upload', {
    method: 'POST',
    body: formData,
  });
  return handleResponse(response);
};
