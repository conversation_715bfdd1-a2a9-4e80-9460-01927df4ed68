import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// English translations
const en = {
  translation: {
    // App
    app: {
      name: 'Sailing Serai',
    },
    // Navigation
    nav: {
      home: 'Home',
      destinations: 'Destinations',
      yachts: 'Our Yachts',
      about: 'About Us',
      contact: 'Contact',
      login: 'Login',
      logout: 'Logout',
      profile: 'Profile',
      bookNow: 'Book Now',
    },
    // Hero Section
    hero: {
      title: 'Discover the Magic of Sailing',
      subtitle: 'Experience the beauty of Auckland\'s waters with our exclusive sailing adventures.',
      cta: {
        primary: 'Book Your Adventure',
        secondary: 'Learn More'
      }
    },
    // Home page
    home: {
      featured: {
        title: 'Featured Destinations',
        subtitle: 'Explore our most popular sailing destinations around Auckland\'s stunning coastline and islands.',
        destinations: {
          caribbean: 'Caribbean',
          mediterranean: 'Mediterranean',
          southeastAsia: 'Southeast Asia',
          sailingIn: '{{destination}} Sailing',
          description: 'Experience the best sailing routes in {{destination}} with our expert guides.',
          viewDetails: 'View Details'
        }
      }
    },
    // Common
    common: {
      loading: 'Loading...',
      copyright: ' Sailing Serai. All rights reserved.',
      imagePlaceholder: '{{destination}} Image',
      bookNow: 'Book Now',
      learnMore: 'Learn More',
    },
    
    // Booking
    booking: {
      selectDate: 'Select a Date & Time',
      selectTime: 'Select Time',
      numberOfGuests: 'Number of Guests',
      maxGuests: 'Maximum {{count}} guests',
      contactInfo: 'Your Information',
      fullName: 'Full Name',
      phoneNumber: 'Phone Number',
      specialRequests: 'Special Requests',
      specialRequestsPlaceholder: 'Any special requirements or notes...',
      bookNow: 'Book Now',
      booking: 'Booking...',
      selected: 'Selected',
      unavailable: 'Unavailable',
      selectedDate: 'Selected: {{date}} at {{time}}',
      bookingSuccess: 'Your booking has been confirmed!',
      bookingError: 'There was an error processing your booking. Please try again.'
    },
    
    // Experiences Section
    experiences: {
      title: 'Sailing Experiences',
      subtitle: 'Discover our range of unforgettable sailing adventures in the beautiful waters of Auckland.',
      featured: 'Featured',
      maxGuests: 'Up to 6 guests',
      cta: {
        title: 'Ready for an Adventure?',
        subtitle: 'Book your sailing experience today and create memories that will last a lifetime.',
        button: 'View All Experiences'
      },
      // Individual experiences will be added here with their IDs
      1: {
        title: 'Sunset Sailing Experience',
        description: 'Enjoy a breathtaking sunset over the Auckland skyline with our premium sailing experience. Includes light refreshments and a complimentary glass of New Zealand wine.',
        duration: '2 hours'
      },
      2: {
        title: 'Full Day Island Adventure',
        description: 'Spend a full day exploring the beautiful islands of the Hauraki Gulf. Includes lunch, snorkeling equipment, and guided walks.',
        duration: '8 hours'
      },
      3: {
        title: 'Whale Watching Expedition',
        description: 'Join us for an unforgettable whale watching experience. Our expert guides will take you to the best spots to see these magnificent creatures in their natural habitat.',
        duration: '4 hours'
      },
      4: {
        title: 'Private Charter',
        description: 'Experience the ultimate in luxury with a private charter. Perfect for special occasions, corporate events, or just a day out with friends and family.',
        duration: 'Custom'
      },
      5: {
        title: 'Learn to Sail',
        description: 'Always wanted to learn how to sail? Join our experienced instructors for a hands-on sailing lesson in the beautiful waters of Auckland.',
        duration: '6 hours'
      },
      6: {
        title: 'Overnight Sailing',
        description: 'Experience the magic of sleeping under the stars on our overnight sailing adventure. Includes dinner, breakfast, and all necessary equipment.',
        duration: 'Overnight'
      }
    }
  }
};

// Te Reo Māori translations
const mi = {
  translation: {
    // App
    app: {
      name: 'Sailing Serai',
    },
    // Navigation
    nav: {
      home: 'Kāinga',
      destinations: 'Wāhi Haere',
      yachts: 'Ō Mātou Waka',
      about: 'Mō Mātou',
      contact: 'Whakapā Mai',
      login: 'Takiuru',
      logout: 'Takiputa',
      profile: 'Kōtaha',
      bookNow: 'Pānui Ināianei',
    },
    // Hero Section
    hero: {
      title: 'Tūhuratia te Mīharo o te Rērere Waka',
      subtitle: 'Pākiki ki ngā wai ātaahua o Tāmaki Makaurau mā ā mātou haerenga rērere waka motuhake.',
      cta: {
        primary: 'Pānui Tō Haerenga',
        secondary: 'Ako Anō'
      }
    },
    // Home page
    home: {
      hero: {
        title: 'Nau mai ki te ',
        appName: 'Sailing Serai',
        subtitle: 'Kimihia te wheako e tino hiahiatia ana māu i roto i ā mātou kōwhiringa waka, i ngā haerenga e taea ana i ngā wai ātaahua o Tāmaki Makaurau.',
        cta: {
          primary: 'Tirotirohia ngā Waka',
          secondary: 'Kōrero Anō'
        }
      },
      featured: {
        title: 'Ngā Wāhi Tino Rongonui',
        subtitle: 'Toro atu ki ngā wāhi e tino hōnorehia ana e ngā kaumoana huri noa i ngā tahatai me ngā moutere ātaahua o Tāmaki Makaurau.',
        destinations: {
          caribbean: 'Karipiana',
          mediterranean: 'Te Moana Waenganui',
          southeastAsia: 'Āhia Ki Te Tonga Rāwhiti',
          sailingIn: 'Te Rērere Waka i {{destination}}',
          description: 'Pākiki ki ngā huarahi e tino pai ana mō te hoe waka i {{destination}} mā ō mātou kaiārahi mōhio.',
          viewDetails: 'Tirohia ngā Kōrero'
        }
      }
    },
    // Common
    common: {
      loading: 'Tukatuka ana...',
      copyright: ' Sailing Serai. Ngā mana katoa kua manaakitia.',
      imagePlaceholder: 'Te Whakaahua o {{destination}}',
      bookNow: 'Pānui Ināianei',
      learnMore: 'Ako Anō',
    },
    
    // Experiences Section
    experiences: {
      title: 'Ngā Wheako Rērere Waka',
      subtitle: 'Tūhuratia ā mātou momo haerenga rērere waka mīharo ki ngā wai ātaahua o Tāmaki Makaurau.',
      featured: 'Tino Rongonui',
      maxGuests: 'Tae atu ki te 6 manuhiri',
      cta: {
        title: 'Kua Reri Mō Tētahi Haerenga?',
        subtitle: 'Pānuitia tō wheako rērere waka i tēnei rā, ka waiho hei maumaharatanga mō ake tonu atu.',
        button: 'Tirohia Ngā Wheako Katoa'
      },
      // Individual experiences will be added here with their IDs
      1: {
        title: 'Te Wheako Rērere Waka i te Ahiahi',
        description: 'Pākiki i te tohu o te rā i runga i te rangi o Tāmaki Makaurau mā tā mātou wheako rērere waka kounga. Ka kīia ngā kai māmā me tētahi karaehe wāina o Aotearoa māu.',
        duration: '2 haora'
      },
      2: {
        title: 'Te Haerenga Moutere Rā Roa',
        description: 'Noho mō te rā katoa e tūhura ana i ngā moutere ātaahua o Te Moananui-ā-Kiwa. Ka kīia te tina, ngā taputapu mō te kauhoe, me ngā haerenga haere mā ngā kaiārahi.',
        duration: '8 haora'
      },
      3: {
        title: 'Te Haerenga Tirotiro Tohorā',
        description: 'Hono mai mō tētahi wheako tirotiro tohorā e kore e warewaretia. Ka ārahi ngā kaiārahi mōhiotia ki ngā wāhi pai rawa atu ki te kite i ēnei kararehe mīharo i tō rātou wāhi māori.',
        duration: '4 haora'
      },
      4: {
        title: 'Rēti Motuhake',
        description: 'Pākiki i te tino hirahira o te rērere waka mā te rēti motuhake. Tino tika mō ngā hui motuhake, ngā hui umanga, rānei he rā noho tahi me ngā hoa me te whānau.',
        duration: 'Whakaritea'
      },
      5: {
        title: 'Ako ki te Rērere Waka',
        description: 'I hiahia tonu koe ki te ako me pēhea te rērere waka? Hono mai ki ā mātou kaiako mō tētahi akoranga ruku ringa ki ngā wai ātaahua o Tāmaki Makaurau.',
        duration: '6 haora'
      },
      6: {
        title: 'Rērere Pō',
        description: 'Pākiki i te mīharo o te moe i raro i ngā whetū i tā mātou haerenga rērere pō. Ka kīia te hapa, te parakuihi, me ngā taputapu katoa e hiahiatia ana.',
        duration: 'Pō'
      }
    }
  }
};

const resources = {
  en,
  mi,
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
  });

export default i18n;
