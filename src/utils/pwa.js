/**
 * PWA (Progressive Web App) utilities
 */
// State
let deferredPrompt = null;
let isAppInstalled = false;
/**
 * Initialize PWA features
 */
export function initPWA() {
    if (typeof window === 'undefined')
        return;
    // Check if the app is already installed
    if (window.matchMedia('(display-mode: standalone)').matches ||
        window.navigator.standalone ||
        document.referrer.includes('android-app://')) {
        isAppInstalled = true;
    }
    // Listen for beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (e) => {
        // Prevent the default prompt
        e.preventDefault();
        // Stash the event so it can be triggered later
        deferredPrompt = e;
        // Dispatch a custom event to notify the app
        window.dispatchEvent(new Event('canInstallPWA'));
    });
    // Track app installation
    window.addEventListener('appinstalled', () => {
        isAppInstalled = true;
        deferredPrompt = null;
        // Dispatch a custom event to notify the app
        window.dispatchEvent(new Event('pwaInstalled'));
        // Log the installation
        console.log('PWA installed successfully');
    });
    // Initialize service worker
    if ('serviceWorker' in navigator) {
        registerServiceWorker();
    }
}
/**
 * Register the service worker
 */
async function registerServiceWorker() {
    try {
        const registration = await navigator.serviceWorker.register('/service-worker.js');
        console.log('ServiceWorker registration successful with scope: ', registration.scope);
        // Check for updates
        registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
                newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                        // New update available
                        window.dispatchEvent(new Event('updateAvailable'));
                    }
                });
            }
        });
        // Check for updates on navigation
        if (navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({ type: 'PING' });
        }
        return registration;
    }
    catch (error) {
        console.error('ServiceWorker registration failed: ', error);
        return null;
    }
}
/**
 * Get the PWA install prompt
 * @returns PWA install prompt object
 */
export function getPWAInstallPrompt() {
    return {
        event: deferredPrompt || undefined,
        canInstall: !!deferredPrompt && !isAppInstalled,
        isInstalled,
        prompt: async () => {
            if (!deferredPrompt)
                return false;
            try {
                // Show the install prompt
                await deferredPrompt.prompt();
                // Wait for the user to respond to the prompt
                const { outcome } = await deferredPrompt.userChoice;
                // Clear the deferred prompt
                deferredPrompt = null;
                return outcome === 'accepted';
            }
            catch (error) {
                console.error('Error showing install prompt:', error);
                return false;
            }
        },
    };
}
/**
 * Check if the app is running in standalone mode
 * @returns True if the app is running in standalone mode
 */
export function isRunningStandalone() {
    if (typeof window === 'undefined')
        return false;
    return (window.matchMedia('(display-mode: standalone)').matches ||
        window.navigator.standalone ||
        document.referrer.includes('android-app://'));
}
/**
 * Check if the app is running offline
 * @returns True if the app is offline
 */
export function isOffline() {
    return !navigator.onLine;
}
/**
 * Add an offline/online change listener
 * @param callback - Callback function that receives the online status
 * @returns Function to remove the event listener
 */
export function onConnectionChange(callback) {
    const handleOnline = () => callback(true);
    const handleOffline = () => callback(false);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    // Initial check
    callback(navigator.onLine);
    // Return cleanup function
    return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
    };
}
/**
 * Request notification permission
 * @returns Promise that resolves to the permission status
 */
export async function requestNotificationPermission() {
    if (!('Notification' in window)) {
        return 'denied';
    }
    if (Notification.permission === 'default') {
        return await Notification.requestPermission();
    }
    return Notification.permission;
}
/**
 * Show a notification
 * @param title - Notification title
 * @param options - Notification options
 * @returns Notification object or null if not supported
 */
export function showNotification(title, options = {}) {
    if (!('Notification' in window) || Notification.permission !== 'granted') {
        return null;
    }
    // Set default options
    const defaultOptions = {
        icon: '/logo192.png', // Default icon
        badge: '/logo192.png', // Default badge
        vibrate: [100, 50, 100], // Default vibration pattern
        ...options,
    };
    // Show the notification
    return new Notification(title, defaultOptions);
}
/**
 * Check if the browser supports PWA features
 * @returns Object with feature support information
 */
export function checkPWASupport() {
    if (typeof window === 'undefined') {
        return {
            isSupported: false,
            serviceWorker: false,
            installPrompt: false,
            notifications: false,
            offline: false,
            push: false,
        };
    }
    return {
        isSupported: 'serviceWorker' in navigator && 'caches' in window,
        serviceWorker: 'serviceWorker' in navigator,
        installPrompt: 'BeforeInstallPromptEvent' in window,
        notifications: 'Notification' in window,
        offline: 'serviceWorker' in navigator && 'SyncManager' in window,
        push: 'PushManager' in window,
    };
}
// Initialize PWA when the module loads
if (typeof window !== 'undefined') {
    initPWA();
}
