import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { lazy, Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
/**
 * Creates a lazy-loaded component with retry logic
 * @param componentImport - Dynamic import function for the component
 * @param retries - Number of retry attempts
 * @param interval - Time between retries in ms
 * @returns Lazy component with preload capability
 */
export function lazyWithRetry(componentImport, retries = 3, interval = 1000) {
    const component = (() => {
        // Track retry count
        let retryCount = 0;
        // The actual lazy import with retry logic
        const load = () => new Promise((resolve, reject) => {
            componentImport()
                .then(resolve)
                .catch((error) => {
                setTimeout(() => {
                    if (retryCount < retries) {
                        retryCount++;
                        // Recursively retry
                        load().then(resolve, reject);
                    }
                    else {
                        reject(error);
                    }
                }, interval);
            });
        });
        return lazy(load);
    })();
    // Add preload method for prefetching
    component.preload = () => componentImport();
    return component;
}
/**
 * Fallback component for Suspense
 */
const DefaultFallback = () => (_jsx("div", { className: "flex items-center justify-center w-full h-full min-h-[200px]", children: _jsxs("div", { className: "space-y-3 w-full max-w-[300px]", children: [_jsx(Skeleton, { className: "h-4 w-full" }), _jsx(Skeleton, { className: "h-4 w-4/5" }), _jsx(Skeleton, { className: "h-4 w-3/4" })] }) }));
/**
 * Higher-order component that wraps a lazy-loaded component with Suspense
 * @param Component - Lazy-loaded component
 * @param fallback - Optional custom fallback component
 * @returns Component wrapped with Suspense
 */
export function withSuspense(Component, fallback = _jsx(DefaultFallback, {})) {
    return function WithSuspense(props) {
        return (_jsx(Suspense, { fallback: fallback, children: _jsx(Component, { ...props }) }));
    };
}
/**
 * Preload a lazy component
 * @param lazyComponent - Lazy component with preload method
 * @returns Promise that resolves when the component is loaded
 */
export const preloadComponent = (lazyComponent) => {
    return lazyComponent.preload().then(() => undefined);
};
/**
 * Prefetch a route component when the user hovers over a link
 * @param to - Route path that matches the lazy component
 */
export const prefetchRoute = (to) => {
    // This would be implemented based on your routing solution
    // For example, with React Router, you might have a route configuration
    // that maps paths to lazy components
    console.log(`Prefetching route: ${to}`);
    // Implementation would go here
};
/**
 * Hook to preload a component when the component mounts
 * @param lazyComponent - Lazy component with preload method
 * @param enabled - Whether to enable preloading (default: true)
 */
export const usePreload = (lazyComponent, enabled = true) => {
    React.useEffect(() => {
        if (enabled) {
            preloadComponent(lazyComponent).catch(console.error);
        }
    }, [lazyComponent, enabled]);
};
