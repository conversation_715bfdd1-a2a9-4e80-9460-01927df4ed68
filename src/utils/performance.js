/**
 * Performance monitoring and metrics collection utilities
 */
// Performance metrics storage
const metrics = {};
const marks = {};
const measures = {};
/**
 * Performance metrics to track
 */
export const METRICS = {
    // Core Web Vitals
    LCP: 'largest-contentful-paint',
    FID: 'first-input-delay',
    CLS: 'cumulative-layout-shift',
    FCP: 'first-contentful-paint',
    TTFB: 'time-to-first-byte',
    // Custom metrics
    APP_LOAD: 'app-load',
    ROUTE_CHANGE: 'route-change',
    IMAGE_LOAD: 'image-load',
    API_CALL: 'api-call',
};
/**
 * Initialize performance monitoring
 */
export function initPerformanceMonitoring() {
    if (typeof window === 'undefined')
        return;
    // Register the PerformanceObserver for Core Web Vitals
    if ('PerformanceObserver' in window) {
        // Observe LCP (Largest Contentful Paint)
        const lcpObserver = new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            const lastEntry = entries[entries.length - 1];
            // Only record the LCP if the page wasn't hidden prior to the first input
            if (lastEntry && !lastEntry.hadRecentInput) {
                recordMetric(METRICS.LCP, lastEntry.startTime);
            }
        });
        lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
        // Observe CLS (Cumulative Layout Shift)
        const clsObserver = new PerformanceObserver((entryList) => {
            // Calculate CLS as the sum of all layout shift scores
            const entries = entryList.getEntries();
            const clsScore = entries.reduce((sum, entry) => sum + (entry.hadRecentInput ? 0 : entry.value), 0);
            if (clsScore > 0) {
                recordMetric(METRICS.CLS, clsScore);
            }
        });
        clsObserver.observe({ type: 'layout-shift', buffered: true });
        // Observe FID (First Input Delay)
        const fidObserver = new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            for (const entry of entries) {
                if (entry.entryType === 'first-input') {
                    recordMetric(METRICS.FID, entry.processingStart - entry.startTime);
                }
            }
        });
        fidObserver.observe({ type: 'first-input', buffered: true });
        // Observe FCP (First Contentful Paint)
        const fcpObserver = new PerformanceObserver((entryList) => {
            const entries = entryList.getEntriesByName('first-contentful-paint');
            const entry = entries[0];
            if (entry) {
                recordMetric(METRICS.FCP, entry.startTime);
            }
        });
        fcpObserver.observe({ type: 'paint', buffered: true });
    }
    // Track navigation timing
    if ('performance' in window) {
        // Record TTFB (Time to First Byte)
        const timing = window.performance.timing;
        if (timing) {
            const ttfb = timing.responseStart - timing.navigationStart;
            recordMetric(METRICS.TTFB, ttfb);
        }
        // Record app load time
        if (document.readyState === 'complete') {
            recordAppLoadTime();
        }
        else {
            window.addEventListener('load', recordAppLoadTime);
        }
    }
    // Log metrics to console in development
    if (process.env.NODE_ENV === 'development') {
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('Performance Metrics:', metrics);
            }, 0);
        });
    }
}
/**
 * Record application load time
 */
function recordAppLoadTime() {
    if (window.performance) {
        const now = new Date().getTime();
        const navStart = window.performance.timing?.navigationStart || now;
        const loadTime = now - navStart;
        recordMetric(METRICS.APP_LOAD, loadTime);
    }
}
/**
 * Record a performance metric
 * @param name - Metric name
 * @param value - Metric value
 */
export function recordMetric(name, value) {
    if (typeof value !== 'number' || !isFinite(value))
        return;
    // Store the metric
    metrics[name] = value;
    // Log to analytics service in production
    if (process.env.NODE_ENV === 'production') {
        // Replace with your analytics service integration
        // Example: logToAnalytics(name, value);
    }
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
        console.log(`[Perf] ${name}: ${value.toFixed(2)}ms`);
    }
}
/**
 * Start a performance measurement
 * @param name - Measurement name
 */
export function startMeasure(name) {
    if (typeof performance === 'undefined')
        return;
    marks[name] = performance.now();
}
/**
 * End a performance measurement and record the duration
 * @param name - Measurement name
 * @returns The duration in milliseconds, or undefined if the measurement couldn't be completed
 */
export function endMeasure(name) {
    if (typeof performance === 'undefined' || marks[name] === undefined)
        return undefined;
    const end = performance.now();
    const start = marks[name];
    const duration = end - start;
    // Record the measurement
    measures[name] = duration;
    recordMetric(name, duration);
    // Clean up
    delete marks[name];
    return duration;
}
/**
 * Measure the execution time of an async function
 * @param fn - Async function to measure
 * @param name - Measurement name
 * @returns The result of the function
 */
export async function measureAsync(fn, name) {
    startMeasure(name);
    try {
        return await fn();
    }
    finally {
        endMeasure(name);
    }
}
/**
 * Measure the execution time of a synchronous function
 * @param fn - Function to measure
 * @param name - Measurement name
 * @returns The result of the function
 */
export function measureSync(fn, name) {
    startMeasure(name);
    try {
        return fn();
    }
    finally {
        endMeasure(name);
    }
}
/**
 * Get all recorded metrics
 * @returns Object containing all recorded metrics
 */
export function getMetrics() {
    return { ...metrics };
}
/**
 * Get all recorded measures
 * @returns Object containing all recorded measures
 */
export function getMeasures() {
    return { ...measures };
}
export function trackPageLoad() {
    if (typeof performance === 'undefined')
        return;
    // Get navigation timing
    const [navigationEntry] = performance.getEntriesByType('navigation');
    if (navigationEntry) {
        // Record navigation timing metrics
        const { domainLookupStart, domainLookupEnd, connectStart, connectEnd, requestStart, responseStart, responseEnd, domInteractive, domContentLoadedEventEnd, loadEventStart, activationStart = 0, } = navigationEntry;
        // Calculate timing metrics
        const timing = {
            dns: domainLookupEnd - domainLookupStart,
            tcp: connectEnd - connectStart,
            ttfb: responseStart - requestStart,
            response: responseEnd - responseStart,
            dom: domInteractive - responseEnd,
            interactive: domInteractive - activationStart,
            domContentLoaded: domContentLoadedEventEnd - activationStart,
            load: loadEventStart - activationStart,
        };
        // Record metrics
        Object.entries(timing).forEach(([key, value]) => {
            if (value > 0) {
                recordMetric(`page_${key}`, value);
            }
        });
    }
    // Track LCP (Largest Contentful Paint)
    const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
    if (lcpEntries.length > 0) {
        const lcp = lcpEntries[lcpEntries.length - 1];
        if (lcp && !lcp.hadRecentInput) {
            recordMetric(METRICS.LCP, lcp.startTime);
        }
    }
    // Track CLS (Cumulative Layout Shift)
    const clsEntries = performance.getEntriesByType('layout-shift');
    if (clsEntries.length > 0) {
        const clsScore = clsEntries.reduce((sum, entry) => sum + (entry.hadRecentInput ? 0 : entry.value), 0);
        if (clsScore > 0) {
            recordMetric(METRICS.CLS, clsScore);
        }
    }
}
/**
 * Track route changes
 * @param to - Target route
 * @param from - Source route (optional)
 */
export function trackRouteChange(to, from) {
    if (typeof performance === 'undefined')
        return;
    const now = performance.now();
    const routeChangeTime = now - (window.performance.timing?.navigationStart || now);
    recordMetric(METRICS.ROUTE_CHANGE, routeChangeTime);
    if (process.env.NODE_ENV === 'development') {
        console.log(`Route changed from ${from || 'undefined'} to ${to} in ${routeChangeTime.toFixed(2)}ms`);
    }
}
/**
 * Track image load performance
 * @param src - Image source URL
 * @param startTime - Optional start time (performance.now())
 */
export function trackImageLoad(src, startTime) {
    if (typeof performance === 'undefined')
        return;
    const loadTime = startTime ? performance.now() - startTime : 0;
    recordMetric(METRICS.IMAGE_LOAD, loadTime);
    if (process.env.NODE_ENV === 'development') {
        console.log(`Image loaded: ${src} in ${loadTime.toFixed(2)}ms`);
    }
}
/**
 * Track API call performance
 * @param url - API endpoint URL
 * @param method - HTTP method
 * @param duration - Request duration in milliseconds
 * @param status - HTTP status code
 */
export function trackApiCall(url, method, duration, status) {
    recordMetric(METRICS.API_CALL, duration);
    if (process.env.NODE_ENV === 'development') {
        console.log(`API ${method} ${url} (${status}) in ${duration.toFixed(2)}ms`);
    }
}
// Initialize performance monitoring when the module loads
if (typeof window !== 'undefined') {
    initPerformanceMonitoring();
}
