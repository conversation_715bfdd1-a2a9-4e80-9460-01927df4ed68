import { ComponentType, lazy, LazyExoticComponent, ReactNode, Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

/**
 * Type for the lazy component with preload method
 */
type LazyComponentType<T extends ComponentType<any>> = LazyExoticComponent<T> & {
  preload: () => Promise<{ default: T }>;
};

/**
 * Creates a lazy-loaded component with retry logic
 * @param componentImport - Dynamic import function for the component
 * @param retries - Number of retry attempts
 * @param interval - Time between retries in ms
 * @returns Lazy component with preload capability
 */
export function lazyWithRetry<T extends ComponentType<any>>(
  componentImport: () => Promise<{ default: T }>,
  retries = 3,
  interval = 1000
): LazyComponentType<T> {
  const component = (() => {
    // Track retry count
    let retryCount = 0;
    
    // The actual lazy import with retry logic
    const load = (): Promise<{ default: T }> =>
      new Promise((resolve, reject) => {
        componentImport()
          .then(resolve)
          .catch((error) => {
            setTimeout(() => {
              if (retryCount < retries) {
                retryCount++;
                // Recursively retry
                load().then(resolve, reject);
              } else {
                reject(error);
              }
            }, interval);
          });
      });

    return lazy(load);
  })() as LazyComponentType<T>;

  // Add preload method for prefetching
  component.preload = () => componentImport();
  
  return component;
}

/**
 * Fallback component for Suspense
 */
const DefaultFallback = () => (
  <div className="flex items-center justify-center w-full h-full min-h-[200px]">
    <div className="space-y-3 w-full max-w-[300px]">
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-4/5" />
      <Skeleton className="h-4 w-3/4" />
    </div>
  </div>
);

/**
 * Higher-order component that wraps a lazy-loaded component with Suspense
 * @param Component - Lazy-loaded component
 * @param fallback - Optional custom fallback component
 * @returns Component wrapped with Suspense
 */
export function withSuspense<T extends object>(
  Component: LazyComponentType<ComponentType<T>>,
  fallback: ReactNode = <DefaultFallback />
) {
  return function WithSuspense(props: T) {
    return (
      <Suspense fallback={fallback}>
        <Component {...props} />
      </Suspense>
    );
  };
}

/**
 * Preload a lazy component
 * @param lazyComponent - Lazy component with preload method
 * @returns Promise that resolves when the component is loaded
 */
export const preloadComponent = <T extends ComponentType<any>>(
  lazyComponent: LazyComponentType<T>
): Promise<void> => {
  return lazyComponent.preload().then(() => undefined);
};

/**
 * Prefetch a route component when the user hovers over a link
 * @param to - Route path that matches the lazy component
 */
export const prefetchRoute = (to: string) => {
  // This would be implemented based on your routing solution
  // For example, with React Router, you might have a route configuration
  // that maps paths to lazy components
  console.log(`Prefetching route: ${to}`);
  // Implementation would go here
};

/**
 * Hook to preload a component when the component mounts
 * @param lazyComponent - Lazy component with preload method
 * @param enabled - Whether to enable preloading (default: true)
 */
export const usePreload = <T extends ComponentType<any>>(
  lazyComponent: LazyComponentType<T>,
  enabled = true
) => {
  React.useEffect(() => {
    if (enabled) {
      preloadComponent(lazyComponent).catch(console.error);
    }
  }, [lazyComponent, enabled]);
};
