/**
 * Image optimization utilities for generating responsive image sets
 * with WebP and AVIF formats where supported
 */
// Default image quality settings
const DEFAULT_QUALITY = 80;
const DEFAULT_FORMATS = ['webp', 'avif', 'original'];
/**
 * Breakpoints for responsive images (in pixels)
 */
const BREAKPOINTS = {
    mobile: 640, // 640px
    tablet: 768, // 768px
    desktop: 1024, // 1024px
    retina: 1536, // 1536px
};
/**
 * Default image sizes for different device types
 */
const DEFAULT_SIZES = {
    mobile: 400, // 400px wide on mobile
    tablet: 600, // 600px wide on tablet
    desktop: 800, // 800px wide on desktop
    retina: 1200, // 1200px wide on retina
};
/**
 * Generates a srcSet string for responsive images
 * @param src - Base image URL or path
 * @param options - Configuration options
 * @returns Object containing srcSet and sizes strings
 */
export function generateResponsiveImage(src, options = {}) {
    const { widths = Object.values(DEFAULT_SIZES), formats = [...DEFAULT_FORMATS], quality = DEFAULT_QUALITY, aspectRatio, sizes = '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw', baseUrl = '', } = options;
    // Sort widths in ascending order
    const sortedWidths = [...new Set(widths)].sort((a, b) => a - b);
    const maxWidth = Math.max(...sortedWidths);
    // Generate srcSet for each format
    const srcSets = {};
    for (const format of formats) {
        if (format === 'original') {
            // For original format, use the original image with width descriptors
            const srcSet = sortedWidths
                .map((width) => `${getOptimizedImageUrl(src, { format: 'original', width, quality, baseUrl })} ${width}w`)
                .join(', ');
            if (srcSet) {
                srcSets.original = srcSet;
            }
        }
        else {
            // For WebP/AVIF, generate optimized versions
            const srcSet = sortedWidths
                .map((width) => `${getOptimizedImageUrl(src, { format, width, quality, baseUrl })} ${width}w`)
                .join(', ');
            if (srcSet) {
                srcSets[format] = srcSet;
            }
        }
    }
    // Generate the picture element sources
    const sources = Object.entries(srcSets)
        .filter(([format]) => format !== 'original')
        .map(([format, srcSet]) => {
        const type = `image/${format}`;
        return `  <source
    type="${type}"
    srcSet="${srcSet}"
    sizes="${sizes}"
  />`;
    })
        .join('\n');
    // The fallback image (original format)
    const fallbackSrc = getOptimizedImageUrl(src, {
        format: 'original',
        width: maxWidth,
        quality,
        baseUrl,
    });
    // Generate the picture element
    const pictureElement = `<picture>
${sources}
  <img
    src="${fallbackSrc}"
    ${aspectRatio ? `style="aspect-ratio: ${aspectRatio}"` : ''}
    loading="lazy"
    decoding="async"
    alt=""
  />
</picture>`;
    return {
        srcSet: srcSets.original || '',
        srcSetWebp: srcSets.webp || '',
        srcSetAvif: srcSets.avif || '',
        sizes,
        fallbackSrc,
        pictureElement,
        width: maxWidth,
        height: aspectRatio ? Math.round(maxWidth / aspectRatio) : undefined,
    };
}
/**
 * Generates an optimized image URL with the specified parameters
 * @param src - Original image URL or path
 * @param options - Optimization options
 * @returns Optimized image URL
 */
export function getOptimizedImageUrl(src, options) {
    const { format, width, height, quality = DEFAULT_QUALITY, baseUrl = '' } = options;
    // If it's already a full URL, return as is
    if (src.startsWith('http') || src.startsWith('//') || src.startsWith('data:')) {
        return src;
    }
    // Remove leading slash if present
    const cleanSrc = src.startsWith('/') ? src.slice(1) : src;
    // In a real implementation, this would use an image optimization service
    // like Cloudinary, Imgix, or a custom image processing API
    const params = new URLSearchParams();
    if (width)
        params.set('w', width.toString());
    if (height)
        params.set('h', height.toString());
    if (quality)
        params.set('q', quality.toString());
    // Add format if not original
    if (format !== 'original') {
        params.set('fm', format);
    }
    // Add auto-format and quality settings
    params.set('auto', 'format,compress');
    // Construct the URL
    const queryString = params.toString();
    const url = baseUrl
        ? `${baseUrl.replace(/\/+$/, '')}/${cleanSrc}${queryString ? `?${queryString}` : ''}`
        : `/${cleanSrc}${queryString ? `?${queryString}` : ''}`;
    return url;
}
/**
 * Preloads an image to improve perceived performance
 * @param src - Image URL
 * @returns Promise that resolves when the image is loaded
 */
export function preloadImage(src) {
    return new Promise((resolve, reject) => {
        if (typeof window === 'undefined') {
            resolve(false);
            return;
        }
        const img = new Image();
        img.src = src;
        if (img.complete) {
            resolve(true);
        }
        else {
            img.onload = () => resolve(true);
            img.onerror = (error) => {
                console.error(`Failed to preload image: ${src}`, error);
                resolve(false);
            };
        }
    });
}
/**
 * Generates a blur placeholder for an image
 * @param width - Image width
 * @param height - Image height
 * @returns Base64 encoded SVG placeholder
 */
export function generateBlurPlaceholder(width, height) {
    // Create a small, blurred version of the image as a placeholder
    const svg = `
    <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f3f4f6" />
      <rect width="100%" height="100%" fill="url(#gradient)" />
      <defs>
        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stop-color="#e5e7eb" />
          <stop offset="50%" stop-color="#d1d5db" />
          <stop offset="100%" stop-color="#9ca3af" />
        </linearGradient>
      </defs>
    </svg>
  `;
    return `data:image/svg+xml;base64,${btoa(svg)}`;
}
/**
 * Optimized Image component with lazy loading and automatic format selection
 */
export const OptimizedImage = ({ src, alt, width, height, sizes, className = '', style, loading = 'lazy', priority = false, quality = DEFAULT_QUALITY, placeholder = 'empty', blurDataURL, onLoad, onError, }) => {
    const [isLoading, setIsLoading] = React.useState(true);
    const [hasError, setHasError] = React.useState(false);
    const [imgSrc, setImgSrc] = React.useState(blurDataURL || src);
    const [placeholderSrc, setPlaceholderSrc] = React.useState(placeholder === 'blur' && !blurDataURL
        ? generateBlurPlaceholder(width || 100, height || 100)
        : blurDataURL);
    // Generate responsive image sources
    const responsiveImage = React.useMemo(() => {
        return generateResponsiveImage(src, {
            widths: width ? [Math.floor(width / 2), width, width * 1.5, width * 2] : undefined,
            quality,
            aspectRatio: width && height ? width / height : undefined,
            sizes,
        });
    }, [src, width, height, quality, sizes]);
    // Handle image load
    const handleLoad = React.useCallback((e) => {
        setIsLoading(false);
        setHasError(false);
        setImgSrc(src);
        if (onLoad)
            onLoad();
    }, [src, onLoad]);
    // Handle image error
    const handleError = React.useCallback((e) => {
        console.error(`Failed to load image: ${src}`);
        setIsLoading(false);
        setHasError(true);
        if (onError)
            onError();
    }, [src, onError]);
    // Preload image if priority is true
    React.useEffect(() => {
        if (priority) {
            preloadImage(src).catch(console.error);
        }
    }, [src, priority]);
    // If we have WebP and AVIF support, use the picture element
    const supportsModernFormats = typeof window !== 'undefined'
        ? document.createElement('picture').toString() === '[object HTMLPictureElement]'
        : true;
    if (supportsModernFormats && (responsiveImage.srcSetWebp || responsiveImage.srcSetAvif)) {
        return { responsiveImage, : .srcSetAvif && type, "image/avif": srcSet = { responsiveImage, : .srcSetAvif },
            sizes = { sizes }
                /  >
         };
    }
    {
        responsiveImage.srcSetWebp && type;
        "image/webp";
        srcSet = { responsiveImage, : .srcSetWebp };
        sizes = { sizes }
            /  >
        ;
    }
};
src;
{
    imgSrc;
}
alt = { alt };
width = { width };
height = { height };
className = { className };
style = { style };
loading = { loading };
decoding = "async";
onLoad = { handleLoad };
onError = { handleError }
    /  >
    /picture>;
;
// Fallback to regular img element
return src = { imgSrc };
alt = { alt };
width = { width };
height = { height };
className = { className };
style = { style };
loading = { loading };
decoding = "async";
onLoad = { handleLoad };
onError = { handleError };
srcSet = { responsiveImage, : .srcSet };
sizes = { sizes }
    /  >
    { isLoading } && placeholderSrc && src;
{
    placeholderSrc;
}
alt = "";
aria - hidden;
"true";
style = {};
{
    position: 'absolute',
        top;
    0,
        left;
    0,
        width;
    '100%',
        height;
    '100%',
        filter;
    'blur(10px)',
        transform;
    'scale(1.05)',
        opacity;
    isLoading ? 1 : 0,
        transition;
    'opacity 0.3s ease-in-out',
        zIndex;
    1,
    ;
}
/>;
/>;
;
;
