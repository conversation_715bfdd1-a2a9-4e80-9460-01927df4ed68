import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
export function LoginPage() {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const { login } = useAuth();
    const navigate = useNavigate();
    const location = useLocation();
    const { t } = useTranslation();
    const from = location.state?.from?.pathname || '/admin';
    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        if (!email || !password) {
            setError(t('auth.fillAllFields', 'Please fill in all fields'));
            return;
        }
        try {
            setIsLoading(true);
            await login(email, password);
            navigate(from, { replace: true });
        }
        catch (err) {
            setError(err instanceof Error
                ? err.message
                : t('auth.loginFailed', 'Login failed. Please try again.'));
        }
        finally {
            setIsLoading(false);
        }
    };
    return (_jsxs("div", { className: "flex min-h-screen flex-col justify-center bg-gray-50 py-12 sm:px-6 lg:px-8", children: [_jsxs("div", { className: "sm:mx-auto sm:w-full sm:max-w-md", children: [_jsx("h2", { className: "mt-6 text-center text-3xl font-extrabold text-gray-900", children: t('auth.signInToAccount', 'Sign in to your account') }), _jsxs("p", { className: "mt-2 text-center text-sm text-gray-600", children: [t('auth.or', 'Or '), _jsx(Link, { to: "/", className: "font-medium text-blue-600 hover:text-blue-500", children: t('auth.returnHome', 'return to the homepage') })] })] }), _jsx("div", { className: "mt-8 sm:mx-auto sm:w-full sm:max-w-md", children: _jsxs("div", { className: "bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10", children: [error && (_jsx("div", { className: "mb-4 rounded-md bg-red-50 p-4", children: _jsxs("div", { className: "flex", children: [_jsx("div", { className: "flex-shrink-0", children: _jsx("svg", { className: "h-5 w-5 text-red-400", fill: "currentColor", viewBox: "0 0 20 20", children: _jsx("path", { fillRule: "evenodd", d: "M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z", clipRule: "evenodd" }) }) }), _jsx("div", { className: "ml-3", children: _jsx("h3", { className: "text-sm font-medium text-red-800", children: error }) })] }) })), _jsxs("form", { className: "space-y-6", onSubmit: handleSubmit, children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "email", children: t('auth.emailAddress', 'Email address') }), _jsx("div", { className: "mt-1", children: _jsx(Input, { id: "email", name: "email", type: "email", autoComplete: "email", required: true, value: email, onChange: (e) => setEmail(e.target.value), className: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" }) })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "password", children: t('auth.password', 'Password') }), _jsx("div", { className: "mt-1", children: _jsx(Input, { id: "password", name: "password", type: "password", autoComplete: "current-password", required: true, value: password, onChange: (e) => setPassword(e.target.value), className: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" }) })] }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex items-center", children: [_jsx("input", { id: "remember-me", name: "remember-me", type: "checkbox", className: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" }), _jsx("label", { htmlFor: "remember-me", className: "ml-2 block text-sm text-gray-900", children: t('auth.rememberMe', 'Remember me') })] }), _jsx("div", { className: "text-sm", children: _jsx("a", { href: "/forgot-password", className: "font-medium text-blue-600 hover:text-blue-500", children: t('auth.forgotPassword', 'Forgot your password?') }) })] }), _jsx("div", { children: _jsx(Button, { type: "submit", disabled: isLoading, className: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500", children: isLoading ? (_jsxs(_Fragment, { children: [_jsx(Loader2, { className: "animate-spin -ml-1 mr-3 h-5 w-5 text-white" }), t('auth.signingIn', 'Signing in...')] })) : (t('auth.signIn', 'Sign in')) }) })] })] }) })] }));
}
export default LoginPage;
