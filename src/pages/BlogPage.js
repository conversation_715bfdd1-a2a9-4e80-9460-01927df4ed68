import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Search, Loader2 } from 'lucide-react';
import { getBlogPosts } from '@/api/blog';
import { BlogPostList } from '@/components/blog/BlogPostList';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
export const BlogPage = () => {
    const { t } = useTranslation();
    const [posts, setPosts] = useState([]);
    const [filteredPosts, setFilteredPosts] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [category, setCategory] = useState('all');
    const [categories, setCategories] = useState([]);
    // Fetch blog posts
    useEffect(() => {
        const fetchPosts = async () => {
            try {
                setIsLoading(true);
                const data = await getBlogPosts();
                // Filter only published posts for the public view
                const publishedPosts = data.filter(post => post.status === 'published');
                setPosts(publishedPosts);
                setFilteredPosts(publishedPosts);
                // Extract unique categories from posts
                const allCategories = Array.from(new Set(publishedPosts.flatMap(post => post.tags || [])));
                setCategories(allCategories);
            }
            catch (error) {
                console.error('Error fetching blog posts:', error);
            }
            finally {
                setIsLoading(false);
            }
        };
        fetchPosts();
    }, []);
    // Filter posts based on search term and category
    useEffect(() => {
        let result = [...posts];
        // Apply search filter
        if (searchTerm.trim()) {
            const lowercasedFilter = searchTerm.toLowerCase();
            result = result.filter((post) => post.title.toLowerCase().includes(lowercasedFilter) ||
                post.excerpt.toLowerCase().includes(lowercasedFilter) ||
                post.tags?.some((tag) => tag.toLowerCase().includes(lowercasedFilter)) ||
                post.author.name.toLowerCase().includes(lowercasedFilter));
        }
        // Apply category filter
        if (category && category !== 'all') {
            result = result.filter(post => post.tags?.includes(category));
        }
        setFilteredPosts(result);
    }, [searchTerm, category, posts]);
    return (_jsxs("div", { className: "container py-12", children: [_jsxs("div", { className: "text-center mb-12", children: [_jsx("h1", { className: "text-4xl font-bold tracking-tight mb-4", children: t('blog.title', 'Sailing Serai Blog') }), _jsx("p", { className: "text-xl text-muted-foreground max-w-2xl mx-auto", children: t('blog.subtitle', 'Discover stories, tips, and insights from our sailing adventures') })] }), _jsxs("div", { className: "flex flex-col md:flex-row gap-4 mb-8", children: [_jsxs("div", { className: "relative flex-1", children: [_jsx(Search, { className: "absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" }), _jsx(Input, { type: "search", placeholder: t('blog.searchPlaceholder', 'Search posts...'), className: "pl-9", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value) })] }), _jsx("div", { className: "w-full md:w-64", children: _jsxs(Select, { value: category, onValueChange: setCategory, children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, { placeholder: t('blog.selectCategory', 'Select category') }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "all", children: t('blog.allCategories', 'All Categories') }), categories.map((cat) => (_jsx(SelectItem, { value: cat, children: cat }, cat)))] })] }) })] }), isLoading ? (_jsxs("div", { className: "flex items-center justify-center h-64", children: [_jsx(Loader2, { className: "h-8 w-8 animate-spin text-primary" }), _jsx("span", { className: "ml-2", children: t('blog.loadingPosts', 'Loading blog posts...') })] })) : filteredPosts.length > 0 ? (_jsx(BlogPostList, { posts: filteredPosts })) : (_jsxs("div", { className: "text-center py-16 bg-muted/50 rounded-lg", children: [_jsx("h3", { className: "text-lg font-medium mb-2", children: searchTerm || category !== 'all'
                            ? t('blog.noMatchingPosts', 'No matching posts found')
                            : t('blog.noPosts', 'No blog posts yet') }), _jsx("p", { className: "text-muted-foreground", children: searchTerm || category !== 'all'
                            ? t('blog.tryDifferentSearch', 'Try adjusting your search or filter to find what you\'re looking for.')
                            : t('blog.checkBackLater', 'Check back later for new posts.') }), (searchTerm || category !== 'all') && (_jsx(Button, { variant: "outline", className: "mt-4", onClick: () => {
                            setSearchTerm('');
                            setCategory('all');
                        }, children: t('blog.clearFilters', 'Clear filters') }))] }))] }));
};
export default BlogPage;
