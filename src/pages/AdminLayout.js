import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Outlet } from 'react-router-dom';
import { Sidebar } from '@/components/admin/Sidebar';
import { Header } from '@/components/admin/Header';
export function AdminLayout() {
    // Mock user data - in a real app, this would come from your auth context
    const mockUser = {
        name: 'Admin User',
        email: '<EMAIL>',
        avatar: 'https://ui-avatars.com/api/?name=Admin+User&background=0D8ABC&color=fff',
    };
    const handleLogout = () => {
        // In a real app, this would call your auth service to log the user out
        console.log('Logging out...');
        // Redirect to login page or home page
        window.location.href = '/login';
    };
    return (_jsxs("div", { className: "flex h-screen bg-gray-50", children: [_jsx(Sidebar, { user: mockUser, onLogout: handleLogout }), _jsxs("div", { className: "flex-1 flex flex-col overflow-hidden", children: [_jsx(<PERSON>er, {}), _jsx("main", { className: "flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6", children: _jsx("div", { className: "mx-auto max-w-7xl", children: _jsx(Outlet, {}) }) })] })] }));
}
export default AdminLayout;
