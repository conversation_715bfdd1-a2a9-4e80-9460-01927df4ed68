import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { TestimonialCard } from '@/components/testimonials/TestimonialCard';
import { TestimonialCarousel } from '@/components/testimonials/TestimonialCarousel';
import { Loader2, Star, StarHalf, ChevronDown, ChevronUp } from 'lucide-react';
const TestimonialsPage = () => {
    const { t } = useTranslation();
    const [testimonials, setTestimonials] = useState([]);
    const [featuredTestimonials, setFeaturedTestimonials] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [showAll, setShowAll] = useState(false);
    const [activeFilter, setActiveFilter] = useState('all');
    // Fetch testimonials on component mount
    useEffect(() => {
        const fetchTestimonials = async () => {
            try {
                setIsLoading(true);
                const data = await getTestimonals();
                // Filter only approved testimonials for the public page
                const approvedTestimonials = data.filter(t => t.status === 'approved');
                setTestimonials(approvedTestimonials);
                // Get featured testimonials
                const featured = approvedTestimonials.filter(t => t.featured);
                setFeaturedTestimonials(featured);
            }
            catch (error) {
                console.error('Error fetching testimonials:', error);
            }
            finally {
                setIsLoading(false);
            }
        };
        fetchTestimonials();
    }, []);
    // Render star rating
    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        for (let i = 1; i <= 5; i++) {
            if (i <= fullStars) {
                stars.push(_jsx(Star, { className: "h-5 w-5 fill-yellow-400 text-yellow-400" }, i));
            }
            else if (i === fullStars + 1 && hasHalfStar) {
                stars.push(_jsx(StarHalf, { className: "h-5 w-5 fill-yellow-400 text-yellow-400" }, i));
            }
            else {
                stars.push(_jsx(Star, { className: "h-5 w-5 text-gray-200 dark:text-gray-700" }, i));
            }
        }
        return stars;
    };
    // Filter testimonials based on active filter
    const filteredTestimonials = React.useMemo(() => {
        if (activeFilter === 'all')
            return testimonials;
        if (activeFilter === 'featured')
            return testimonials.filter(t => t.featured);
        return testimonials.filter(t => Math.floor(t.rating) === activeFilter);
    }, [testimonials, activeFilter]);
    // Calculate average rating
    const averageRating = React.useMemo(() => {
        if (testimonials.length === 0)
            return 0;
        const sum = testimonials.reduce((acc, t) => acc + t.rating, 0);
        return Math.round((sum / testimonials.length) * 10) / 10;
    }, [testimonials]);
    // Calculate rating distribution
    const ratingDistribution = React.useMemo(() => {
        const distribution = [0, 0, 0, 0, 0]; // 1-5 stars
        testimonials.forEach(t => {
            const index = Math.floor(t.rating) - 1;
            if (index >= 0 && index < 5) {
                distribution[index]++;
            }
        });
        return distribution.map((count, index) => ({
            rating: index + 1,
            count,
            percentage: testimonials.length > 0 ? Math.round((count / testimonials.length) * 100) : 0,
        }));
    }, [testimonials]);
    if (isLoading) {
        return (_jsxs("div", { className: "container py-12 flex items-center justify-center", children: [_jsx(Loader2, { className: "h-8 w-8 animate-spin text-primary" }), _jsx("span", { className: "ml-2", children: t('testimonials.loading', 'Loading testimonials...') })] }));
    }
    return (_jsxs("div", { className: "bg-white dark:bg-gray-900", children: [_jsx("section", { className: "bg-gradient-to-b from-blue-50 to-white dark:from-gray-800 dark:to-gray-900 py-16 md:py-24", children: _jsx("div", { className: "container px-4 mx-auto", children: _jsxs("div", { className: "max-w-3xl mx-auto text-center", children: [_jsx("h1", { className: "text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4", children: t('testimonials.title', 'What Our Guests Say') }), _jsx("p", { className: "text-xl text-gray-600 dark:text-gray-300 mb-8", children: t('testimonials.subtitle', 'Discover the experiences of our valued guests and share your own story.') }), _jsxs("div", { className: "bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 inline-flex flex-col items-center", children: [_jsx("div", { className: "text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2", children: averageRating.toFixed(1) }), _jsx("div", { className: "flex mb-2", children: renderStars(averageRating) }), _jsx("p", { className: "text-sm text-gray-500 dark:text-gray-400", children: t('testimonials.averageRating', 'Based on {count} reviews', { count: testimonials.length }) })] })] }) }) }), _jsx("section", { className: "py-12 bg-gray-50 dark:bg-gray-800", children: _jsxs("div", { className: "container px-4 mx-auto", children: [_jsx("h2", { className: "text-2xl font-bold text-center text-gray-900 dark:text-white mb-8", children: t('testimonials.ratingBreakdown', 'Rating Breakdown') }), _jsx("div", { className: "max-w-2xl mx-auto space-y-3", children: [5, 4, 3, 2, 1].map((rating) => {
                                const dist = ratingDistribution.find(d => d.rating === rating) || { percentage: 0, count: 0 };
                                return (_jsxs("div", { className: "flex items-center", children: [_jsxs("div", { className: "w-10 text-sm font-medium text-gray-900 dark:text-white", children: [rating, " ", t('testimonials.stars', 'stars')] }), _jsx("div", { className: "flex-1 mx-2 h-4 bg-gray-200 rounded-full dark:bg-gray-700", children: _jsx("div", { className: "h-4 bg-yellow-400 rounded-full", style: { width: `${dist.percentage}%` } }) }), _jsx("div", { className: "w-10 text-sm text-right text-gray-600 dark:text-gray-300", children: dist.count })] }, rating));
                            }) })] }) }), featuredTestimonials.length > 0 && (_jsx("section", { className: "py-16 bg-white dark:bg-gray-900", children: _jsxs("div", { className: "container px-4 mx-auto", children: [_jsxs("div", { className: "text-center mb-12", children: [_jsx("h2", { className: "text-3xl font-bold text-gray-900 dark:text-white mb-2", children: t('testimonials.featuredTestimonials', 'Featured Testimonials') }), _jsx("div", { className: "w-20 h-1 bg-blue-600 mx-auto" })] }), _jsx("div", { className: "max-w-5xl mx-auto", children: _jsx(TestimonialCarousel, { testimonials: featuredTestimonials, autoPlay: true, interval: 8000, showControls: true, showDots: true, className: "px-4 md:px-8" }) })] }) })), _jsx("section", { className: "py-16 bg-gray-50 dark:bg-gray-800", children: _jsxs("div", { className: "container px-4 mx-auto", children: [_jsxs("div", { className: "flex flex-col md:flex-row md:items-center md:justify-between mb-8", children: [_jsx("h2", { className: "text-3xl font-bold text-gray-900 dark:text-white mb-4 md:mb-0", children: t('testimonials.allTestimonials', 'All Testimonials') }), _jsxs("div", { className: "flex flex-wrap gap-2", children: [_jsxs(Button, { variant: activeFilter === 'all' ? 'default' : 'outline', size: "sm", onClick: () => setActiveFilter('all'), children: [t('all', 'All'), " (", testimonials.length, ")"] }), _jsxs(Button, { variant: activeFilter === 'featured' ? 'default' : 'outline', size: "sm", onClick: () => setActiveFilter('featured'), children: [t('testimonials.featured', 'Featured'), " (", featuredTestimonials.length, ")"] }), [5, 4, 3, 2, 1].map((rating) => (_jsxs(Button, { variant: activeFilter === rating ? 'default' : 'outline', size: "sm", className: "flex items-center", onClick: () => setActiveFilter(rating), children: [rating, " ", _jsx(Star, { className: "h-4 w-4 ml-1 fill-current" })] }, rating)))] })] }), filteredTestimonials.length === 0 ? (_jsx("div", { className: "text-center py-12", children: _jsx("p", { className: "text-gray-600 dark:text-gray-300", children: t('testimonials.noTestimonialsFound', 'No testimonials found matching your criteria.') }) })) : (_jsxs(_Fragment, { children: [_jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8", children: (showAll ? filteredTestimonials : filteredTestimonials.slice(0, 6)).map((testimonial) => (_jsx(TestimonialCard, { testimonial: testimonial, className: "h-full" }, testimonial.id))) }), filteredTestimonials.length > 6 && (_jsx("div", { className: "text-center mt-8", children: _jsx(Button, { variant: "outline", onClick: () => setShowAll(!showAll), className: "flex items-center mx-auto", children: showAll ? (_jsxs(_Fragment, { children: [t('testimonials.showLess', 'Show Less'), _jsx(ChevronUp, { className: "ml-2 h-4 w-4" })] })) : (_jsxs(_Fragment, { children: [t('testimonials.showMore', 'Show More'), " (", filteredTestimonials.length - 6, ")", _jsx(ChevronDown, { className: "ml-2 h-4 w-4" })] })) }) }))] }))] }) }), _jsx("section", { className: "bg-blue-600 text-white py-16", children: _jsxs("div", { className: "container px-4 mx-auto text-center", children: [_jsx("h2", { className: "text-3xl font-bold mb-4", children: t('testimonials.shareYourExperience', 'Share Your Experience') }), _jsx("p", { className: "text-xl text-blue-100 mb-8 max-w-2xl mx-auto", children: t('testimonials.ctaDescription', 'We value your feedback! Share your experience with us and help others discover the magic of our services.') }), _jsxs("div", { className: "flex flex-col sm:flex-row justify-center gap-4", children: [_jsx(Button, { variant: "secondary", size: "lg", onClick: () => {
                                        // Scroll to the form section
                                        document.getElementById('testimonial-form')?.scrollIntoView({ behavior: 'smooth' });
                                    }, children: t('testimonials.writeReview', 'Write a Review') }), _jsx(Button, { variant: "outline", size: "lg", className: "bg-transparent border-white text-white hover:bg-white/10", onClick: () => {
                                        // In a real app, this would link to a contact page or open a contact form
                                        window.location.href = '/contact';
                                    }, children: t('contactUs', 'Contact Us') })] })] }) })] }));
};
export default TestimonialsPage;
