import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { TestimonialCard } from '@/components/testimonials/TestimonialCard';
import { TestimonialCarousel } from '@/components/testimonials/TestimonialCarousel';
import { getTestimonials } from '@/api/testimonials';
import { Testimonial } from '@/types/testimonial';
import { Loader2, Star, StarHalf, ChevronDown, ChevronUp } from 'lucide-react';
import { cn } from '@/lib/utils';

const TestimonialsPage: React.FC = () => {
  const { t } = useTranslation();
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [featuredTestimonials, setFeaturedTestimonials] = useState<Testimonial[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAll, setShowAll] = useState(false);
  const [activeFilter, setActiveFilter] = useState<'all' | 'featured' | number>('all');

  // Fetch testimonials on component mount
  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        setIsLoading(true);
        const data = await getTestimonals();
        
        // Filter only approved testimonials for the public page
        const approvedTestimonials = data.filter(t => t.status === 'approved');
        
        setTestimonials(approvedTestimonials);
        
        // Get featured testimonials
        const featured = approvedTestimonials.filter(t => t.featured);
        setFeaturedTestimonials(featured);
        
      } catch (error) {
        console.error('Error fetching testimonials:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  // Render star rating
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 1; i <= 5; i++) {
      if (i <= fullStars) {
        stars.push(<Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />);
      } else if (i === fullStars + 1 && hasHalfStar) {
        stars.push(<StarHalf key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />);
      } else {
        stars.push(<Star key={i} className="h-5 w-5 text-gray-200 dark:text-gray-700" />);
      }
    }

    return stars;
  };

  // Filter testimonials based on active filter
  const filteredTestimonials = React.useMemo(() => {
    if (activeFilter === 'all') return testimonials;
    if (activeFilter === 'featured') return testimonials.filter(t => t.featured);
    return testimonials.filter(t => Math.floor(t.rating) === activeFilter);
  }, [testimonials, activeFilter]);

  // Calculate average rating
  const averageRating = React.useMemo(() => {
    if (testimonials.length === 0) return 0;
    const sum = testimonials.reduce((acc, t) => acc + t.rating, 0);
    return Math.round((sum / testimonials.length) * 10) / 10;
  }, [testimonials]);

  // Calculate rating distribution
  const ratingDistribution = React.useMemo(() => {
    const distribution = [0, 0, 0, 0, 0]; // 1-5 stars
    
    testimonials.forEach(t => {
      const index = Math.floor(t.rating) - 1;
      if (index >= 0 && index < 5) {
        distribution[index]++;
      }
    });
    
    return distribution.map((count, index) => ({
      rating: index + 1,
      count,
      percentage: testimonials.length > 0 ? Math.round((count / testimonials.length) * 100) : 0,
    }));
  }, [testimonials]);

  if (isLoading) {
    return (
      <div className="container py-12 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">
          {t('testimonials.loading', 'Loading testimonials...')}
        </span>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-blue-50 to-white dark:from-gray-800 dark:to-gray-900 py-16 md:py-24">
        <div className="container px-4 mx-auto">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              {t('testimonials.title', 'What Our Guests Say')}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
              {t('testimonials.subtitle', 'Discover the experiences of our valued guests and share your own story.')}
            </p>
            
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 inline-flex flex-col items-center">
              <div className="text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                {averageRating.toFixed(1)}
              </div>
              <div className="flex mb-2">
                {renderStars(averageRating)}
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('testimonials.averageRating', 'Based on {count} reviews', { count: testimonials.length })}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Rating Distribution */}
      <section className="py-12 bg-gray-50 dark:bg-gray-800">
        <div className="container px-4 mx-auto">
          <h2 className="text-2xl font-bold text-center text-gray-900 dark:text-white mb-8">
            {t('testimonials.ratingBreakdown', 'Rating Breakdown')}
          </h2>
          
          <div className="max-w-2xl mx-auto space-y-3">
            {[5, 4, 3, 2, 1].map((rating) => {
              const dist = ratingDistribution.find(d => d.rating === rating) || { percentage: 0, count: 0 };
              return (
                <div key={rating} className="flex items-center">
                  <div className="w-10 text-sm font-medium text-gray-900 dark:text-white">
                    {rating} {t('testimonials.stars', 'stars')}
                  </div>
                  <div className="flex-1 mx-2 h-4 bg-gray-200 rounded-full dark:bg-gray-700">
                    <div 
                      className="h-4 bg-yellow-400 rounded-full" 
                      style={{ width: `${dist.percentage}%` }}
                    />
                  </div>
                  <div className="w-10 text-sm text-right text-gray-600 dark:text-gray-300">
                    {dist.count}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Featured Testimonials Carousel */}
      {featuredTestimonials.length > 0 && (
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="container px-4 mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {t('testimonials.featuredTestimonials', 'Featured Testimonials')}
              </h2>
              <div className="w-20 h-1 bg-blue-600 mx-auto"></div>
            </div>
            
            <div className="max-w-5xl mx-auto">
              <TestimonialCarousel 
                testimonials={featuredTestimonials}
                autoPlay={true}
                interval={8000}
                showControls={true}
                showDots={true}
                className="px-4 md:px-8"
              />
            </div>
          </div>
        </section>
      )}

      {/* All Testimonials */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container px-4 mx-auto">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4 md:mb-0">
              {t('testimonials.allTestimonials', 'All Testimonials')}
            </h2>
            
            <div className="flex flex-wrap gap-2">
              <Button
                variant={activeFilter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveFilter('all')}
              >
                {t('all', 'All')} ({testimonials.length})
              </Button>
              <Button
                variant={activeFilter === 'featured' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveFilter('featured')}
              >
                {t('testimonials.featured', 'Featured')} ({featuredTestimonials.length})
              </Button>
              {[5, 4, 3, 2, 1].map((rating) => (
                <Button
                  key={rating}
                  variant={activeFilter === rating ? 'default' : 'outline'}
                  size="sm"
                  className="flex items-center"
                  onClick={() => setActiveFilter(rating)}
                >
                  {rating} <Star className="h-4 w-4 ml-1 fill-current" />
                </Button>
              ))}
            </div>
          </div>
          
          {filteredTestimonials.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-600 dark:text-gray-300">
                {t('testimonials.noTestimonialsFound', 'No testimonials found matching your criteria.')}
              </p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
                {(showAll ? filteredTestimonials : filteredTestimonials.slice(0, 6)).map((testimonial) => (
                  <TestimonialCard 
                    key={testimonial.id} 
                    testimonial={testimonial} 
                    className="h-full"
                  />
                ))}
              </div>
              
              {filteredTestimonials.length > 6 && (
                <div className="text-center mt-8">
                  <Button
                    variant="outline"
                    onClick={() => setShowAll(!showAll)}
                    className="flex items-center mx-auto"
                  >
                    {showAll ? (
                      <>
                        {t('testimonials.showLess', 'Show Less')}
                        <ChevronUp className="ml-2 h-4 w-4" />
                      </>
                    ) : (
                      <>
                        {t('testimonials.showMore', 'Show More')} ({filteredTestimonials.length - 6})
                        <ChevronDown className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="bg-blue-600 text-white py-16">
        <div className="container px-4 mx-auto text-center">
          <h2 className="text-3xl font-bold mb-4">
            {t('testimonials.shareYourExperience', 'Share Your Experience')}
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            {t('testimonials.ctaDescription', 'We value your feedback! Share your experience with us and help others discover the magic of our services.')}
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button 
              variant="secondary" 
              size="lg"
              onClick={() => {
                // Scroll to the form section
                document.getElementById('testimonial-form')?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              {t('testimonials.writeReview', 'Write a Review')}
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              className="bg-transparent border-white text-white hover:bg-white/10"
              onClick={() => {
                // In a real app, this would link to a contact page or open a contact form
                window.location.href = '/contact';
              }}
            >
              {t('contactUs', 'Contact Us')}
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TestimonialsPage;
