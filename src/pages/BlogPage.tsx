import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Search, Loader2 } from 'lucide-react';
import { BlogPost } from '@/types/blog';
import { getBlogPosts } from '@/api/blog';
import { BlogPostList } from '@/components/blog/BlogPostList';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export const BlogPage: React.FC = () => {
  const { t } = useTranslation();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [category, setCategory] = useState<string>('all');
  const [categories, setCategories] = useState<string[]>([]);

  // Fetch blog posts
  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setIsLoading(true);
        const data = await getBlogPosts();
        // Filter only published posts for the public view
        const publishedPosts = data.filter(post => post.status === 'published');
        setPosts(publishedPosts);
        setFilteredPosts(publishedPosts);
        
        // Extract unique categories from posts
        const allCategories = Array.from(
          new Set(
            publishedPosts.flatMap(post => post.tags || [])
          )
        );
        setCategories(allCategories);
      } catch (error) {
        console.error('Error fetching blog posts:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPosts();
  }, []);

  // Filter posts based on search term and category
  useEffect(() => {
    let result = [...posts];
    
    // Apply search filter
    if (searchTerm.trim()) {
      const lowercasedFilter = searchTerm.toLowerCase();
      result = result.filter(
        (post) =>
          post.title.toLowerCase().includes(lowercasedFilter) ||
          post.excerpt.toLowerCase().includes(lowercasedFilter) ||
          post.tags?.some((tag) => tag.toLowerCase().includes(lowercasedFilter)) ||
          post.author.name.toLowerCase().includes(lowercasedFilter)
      );
    }
    
    // Apply category filter
    if (category && category !== 'all') {
      result = result.filter(post => 
        post.tags?.includes(category)
      );
    }
    
    setFilteredPosts(result);
  }, [searchTerm, category, posts]);

  return (
    <div className="container py-12">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold tracking-tight mb-4">
          {t('blog.title', 'Sailing Serai Blog')}
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          {t('blog.subtitle', 'Discover stories, tips, and insights from our sailing adventures')}
        </p>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row gap-4 mb-8">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="search"
            placeholder={t('blog.searchPlaceholder', 'Search posts...')}
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="w-full md:w-64">
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger>
              <SelectValue placeholder={t('blog.selectCategory', 'Select category')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {t('blog.allCategories', 'All Categories')}
              </SelectItem>
              {categories.map((cat) => (
                <SelectItem key={cat} value={cat}>
                  {cat}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">
            {t('blog.loadingPosts', 'Loading blog posts...')}
          </span>
        </div>
      ) : filteredPosts.length > 0 ? (
        <BlogPostList posts={filteredPosts} />
      ) : (
        <div className="text-center py-16 bg-muted/50 rounded-lg">
          <h3 className="text-lg font-medium mb-2">
            {searchTerm || category !== 'all'
              ? t('blog.noMatchingPosts', 'No matching posts found')
              : t('blog.noPosts', 'No blog posts yet')}
          </h3>
          <p className="text-muted-foreground">
            {searchTerm || category !== 'all'
              ? t('blog.tryDifferentSearch', 'Try adjusting your search or filter to find what you\'re looking for.')
              : t('blog.checkBackLater', 'Check back later for new posts.')}
          </p>
          {(searchTerm || category !== 'all') && (
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => {
                setSearchTerm('');
                setCategory('all');
              }}
            >
              {t('blog.clearFilters', 'Clear filters')}
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default BlogPage;
