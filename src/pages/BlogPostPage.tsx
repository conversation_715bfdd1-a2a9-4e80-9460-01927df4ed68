import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, Loader2, Calendar, Clock, Tag, User, Share2, MessageSquare } from 'lucide-react';
import { BlogPost } from '@/types/blog';
import { getBlogPostBySlug, getBlogPosts } from '@/api/blog';
import { Button } from '@/components/ui/button';
import { BlogPostList } from '@/components/blog/BlogPostList';
import { format } from 'date-fns';
import { toast } from '@/components/ui/use-toast';

export const BlogPostPage: React.FC = () => {
  const { t } = useTranslation();
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  
  const [post, setPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch blog post
  useEffect(() => {
    const fetchPost = async () => {
      if (!slug) return;
      
      try {
        setIsLoading(true);
        setError(null);
        
        const data = await getBlogPostBySlug(slug);
        
        // Only show published posts to the public
        if (data.status !== 'published') {
          throw new Error('Post not found');
        }
        
        setPost(data);
        
        // Fetch related posts
        const allPosts = await getBlogPosts();
        const related = allPosts
          .filter(p => 
            p.id !== data.id && 
            p.status === 'published' && 
            p.tags?.some(tag => data.tags?.includes(tag))
          )
          .slice(0, 3);
        
        setRelatedPosts(related);
      } catch (err) {
        console.error('Error fetching blog post:', err);
        setError(t('blog.postNotFound', 'Blog post not found'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchPost();
  }, [slug, t]);

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: post?.title,
          text: post?.excerpt,
          url: window.location.href,
        });
      } else {
        await navigator.clipboard.writeText(window.location.href);
        toast({
          title: t('blog.linkCopied', 'Link copied to clipboard'),
        });
      }
    } catch (err) {
      console.error('Error sharing:', err);
    }
  };

  if (isLoading) {
    return (
      <div className="container py-12">
        <Button
          variant="ghost"
          onClick={() => navigate(-1)}
          className="mb-8"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('common.back', 'Back to Blog')}
        </Button>
        
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">
            {t('blog.loadingPost', 'Loading blog post...')}
          </span>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="container py-12 text-center">
        <Button
          variant="ghost"
          onClick={() => navigate('/blog')}
          className="mb-8"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('blog.backToBlog', 'Back to Blog')}
        </Button>
        
        <h2 className="text-2xl font-bold mb-4">
          {t('blog.postNotFound', 'Blog post not found')}
        </h2>
        <p className="text-muted-foreground mb-6">
          {t('blog.postNotFoundDescription', 'The requested blog post could not be found.')}
        </p>
        <Button onClick={() => navigate('/blog')}>
          {t('blog.viewAllPosts', 'View all blog posts')}
        </Button>
      </div>
    );
  }

  // Calculate reading time (assuming 200 words per minute)
  const words = post.content.trim().split(/\s+/).length;
  const readingTime = Math.ceil(words / 200);

  return (
    <div className="container py-12">
      <Button
        variant="ghost"
        onClick={() => navigate(-1)}
        className="mb-8"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        {t('common.back', 'Back to Blog')}
      </Button>

      <article className="max-w-3xl mx-auto">
        <header className="mb-8">
          {post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {post.tags.map((tag) => (
                <Button
                  key={tag}
                  variant="outline"
                  size="sm"
                  className="rounded-full"
                  onClick={() => navigate(`/blog?category=${encodeURIComponent(tag)}`)}
                >
                  <Tag className="h-3 w-3 mr-1" />
                  {tag}
                </Button>
              ))}
            </div>
          )}
          
          <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">
            {post.title}
          </h1>
          
          <div className="flex flex-wrap items-center text-sm text-muted-foreground gap-4 mb-6">
            <div className="flex items-center">
              <User className="h-4 w-4 mr-1" />
              <span>{post.author.name}</span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              <time dateTime={post.publishedAt}>
                {format(new Date(post.publishedAt), 'MMMM d, yyyy')}
              </time>
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              <span>
                {readingTime} {t('blog.minRead', 'min read')}
              </span>
            </div>
          </div>

          {post.featuredImage && (
            <div className="rounded-lg overflow-hidden mb-8">
              <img
                src={post.featuredImage}
                alt={post.title}
                className="w-full h-auto max-h-[500px] object-cover"
              />
            </div>
          )}
        </header>

        <div 
          className="prose prose-lg max-w-none dark:prose-invert prose-headings:font-display"
          dangerouslySetInnerHTML={{ __html: post.content }}
        />

        <div className="mt-12 pt-6 border-t flex flex-wrap justify-between items-center gap-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              {t('blog.shareThisPost', 'Share this post:')}
            </span>
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleShare}
            >
              <Share2 className="h-4 w-4 mr-2" />
              {t('common.share', 'Share')}
            </Button>
          </div>
          
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => {
              const commentsSection = document.getElementById('comments');
              if (commentsSection) {
                commentsSection.scrollIntoView({ behavior: 'smooth' });
              }
            }}
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            {t('blog.leaveComment', 'Leave a comment')}
          </Button>
        </div>
      </article>

      {/* Comments Section */}
      <section id="comments" className="max-w-3xl mx-auto mt-16 pt-8 border-t">
        <h2 className="text-2xl font-bold mb-6">
          {t('blog.comments', 'Comments')}
        </h2>
        <div className="bg-muted/50 rounded-lg p-6 text-center">
          <p className="text-muted-foreground">
            {t('blog.commentsComingSoon', 'Comments will be available soon.')}
          </p>
        </div>
      </section>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <section className="mt-16 pt-8 border-t">
          <h2 className="text-2xl font-bold mb-6">
            {t('blog.youMightAlsoLike', 'You might also like')}
          </h2>
          <BlogPostList posts={relatedPosts} />
        </section>
      )}
    </div>
  );
};

export default BlogPostPage;
