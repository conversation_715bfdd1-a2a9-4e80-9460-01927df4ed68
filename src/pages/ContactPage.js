import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Mail, Phone, MapPin, Clock, Send, Instagram, Facebook, Twitter } from 'lucide-react';
import { motion } from 'framer-motion';
import { ContactForm } from '@/components/contact/ContactForm';
import { submitContactForm } from '@/api/contact';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
const ContactPage = () => {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState(null);
    // ReCAPTCHA site key (replace with your actual key)
    const recaptchaSiteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI'; // Default is test key
    // Handle form submission
    const handleSubmit = async (formData) => {
        setIsSubmitting(true);
        setSubmitStatus(null);
        try {
            const result = await submitContactForm({
                ...formData,
                // In a real app, you might want to add additional metadata here
                // like the page URL, user agent, etc.
                _pageUrl: typeof window !== 'undefined' ? window.location.href : '',
                _timestamp: new Date().toISOString(),
            });
            setSubmitStatus({
                success: result.success,
                message: result.message,
            });
            return result.success;
        }
        catch (error) {
            console.error('Error submitting contact form:', error);
            setSubmitStatus({
                success: false,
                message: 'An unexpected error occurred. Please try again later or contact us directly.',
            });
            return false;
        }
        finally {
            setIsSubmitting(false);
        }
    };
    // Animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
                delayChildren: 0.2,
            },
        },
    };
    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                type: 'spring',
                stiffness: 100,
                damping: 10,
            },
        },
    };
    return (_jsxs("div", { className: "min-h-screen bg-background", children: [_jsx("section", { className: "relative bg-gradient-to-b from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-900 py-16 md:py-24", children: _jsx("div", { className: "container mx-auto px-4", children: _jsxs(motion.div, { initial: { opacity: 0, y: 20 }, animate: { opacity: 1, y: 0 }, transition: { duration: 0.6 }, className: "text-center max-w-3xl mx-auto", children: [_jsx("h1", { className: "text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4", children: "Get in Touch" }), _jsx("p", { className: "text-xl text-muted-foreground", children: "Have questions or want to book a sailing adventure? We'd love to hear from you!" })] }) }) }), _jsx("section", { className: "py-12 md:py-20", children: _jsx("div", { className: "container mx-auto px-4", children: _jsxs(motion.div, { variants: containerVariants, initial: "hidden", animate: "visible", className: "grid grid-cols-1 lg:grid-cols-2 gap-12 items-start", children: [_jsx(motion.div, { variants: itemVariants, className: "lg:sticky lg:top-8", children: _jsxs(Card, { className: "shadow-lg overflow-hidden", children: [_jsx(CardHeader, { className: "bg-primary/5 border-b", children: _jsxs(CardTitle, { className: "text-2xl flex items-center gap-2", children: [_jsx(Send, { className: "h-6 w-6" }), "Send us a Message"] }) }), _jsx(CardContent, { className: "p-6", children: _jsx(ContactForm, { recaptchaSiteKey: recaptchaSiteKey, onSubmit: handleSubmit, className: "max-w-2xl mx-auto" }) })] }) }), _jsxs(motion.div, { variants: itemVariants, className: "space-y-8", children: [_jsxs("div", { children: [_jsx("h2", { className: "text-3xl font-bold text-gray-900 dark:text-white mb-6", children: "Contact Information" }), _jsx("p", { className: "text-lg text-muted-foreground mb-8", children: "We're here to help and answer any questions you might have. Reach out to us and we'll respond as soon as possible." })] }), _jsxs("div", { className: "grid gap-6 sm:grid-cols-2", children: [_jsx(Card, { className: "hover:shadow-md transition-shadow", children: _jsx(CardContent, { className: "p-6", children: _jsxs("div", { className: "flex items-start gap-4", children: [_jsx("div", { className: "bg-primary/10 p-3 rounded-full text-primary", children: _jsx(Mail, { className: "h-6 w-6" }) }), _jsxs("div", { children: [_jsx("h3", { className: "font-semibold text-lg mb-1", children: "Email Us" }), _jsx("p", { className: "text-muted-foreground", children: _jsx("a", { href: "mailto:<EMAIL>", className: "hover:text-primary hover:underline", children: "<EMAIL>" }) }), _jsx("p", { className: "text-sm text-muted-foreground mt-2", children: "We'll respond within 24 hours" })] })] }) }) }), _jsx(Card, { className: "hover:shadow-md transition-shadow", children: _jsx(CardContent, { className: "p-6", children: _jsxs("div", { className: "flex items-start gap-4", children: [_jsx("div", { className: "bg-primary/10 p-3 rounded-full text-primary", children: _jsx(Phone, { className: "h-6 w-6" }) }), _jsxs("div", { children: [_jsx("h3", { className: "font-semibold text-lg mb-1", children: "Call Us" }), _jsx("p", { className: "text-muted-foreground", children: _jsx("a", { href: "tel:+6495551234", className: "hover:text-primary hover:underline", children: "+64 9 555 1234" }) }), _jsx("p", { className: "text-sm text-muted-foreground mt-2", children: "Mon-Fri, 9am-5pm NZST" })] })] }) }) }), _jsx(Card, { className: "hover:shadow-md transition-shadow sm:col-span-2", children: _jsx(CardContent, { className: "p-6", children: _jsxs("div", { className: "flex items-start gap-4", children: [_jsx("div", { className: "bg-primary/10 p-3 rounded-full text-primary", children: _jsx(MapPin, { className: "h-6 w-6" }) }), _jsxs("div", { children: [_jsx("h3", { className: "font-semibold text-lg mb-1", children: "Visit Us" }), _jsx("p", { className: "text-muted-foreground", children: "123 Maritime Parade" }), _jsx("p", { className: "text-muted-foreground", children: "Auckland Central, 1010" }), _jsx("p", { className: "text-muted-foreground", children: "New Zealand" }), _jsx(Button, { variant: "outline", size: "sm", className: "mt-3", children: "Get Directions" })] })] }) }) })] }), _jsxs("div", { children: [_jsxs("h3", { className: "font-semibold text-lg mb-4 flex items-center gap-2", children: [_jsx(Clock, { className: "h-5 w-5 text-primary" }), "Business Hours"] }), _jsx("div", { className: "bg-muted/50 rounded-lg p-4 space-y-2", children: [
                                                    { day: 'Monday - Friday', hours: '9:00 AM - 5:00 PM' },
                                                    { day: 'Saturday', hours: '10:00 AM - 4:00 PM' },
                                                    { day: 'Sunday', hours: 'Closed' },
                                                ].map((item, index) => (_jsxs("div", { className: "flex justify-between", children: [_jsx("span", { className: "text-muted-foreground", children: item.day }), _jsx("span", { className: "font-medium", children: item.hours })] }, index))) })] }), _jsxs("div", { children: [_jsx("h3", { className: "font-semibold text-lg mb-4", children: "Follow Us" }), _jsx("div", { className: "flex gap-4", children: [
                                                    { icon: Instagram, url: 'https://instagram.com/sailingserai' },
                                                    { icon: Facebook, url: 'https://facebook.com/sailingserai' },
                                                    { icon: Twitter, url: 'https://twitter.com/sailingserai' },
                                                ].map((social, index) => (_jsx("a", { href: social.url, target: "_blank", rel: "noopener noreferrer", className: "inline-flex items-center justify-center h-10 w-10 rounded-full bg-muted hover:bg-primary/10 text-muted-foreground hover:text-primary transition-colors", "aria-label": social.icon.name, children: _jsx(social.icon, { className: "h-5 w-5" }) }, index))) })] })] })] }) }) }), _jsx("section", { className: "bg-muted/50 py-12 md:py-20", children: _jsx("div", { className: "container mx-auto px-4", children: _jsxs(motion.div, { initial: { opacity: 0, y: 20 }, whileInView: { opacity: 1, y: 0 }, viewport: { once: true }, transition: { duration: 0.6 }, className: "max-w-6xl mx-auto", children: [_jsx("h2", { className: "text-3xl font-bold text-center mb-8", children: "Find Us on the Map" }), _jsx("div", { className: "rounded-xl overflow-hidden shadow-lg h-96 bg-muted", style: {
                                    backgroundImage: 'url(/images/map-placeholder.jpg)',
                                    backgroundSize: 'cover',
                                    backgroundPosition: 'center',
                                }, children: _jsx("div", { className: "w-full h-full flex items-center justify-center bg-black/20 backdrop-blur-sm", children: _jsx(Button, { asChild: true, children: _jsxs("a", { href: "https://www.google.com/maps/search/?api=1&query=Sailing+Serai+Auckland", target: "_blank", rel: "noopener noreferrer", className: "flex items-center gap-2", children: [_jsx(MapPin, { className: "h-5 w-5" }), "Open in Google Maps"] }) }) }) })] }) }) })] }));
};
export default ContactPage;
