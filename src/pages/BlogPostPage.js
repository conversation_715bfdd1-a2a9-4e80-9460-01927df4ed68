import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, Loader2, Calendar, Clock, Tag, User, Share2, MessageSquare } from 'lucide-react';
import { getBlogPostBySlug, getBlogPosts } from '@/api/blog';
import { Button } from '@/components/ui/button';
import { BlogPostList } from '@/components/blog/BlogPostList';
import { format } from 'date-fns';
import { toast } from '@/components/ui/use-toast';
export const BlogPostPage = () => {
    const { t } = useTranslation();
    const { slug } = useParams();
    const navigate = useNavigate();
    const [post, setPost] = useState(null);
    const [relatedPosts, setRelatedPosts] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    // Fetch blog post
    useEffect(() => {
        const fetchPost = async () => {
            if (!slug)
                return;
            try {
                setIsLoading(true);
                setError(null);
                const data = await getBlogPostBySlug(slug);
                // Only show published posts to the public
                if (data.status !== 'published') {
                    throw new Error('Post not found');
                }
                setPost(data);
                // Fetch related posts
                const allPosts = await getBlogPosts();
                const related = allPosts
                    .filter(p => p.id !== data.id &&
                    p.status === 'published' &&
                    p.tags?.some(tag => data.tags?.includes(tag)))
                    .slice(0, 3);
                setRelatedPosts(related);
            }
            catch (err) {
                console.error('Error fetching blog post:', err);
                setError(t('blog.postNotFound', 'Blog post not found'));
            }
            finally {
                setIsLoading(false);
            }
        };
        fetchPost();
    }, [slug, t]);
    const handleShare = async () => {
        try {
            if (navigator.share) {
                await navigator.share({
                    title: post?.title,
                    text: post?.excerpt,
                    url: window.location.href,
                });
            }
            else {
                await navigator.clipboard.writeText(window.location.href);
                toast({
                    title: t('blog.linkCopied', 'Link copied to clipboard'),
                });
            }
        }
        catch (err) {
            console.error('Error sharing:', err);
        }
    };
    if (isLoading) {
        return (_jsxs("div", { className: "container py-12", children: [_jsxs(Button, { variant: "ghost", onClick: () => navigate(-1), className: "mb-8", children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), t('common.back', 'Back to Blog')] }), _jsxs("div", { className: "flex items-center justify-center h-64", children: [_jsx(Loader2, { className: "h-8 w-8 animate-spin text-primary" }), _jsx("span", { className: "ml-2", children: t('blog.loadingPost', 'Loading blog post...') })] })] }));
    }
    if (error || !post) {
        return (_jsxs("div", { className: "container py-12 text-center", children: [_jsxs(Button, { variant: "ghost", onClick: () => navigate('/blog'), className: "mb-8", children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), t('blog.backToBlog', 'Back to Blog')] }), _jsx("h2", { className: "text-2xl font-bold mb-4", children: t('blog.postNotFound', 'Blog post not found') }), _jsx("p", { className: "text-muted-foreground mb-6", children: t('blog.postNotFoundDescription', 'The requested blog post could not be found.') }), _jsx(Button, { onClick: () => navigate('/blog'), children: t('blog.viewAllPosts', 'View all blog posts') })] }));
    }
    // Calculate reading time (assuming 200 words per minute)
    const words = post.content.trim().split(/\s+/).length;
    const readingTime = Math.ceil(words / 200);
    return (_jsxs("div", { className: "container py-12", children: [_jsxs(Button, { variant: "ghost", onClick: () => navigate(-1), className: "mb-8", children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), t('common.back', 'Back to Blog')] }), _jsxs("article", { className: "max-w-3xl mx-auto", children: [_jsxs("header", { className: "mb-8", children: [post.tags && post.tags.length > 0 && (_jsx("div", { className: "flex flex-wrap gap-2 mb-4", children: post.tags.map((tag) => (_jsxs(Button, { variant: "outline", size: "sm", className: "rounded-full", onClick: () => navigate(`/blog?category=${encodeURIComponent(tag)}`), children: [_jsx(Tag, { className: "h-3 w-3 mr-1" }), tag] }, tag))) })), _jsx("h1", { className: "text-3xl md:text-4xl font-bold tracking-tight mb-4", children: post.title }), _jsxs("div", { className: "flex flex-wrap items-center text-sm text-muted-foreground gap-4 mb-6", children: [_jsxs("div", { className: "flex items-center", children: [_jsx(User, { className: "h-4 w-4 mr-1" }), _jsx("span", { children: post.author.name })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Calendar, { className: "h-4 w-4 mr-1" }), _jsx("time", { dateTime: post.publishedAt, children: format(new Date(post.publishedAt), 'MMMM d, yyyy') })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Clock, { className: "h-4 w-4 mr-1" }), _jsxs("span", { children: [readingTime, " ", t('blog.minRead', 'min read')] })] })] }), post.featuredImage && (_jsx("div", { className: "rounded-lg overflow-hidden mb-8", children: _jsx("img", { src: post.featuredImage, alt: post.title, className: "w-full h-auto max-h-[500px] object-cover" }) }))] }), _jsx("div", { className: "prose prose-lg max-w-none dark:prose-invert prose-headings:font-display", dangerouslySetInnerHTML: { __html: post.content } }), _jsxs("div", { className: "mt-12 pt-6 border-t flex flex-wrap justify-between items-center gap-4", children: [_jsxs("div", { className: "flex items-center space-x-2", children: [_jsx("span", { className: "text-sm text-muted-foreground", children: t('blog.shareThisPost', 'Share this post:') }), _jsxs(Button, { variant: "outline", size: "sm", onClick: handleShare, children: [_jsx(Share2, { className: "h-4 w-4 mr-2" }), t('common.share', 'Share')] })] }), _jsxs(Button, { variant: "ghost", size: "sm", onClick: () => {
                                    const commentsSection = document.getElementById('comments');
                                    if (commentsSection) {
                                        commentsSection.scrollIntoView({ behavior: 'smooth' });
                                    }
                                }, children: [_jsx(MessageSquare, { className: "h-4 w-4 mr-2" }), t('blog.leaveComment', 'Leave a comment')] })] })] }), _jsxs("section", { id: "comments", className: "max-w-3xl mx-auto mt-16 pt-8 border-t", children: [_jsx("h2", { className: "text-2xl font-bold mb-6", children: t('blog.comments', 'Comments') }), _jsx("div", { className: "bg-muted/50 rounded-lg p-6 text-center", children: _jsx("p", { className: "text-muted-foreground", children: t('blog.commentsComingSoon', 'Comments will be available soon.') }) })] }), relatedPosts.length > 0 && (_jsxs("section", { className: "mt-16 pt-8 border-t", children: [_jsx("h2", { className: "text-2xl font-bold mb-6", children: t('blog.youMightAlsoLike', 'You might also like') }), _jsx(BlogPostList, { posts: relatedPosts })] }))] }));
};
export default BlogPostPage;
