import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { format, parseISO } from 'date-fns';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { MoreHorizontal, Search, Calendar as CalendarIcon, Filter } from 'lucide-react';
import { getBookings } from '@/lib/api/bookings';
import { formatCurrency } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
// Define status variant mapping with proper type safety
const statusVariant = {
    confirmed: 'bg-green-100 text-green-800',
    pending: 'bg-yellow-100 text-yellow-800',
    cancelled: 'bg-red-100 text-red-800',
    completed: 'bg-blue-100 text-blue-800',
};
export function BookingsPage() {
    const { t } = useTranslation();
    const [searchQuery, setSearchQuery] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [selectedBookings, setSelectedBookings] = useState([]);
    // Define query function with proper type
    const fetchBookings = async () => {
        const result = await getBookings({});
        return Array.isArray(result) ? result : [];
    };
    // Fetch bookings data with proper type
    const { data: bookings = [], isLoading } = useQuery({
        queryKey: ['bookings'],
        queryFn: fetchBookings,
        initialData: []
    });
    // Filter and search bookings with proper type safety
    const filteredBookings = useMemo(() => {
        return bookings.filter((booking) => {
            const searchLower = searchQuery.toLowerCase();
            const matchesSearch = (booking.customer?.name?.toLowerCase().includes(searchLower) ?? false) ||
                (booking.experience?.toLowerCase().includes(searchLower) ?? false) ||
                (booking.id?.toLowerCase().includes(searchLower) ?? false);
            const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;
            return matchesSearch && matchesStatus;
        });
    }, [bookings, searchQuery, statusFilter]);
    // Toggle selection for a single booking
    const toggleBookingSelection = (id) => {
        setSelectedBookings((prev) => prev.includes(id)
            ? prev.filter((bookingId) => bookingId !== id)
            : [...prev, id]);
    };
    // Toggle selection for all bookings on current page
    const toggleSelectAllBookings = () => {
        if (selectedBookings.length === filteredBookings.length) {
            setSelectedBookings([]);
        }
        else {
            setSelectedBookings(filteredBookings.map((booking) => booking.id));
        }
    };
    // Handle bulk actions
    const handleBulkAction = (action) => {
        // In a real app, this would make an API call to update the bookings
        console.log(`Performing ${action} on:`, selectedBookings);
        // Reset selection after action
        setSelectedBookings([]);
    };
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex flex-col justify-between space-y-4 sm:flex-row sm:items-center sm:space-y-0", children: [_jsxs("div", { children: [_jsx("h2", { className: "text-2xl font-bold tracking-tight", children: t('bookings.title', 'Bookings') }), _jsx("p", { className: "text-muted-foreground", children: t('bookings.subtitle', 'Manage and track all your bookings') })] }), _jsx("div", { className: "flex items-center space-x-2", children: _jsxs(Button, { children: [_jsx(CalendarIcon, { className: "mr-2 h-4 w-4" }), t('bookings.newBooking', 'New Booking')] }) })] }), _jsxs("div", { className: "flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0", children: [_jsxs("div", { className: "relative w-full md:max-w-sm", children: [_jsx(Search, { className: "absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" }), _jsx(Input, { placeholder: t('bookings.searchPlaceholder', 'Search bookings...'), className: "pl-9", value: searchQuery, onChange: (e) => setSearchQuery(e.target.value) })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsxs(DropdownMenu, { children: [_jsx(DropdownMenuTrigger, { asChild: true, children: _jsxs(Button, { variant: "outline", size: "sm", children: [_jsx(Filter, { className: "mr-2 h-4 w-4" }), statusFilter === 'all'
                                                    ? t('bookings.filterByStatus', 'Filter by status')
                                                    : t(`booking.status.${statusFilter}`, statusFilter)] }) }), _jsxs(DropdownMenuContent, { align: "end", children: [_jsx(DropdownMenuItem, { onClick: () => setStatusFilter('all'), children: t('common.all', 'All') }), _jsx(DropdownMenuSeparator, {}), Object.entries(statusVariant).map(([status, _]) => (_jsx(DropdownMenuItem, { onClick: () => setStatusFilter(status), children: t(`booking.status.${status}`, status) }, status)))] })] }), selectedBookings.length > 0 && (_jsxs(DropdownMenu, { children: [_jsx(DropdownMenuTrigger, { asChild: true, children: _jsx(Button, { variant: "outline", size: "sm", children: t('bookings.bulkActions', 'Bulk Actions') }) }), _jsxs(DropdownMenuContent, { align: "end", children: [_jsx(DropdownMenuLabel, { children: t('bookings.selectedItems', { count: selectedBookings.length }) }), _jsx(DropdownMenuSeparator, {}), _jsx(DropdownMenuItem, { onClick: () => handleBulkAction('confirm'), children: t('bookings.confirmSelected', 'Confirm Selected') }), _jsx(DropdownMenuItem, { onClick: () => handleBulkAction('cancel'), children: t('bookings.cancelSelected', 'Cancel Selected') }), _jsx(DropdownMenuSeparator, {}), _jsx(DropdownMenuItem, { className: "text-red-600", onClick: () => handleBulkAction('delete'), children: t('bookings.deleteSelected', 'Delete Selected') })] })] }))] })] }), _jsx("div", { className: "rounded-md border", children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { className: "w-[50px]", children: _jsx(Checkbox, { checked: filteredBookings.length > 0 &&
                                                selectedBookings.length === filteredBookings.length, onCheckedChange: toggleSelectAllBookings, "aria-label": "Select all" }) }), _jsx(TableHead, { children: t('bookings.bookingId', 'Booking ID') }), _jsx(TableHead, { children: t('bookings.customer', 'Customer') }), _jsx(TableHead, { children: t('bookings.experience', 'Experience') }), _jsx(TableHead, { children: t('bookings.date', 'Date') }), _jsx(TableHead, { className: "text-right", children: t('bookings.amount', 'Amount') }), _jsx(TableHead, { children: t('bookings.status', 'Status') }), _jsx(TableHead, { className: "text-right", children: _jsx("span", { className: "sr-only", children: t('common.actions', 'Actions') }) })] }) }), _jsx(TableBody, { children: isLoading ? (
                            // Loading skeleton
                            Array.from({ length: 5 }).map((_, i) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: _jsx(Skeleton, { className: "h-4 w-4" }) }), _jsx(TableCell, { children: _jsx(Skeleton, { className: "h-4 w-24" }) }), _jsx(TableCell, { children: _jsx(Skeleton, { className: "h-4 w-32" }) }), _jsx(TableCell, { children: _jsx(Skeleton, { className: "h-4 w-24" }) }), _jsx(TableCell, { children: _jsx(Skeleton, { className: "h-4 w-16" }) }), _jsx(TableCell, { children: _jsx(Skeleton, { className: "h-4 w-16" }) }), _jsx(TableCell, { children: _jsx(Skeleton, { className: "h-4 w-16" }) }), _jsx(TableCell, { className: "text-right", children: _jsx(Skeleton, { className: "h-4 w-6" }) })] }, i)))) : filteredBookings.length > 0 ? (
                            // Actual booking data
                            filteredBookings.map((booking) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: _jsx(Checkbox, { checked: selectedBookings.includes(booking.id), onCheckedChange: () => toggleBookingSelection(booking.id), "aria-label": "Select row" }) }), _jsx(TableCell, { className: "font-medium", children: _jsx("div", { className: "font-mono text-sm", children: booking.id }) }), _jsxs(TableCell, { children: [_jsx("div", { className: "font-medium", children: booking.customer.name }), _jsx("div", { className: "text-sm text-muted-foreground", children: booking.customer.email })] }), _jsx(TableCell, { className: "text-sm", children: booking.experience }), _jsxs(TableCell, { children: [_jsx("div", { className: "text-sm", children: format(parseISO(booking.date), 'MMM d, yyyy') }), _jsx("div", { className: "text-xs text-muted-foreground", children: booking.time })] }), _jsx(TableCell, { className: "text-right font-medium", children: formatCurrency(booking.amount) }), _jsx(TableCell, { children: _jsx(Badge, { className: statusVariant[booking.status], children: t(`booking.status.${booking.status}`, { defaultValue: booking.status }) }) }), _jsx(TableCell, { className: "text-right", children: _jsxs(DropdownMenu, { children: [_jsx(DropdownMenuTrigger, { asChild: true, children: _jsxs(Button, { variant: "ghost", size: "sm", className: "h-8 w-8 p-0", children: [_jsx("span", { className: "sr-only", children: t('common.openMenu', 'Open menu') }), _jsx(MoreHorizontal, { className: "h-4 w-4" })] }) }), _jsxs(DropdownMenuContent, { align: "end", children: [_jsx(DropdownMenuLabel, { children: t('common.actions', 'Actions') }), _jsx(DropdownMenuItem, { children: t('common.view', 'View') }), _jsx(DropdownMenuItem, { children: t('common.edit', 'Edit') }), _jsx(DropdownMenuSeparator, {}), booking.status === 'pending' && (_jsx(DropdownMenuItem, { children: t('bookings.confirm', 'Confirm') })), booking.status !== 'cancelled' && (_jsx(DropdownMenuItem, { children: t('bookings.cancel', 'Cancel') })), _jsx(DropdownMenuSeparator, {}), _jsx(DropdownMenuItem, { className: "text-red-600", children: t('common.delete', 'Delete') })] })] }) })] }, booking.id)))) : (
                            // No results
                            _jsx(TableRow, { children: _jsx(TableCell, { colSpan: 8, className: "h-24 text-center", children: t('bookings.noBookingsFound', 'No bookings found.') }) })) })] }) })] }));
}
export default BookingsPage;
