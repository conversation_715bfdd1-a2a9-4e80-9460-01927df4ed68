import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Save, Settings as SettingsIcon, Mail, Lock, Bell, CreditCard, Users, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
export function SettingsPage() {
    const { t } = useTranslation();
    const [isSaving, setIsSaving] = useState(false);
    // Form state
    const [formData, setFormData] = useState({
        siteName: 'Sailing Serai',
        siteDescription: 'Luxury sailing experiences in New Zealand',
        contactEmail: '<EMAIL>',
        contactPhone: '+64 9 123 4567',
        address: '123 Harbour Road, Auckland, New Zealand',
        currency: 'NZD',
        timezone: 'Pacific/Auckland',
        dateFormat: 'DD/MM/YYYY',
        notificationsEnabled: true,
        emailNotifications: true,
        smsNotifications: false,
    });
    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };
    const handleSubmit = (e) => {
        e.preventDefault();
        setIsSaving(true);
        // Simulate API call
        setTimeout(() => {
            setIsSaving(false);
            // Show success message
        }, 1000);
    };
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { children: [_jsx("h2", { className: "text-2xl font-bold tracking-tight", children: t('settings.title', 'Settings') }), _jsx("p", { className: "text-muted-foreground", children: t('settings.subtitle', 'Manage your account and site settings') })] }), _jsxs(Tabs, { defaultValue: "general", className: "space-y-4", children: [_jsxs(TabsList, { children: [_jsxs(TabsTrigger, { value: "general", children: [_jsx(SettingsIcon, { className: "mr-2 h-4 w-4" }), t('settings.general', 'General')] }), _jsxs(TabsTrigger, { value: "notifications", children: [_jsx(Bell, { className: "mr-2 h-4 w-4" }), t('settings.notifications', 'Notifications')] }), _jsxs(TabsTrigger, { value: "billing", children: [_jsx(CreditCard, { className: "mr-2 h-4 w-4" }), t('settings.billing', 'Billing')] }), _jsxs(TabsTrigger, { value: "team", children: [_jsx(Users, { className: "mr-2 h-4 w-4" }), t('settings.team', 'Team')] }), _jsxs(TabsTrigger, { value: "localization", children: [_jsx(Globe, { className: "mr-2 h-4 w-4" }), t('settings.localization', 'Localization')] })] }), _jsxs("form", { onSubmit: handleSubmit, children: [_jsx(TabsContent, { value: "general", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: t('settings.general', 'General Settings') }), _jsx(CardDescription, { children: t('settings.generalDescription', 'Manage your site settings and preferences') })] }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "siteName", children: t('settings.siteName', 'Site Name') }), _jsx(Input, { id: "siteName", name: "siteName", value: formData.siteName, onChange: handleChange })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "contactEmail", children: t('settings.contactEmail', 'Contact Email') }), _jsxs("div", { className: "flex items-center", children: [_jsx(Mail, { className: "h-4 w-4 text-muted-foreground mr-2" }), _jsx(Input, { id: "contactEmail", name: "contactEmail", type: "email", value: formData.contactEmail, onChange: handleChange })] })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "siteDescription", children: t('settings.siteDescription', 'Site Description') }), _jsx(Input, { id: "siteDescription", name: "siteDescription", value: formData.siteDescription, onChange: handleChange })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "contactPhone", children: t('settings.contactPhone', 'Contact Phone') }), _jsx(Input, { id: "contactPhone", name: "contactPhone", value: formData.contactPhone, onChange: handleChange })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "address", children: t('settings.address', 'Business Address') }), _jsx(Input, { id: "address", name: "address", value: formData.address, onChange: handleChange })] })] })] })] }) }), _jsx(TabsContent, { value: "localization", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: t('settings.localization', 'Localization') }), _jsx(CardDescription, { children: t('settings.localizationDescription', 'Set your preferred language, timezone, and date formats') })] }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "currency", children: t('settings.currency', 'Currency') }), _jsxs("select", { id: "currency", name: "currency", value: formData.currency, onChange: (e) => setFormData({ ...formData, currency: e.target.value }), className: "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", children: [_jsx("option", { value: "NZD", children: "NZD - New Zealand Dollar" }), _jsx("option", { value: "USD", children: "USD - US Dollar" }), _jsx("option", { value: "EUR", children: "EUR - Euro" }), _jsx("option", { value: "GBP", children: "GBP - British Pound" }), _jsx("option", { value: "AUD", children: "AUD - Australian Dollar" })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "timezone", children: t('settings.timezone', 'Timezone') }), _jsxs("select", { id: "timezone", name: "timezone", value: formData.timezone, onChange: (e) => setFormData({ ...formData, timezone: e.target.value }), className: "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", children: [_jsx("option", { value: "Pacific/Auckland", children: "Auckland, New Zealand (GMT+12:00)" }), _jsx("option", { value: "Pacific/Chatham", children: "Chatham Islands, New Zealand (GMT+12:45)" }), _jsx("option", { value: "Australia/Sydney", children: "Sydney, Australia (GMT+10:00)" }), _jsx("option", { value: "UTC", children: "UTC" })] })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "dateFormat", children: t('settings.dateFormat', 'Date Format') }), _jsxs("select", { id: "dateFormat", name: "dateFormat", value: formData.dateFormat, onChange: (e) => setFormData({ ...formData, dateFormat: e.target.value }), className: "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", children: [_jsx("option", { value: "DD/MM/YYYY", children: "DD/MM/YYYY (e.g., 31/12/2023)" }), _jsx("option", { value: "MM/DD/YYYY", children: "MM/DD/YYYY (e.g., 12/31/2023)" }), _jsx("option", { value: "YYYY-MM-DD", children: "YYYY-MM-DD (e.g., 2023-12-31)" }), _jsx("option", { value: "DD MMM YYYY", children: "DD MMM YYYY (e.g., 31 Dec 2023)" })] })] })] })] }) }), _jsx(TabsContent, { value: "notifications", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: t('settings.notifications', 'Notifications') }), _jsx(CardDescription, { children: t('settings.notificationsDescription', 'Configure how you receive notifications') })] }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "flex items-center justify-between space-x-2", children: [_jsxs("div", { className: "space-y-0.5", children: [_jsx(Label, { htmlFor: "notificationsEnabled", children: t('settings.enableNotifications', 'Enable Notifications') }), _jsx("p", { className: "text-sm text-muted-foreground", children: t('settings.enableNotificationsDescription', 'Receive notifications about important updates') })] }), _jsx(Switch, { id: "notificationsEnabled", checked: formData.notificationsEnabled, onCheckedChange: (checked) => setFormData({ ...formData, notificationsEnabled: checked }) })] }), _jsxs("div", { className: "space-y-4 pl-6", children: [_jsxs("div", { className: "flex items-center justify-between space-x-2", children: [_jsxs("div", { className: "space-y-0.5", children: [_jsx(Label, { htmlFor: "emailNotifications", children: t('settings.emailNotifications', 'Email Notifications') }), _jsx("p", { className: "text-sm text-muted-foreground", children: t('settings.emailNotificationsDescription', 'Receive notifications via email') })] }), _jsx(Switch, { id: "emailNotifications", checked: formData.emailNotifications, onCheckedChange: (checked) => setFormData({ ...formData, emailNotifications: checked }), disabled: !formData.notificationsEnabled })] }), _jsxs("div", { className: "flex items-center justify-between space-x-2", children: [_jsxs("div", { className: "space-y-0.5", children: [_jsx(Label, { htmlFor: "smsNotifications", children: t('settings.smsNotifications', 'SMS Notifications') }), _jsx("p", { className: "text-sm text-muted-foreground", children: t('settings.smsNotificationsDescription', 'Receive notifications via SMS') })] }), _jsx(Switch, { id: "smsNotifications", checked: formData.smsNotifications, onCheckedChange: (checked) => setFormData({ ...formData, smsNotifications: checked }), disabled: !formData.notificationsEnabled })] })] })] })] }) }), _jsx(TabsContent, { value: "billing", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: t('settings.billing', 'Billing') }), _jsx(CardDescription, { children: t('settings.billingDescription', 'Manage your billing information and subscription') })] }), _jsxs(CardContent, { className: "space-y-4", children: [_jsx("div", { className: "rounded-md border p-4", children: _jsxs("div", { className: "flex items-center space-x-4", children: [_jsxs("div", { className: "flex-1 space-y-1", children: [_jsx("p", { className: "text-sm font-medium", children: t('settings.currentPlan', 'Current Plan') }), _jsx("p", { className: "text-sm text-muted-foreground", children: t('settings.proPlan', 'Pro Plan - $29/month') })] }), _jsx(Button, { variant: "outline", size: "sm", children: t('settings.manageSubscription', 'Manage Subscription') })] }) }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "cardName", children: t('settings.cardName', 'Name on Card') }), _jsx(Input, { id: "cardName", placeholder: t('settings.cardNamePlaceholder', 'John Doe') })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "cardNumber", children: t('settings.cardNumber', 'Card Number') }), _jsxs("div", { className: "flex", children: [_jsx(Input, { id: "cardNumber", placeholder: "4242 4242 4242 4242", className: "rounded-r-none" }), _jsx("div", { className: "flex items-center justify-center rounded-r-md border border-l-0 bg-muted px-3", children: _jsx(CreditCard, { className: "h-5 w-5 text-muted-foreground" }) })] })] })] }), _jsxs("div", { className: "grid grid-cols-2 md:grid-cols-4 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "expiry", children: t('settings.expiry', 'Expiry') }), _jsx(Input, { id: "expiry", placeholder: "MM/YY" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "cvc", children: t('settings.cvc', 'CVC') }), _jsxs("div", { className: "flex", children: [_jsx(Input, { id: "cvc", placeholder: "CVC", className: "rounded-r-none" }), _jsx("div", { className: "flex items-center justify-center rounded-r-md border border-l-0 bg-muted px-3", children: _jsx(Lock, { className: "h-4 w-4 text-muted-foreground" }) })] })] })] })] })] }) }), _jsx(TabsContent, { value: "team", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: t('settings.team', 'Team') }), _jsx(CardDescription, { children: t('settings.teamDescription', 'Manage your team members and their permissions') })] }), _jsx(CardContent, { className: "space-y-4", children: _jsx("div", { className: "rounded-md border", children: _jsxs("div", { className: "p-4", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium", children: t('settings.teamMembers', 'Team Members') }), _jsx("p", { className: "text-sm text-muted-foreground", children: t('settings.teamMembersDescription', 'Manage who has access to the admin panel') })] }), _jsx(Button, { size: "sm", children: t('settings.inviteMember', 'Invite Member') })] }), _jsx("div", { className: "mt-4 space-y-4", children: [
                                                                { name: 'Alex Johnson', email: '<EMAIL>', role: 'Admin' },
                                                                { name: 'Taylor Smith', email: '<EMAIL>', role: 'Manager' },
                                                                { name: 'Jordan Lee', email: '<EMAIL>', role: 'Staff' },
                                                            ].map((member, index) => (_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex items-center space-x-3", children: [_jsx("div", { className: "h-10 w-10 rounded-full bg-muted flex items-center justify-center", children: _jsx("span", { className: "text-sm font-medium", children: member.name.split(' ').map(n => n[0]).join('').toUpperCase() }) }), _jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium", children: member.name }), _jsx("p", { className: "text-xs text-muted-foreground", children: member.email })] })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx("span", { className: "text-sm text-muted-foreground", children: member.role }), _jsx(Button, { variant: "ghost", size: "sm", children: t('settings.edit', 'Edit') })] })] }, index))) })] }) }) })] }) }), _jsxs("div", { className: "flex justify-end pt-4", children: [_jsx(Button, { type: "button", variant: "outline", className: "mr-2", children: t('common.cancel', 'Cancel') }), _jsx(Button, { type: "submit", disabled: isSaving, children: isSaving ? (_jsxs(_Fragment, { children: [_jsxs("svg", { className: "animate-spin -ml-1 mr-2 h-4 w-4 text-white", xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24", children: [_jsx("circle", { className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4" }), _jsx("path", { className: "opacity-75", fill: "currentColor", d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" })] }), t('common.saving', 'Saving...')] })) : (_jsxs(_Fragment, { children: [_jsx(Save, { className: "mr-2 h-4 w-4" }), t('common.saveChanges', 'Save Changes')] })) })] })] })] })] }));
}
export default SettingsPage;
