import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format, startOfWeek, addDays, startOfDay, endOfDay, isWeekend } from 'date-fns';
import { Calendar as BigCalendar, dateFnsLocalizer, SlotInfo, View } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';

// Import shadcn/ui components
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarIcon } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';

// Define types
type CalendarViewType = 'day' | 'week' | 'month';
type AvailabilityStatus = 'available' | 'booked' | 'blocked';

interface AvailabilityEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  status: AvailabilityStatus;
  resource?: any;
}

// Set up the localizer for the calendar
const localizer = dateFnsLocalizer({
  format,
  startOfWeek: () => startOfWeek(new Date(), { weekStartsOn: 1 }),
  getDay: (date: Date) => date.getDay(),
  locales: {},
});

interface BulkBlockModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (startDate: Date, endDate: Date, reason: string) => void;
}

const BulkBlockModal: React.FC<BulkBlockModalProps> = ({ isOpen, onClose, onConfirm }) => {
  const { t } = useTranslation();
  const [startDate, setStartDate] = useState<Date | undefined>(new Date());
  const [endDate, setEndDate] = useState<Date | undefined>(() => {
    const date = new Date();
    date.setDate(date.getDate() + 7); // Default to one week
    return date;
  });
  const [reason, setReason] = useState('');
  const [isStartDatePickerOpen, setIsStartDatePickerOpen] = useState(false);
  const [isEndDatePickerOpen, setIsEndDatePickerOpen] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (startDate && endDate) {
      onConfirm(startDate, endDate, reason);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t('calendar.bulkBlockTitle', 'Block Multiple Dates')}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start-date">{t('calendar.startDate', 'Start Date')}</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="space-y-2">
              <Label htmlFor="end-date">{t('calendar.endDate', 'End Date')}</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="reason">{t('calendar.reason', 'Reason (Optional)')}</Label>
            <input
              id="reason"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder={t('calendar.reasonPlaceholder', 'E.g., Maintenance, Holiday')}
            />
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button type="submit" disabled={!startDate || !endDate}>
              {t('calendar.blockDates', 'Block Dates')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export function CalendarPage() {
  const { t } = useTranslation();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState<{ start: Date; end: Date } | null>(null);
  const [events, setEvents] = useState<AvailabilityEvent[]>([]);
  const [view, setView] = useState<CalendarViewType>('month');
  const [date, setDate] = useState<Date>(new Date());
  const [eventStatus, setEventStatus] = useState<AvailabilityStatus>('available');
  // State for bulk block modal
  const [isBulkBlockOpen, setIsBulkBlockOpen] = useState(false);
  const [isStartDatePickerOpen, setIsStartDatePickerOpen] = useState(false);
  const [isEndDatePickerOpen, setIsEndDatePickerOpen] = useState(false);

  // Handle slot selection
  const handleSelectSlot = useCallback((slotInfo: SlotInfo) => {
    setSelectedSlot({ 
      start: startOfDay(slotInfo.start), 
      end: endOfDay(slotInfo.end || slotInfo.start) 
    });
    setEventStatus('available');
    setIsDialogOpen(true);
  }, []);

  // Handle bulk block confirm
  const handleBulkBlockConfirm = useCallback((startDate: Date, endDate: Date, reason: string) => {
    const newEvents: AvailabilityEvent[] = [];
    let currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      newEvents.push({
        id: `block-${currentDate.toISOString()}`,
        title: reason || 'Blocked',
        start: startOfDay(new Date(currentDate)),
        end: endOfDay(new Date(currentDate)),
        status: 'blocked' as const,
      });
      currentDate = addDays(currentDate, 1);
    }
    
    setEvents(prevEvents => [...prevEvents, ...newEvents]);
  }, []);

  // Handle event save
  const handleSaveEvent = useCallback(() => {
    if (!selectedSlot) return;
    
    const newEvent: AvailabilityEvent = {
      id: `event-${Date.now()}`,
      title: 'New Event',
      start: selectedSlot.start,
      end: selectedSlot.end,
      status: eventStatus,
    };
    
    setEvents(prev => [...prev, newEvent]);
    setIsDialogOpen(false);
  }, [selectedSlot, eventStatus]);

  // Handle event selection
  const handleSelectEvent = useCallback((selectedEvent: AvailabilityEvent) => {
    setSelectedSlot({
      start: selectedEvent.start,
      end: selectedEvent.end || selectedEvent.start
    });
    setEventStatus(selectedEvent.status);
    setIsDialogOpen(true);
  }, []);

  // Handle view change
  const handleViewChange = (newView: View) => {
    setView(newView as CalendarViewType);
  };

  // Handle navigation
  const handleNavigate = (newDate: Date) => {
    setDate(newDate);
  };

  // Style events based on their status
  const eventStyleGetter = (event: AvailabilityEvent) => {
    let backgroundColor = '';
    switch (event.status) {
      case 'booked':
        backgroundColor = '#4f46e5'; // indigo-600
        break;
      case 'blocked':
        backgroundColor = '#ef4444'; // red-500
        break;
      default:
        backgroundColor = '#10b981'; // emerald-500
    }

    return {
      style: {
        backgroundColor,
        borderRadius: '4px',
        opacity: 0.8,
        color: 'white',
        border: '0',
        display: 'block',
      },
    };
  };

  // Custom event component
  const EventComponent = ({ event }: { event: AvailabilityEvent }) => (
    <div className={`p-1 rounded text-xs ${event.status === 'blocked' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`}>
      {event.title}
    </div>
  );

  // Initialize with some mock events for demonstration
  useEffect(() => {
    const mockEvents: AvailabilityEvent[] = [
      {
        id: '1',
        title: 'Available',
        start: new Date(),
        end: new Date(new Date().setHours(new Date().getHours() + 2)),
        status: 'available' as const
      },
      {
        id: '2',
        title: 'Booked',
        start: new Date(new Date().setDate(new Date().getDate() + 1)),
        end: new Date(new Date().setDate(new Date().getDate() + 1)),
        status: 'booked' as const
      }
    ];
    setEvents(mockEvents);
  }, []);

  // Inline toolbar component for the calendar
  const CustomToolbar = (toolbar: any) => (
    <div className="flex justify-between mb-4">
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" onClick={() => toolbar.onNavigate('TODAY')}>
          {t('calendar.today', 'Today')}
        </Button>
        <Button variant="outline" size="sm" onClick={() => toolbar.onNavigate('PREV')}>
          &lt;
        </Button>
        <Button variant="outline" size="sm" onClick={() => toolbar.onNavigate('NEXT')}>
          &gt;
        </Button>
      </div>
      <div className="flex items-center space-x-2">
        <Button 
          variant={view === 'month' ? 'default' : 'outline'} 
          size="sm" 
          onClick={() => setView('month')}
        >
          Month
        </Button>
        <Button 
          variant={view === 'week' ? 'default' : 'outline'} 
          size="sm" 
          onClick={() => setView('week')}
        >
          Week
        </Button>
        <Button 
          variant={view === 'day' ? 'default' : 'outline'} 
          size="sm" 
          onClick={() => setView('day')}
        >
          Day
        </Button>
      </div>
    </div>
  );

  useEffect(() => {
    const mockEvents: AvailabilityEvent[] = [
      {
        id: '1',
        title: 'Available',
        start: new Date(),
        end: new Date(new Date().setHours(new Date().getHours() + 2)),
        status: 'available' as const
      },
      {
        id: '2',
        title: 'Booked',
        start: new Date(new Date().setDate(new Date().getDate() + 1)),
        end: new Date(new Date().setDate(new Date().getDate() + 1)),
        status: 'booked' as const
      }
    ];
    setEvents(mockEvents);
  }, []);

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Calendar</h1>
        <div className="flex space-x-2">
          <BulkBlockModal
            isOpen={isBulkBlockOpen}
            onClose={() => setIsBulkBlockOpen(false)}
            onConfirm={(start, end, reason) => {
              handleBulkBlockConfirm(start, end, reason);
              setIsBulkBlockOpen(false);
            }}
          />
          <Button 
            variant="outline" 
            onClick={() => setIsBulkBlockOpen(true)}
            disabled={isBulkBlockOpen}
          >
            Bulk Block Dates
          </Button>
          <Button 
            onClick={() => {
              setSelectedSlot({ start: new Date(), end: new Date() });
              setEventStatus('available');
              setIsDialogOpen(true);
            }}
          >
            Add Event
          </Button>
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg shadow">
        <div className="h-[600px] w-full">
          <BigCalendar
            localizer={localizer}
            events={events}
            startAccessor="start"
            endAccessor="end"
            style={{ height: '100%' }}
            selectable
            onSelectSlot={handleSelectSlot}
            onSelectEvent={handleSelectEvent}
            view={view as View}
            onView={handleViewChange}
            date={date}
            onNavigate={handleNavigate}
            eventPropGetter={eventStyleGetter}
            components={{ event: EventComponent, toolbar: CustomToolbar }}
            messages={{
              next: '>',
              previous: '<',
              today: 'Today',
              month: 'Month',
              week: 'Week',
              day: 'Day',
            }}
          />
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Event</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div>
              <p className="text-sm font-medium mb-2">Date</p>
              <div className="border rounded-md p-2">
                {selectedSlot && (
                  <p>{`${format(selectedSlot.start, 'PPP')} - ${format(selectedSlot.end, 'PPP')}`}</p>
                )}
              </div>
            </div>
            <div>
              <p className="text-sm font-medium mb-2">Status</p>
              <select 
                className="w-full p-2 border rounded"
                value={eventStatus}
                onChange={(e) => setEventStatus(e.target.value as AvailabilityStatus)}
              >
                <option value="available">Available</option>
                <option value="booked">Booked</option>
                <option value="blocked">Blocked</option>
              </select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveEvent}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default CalendarPage;
