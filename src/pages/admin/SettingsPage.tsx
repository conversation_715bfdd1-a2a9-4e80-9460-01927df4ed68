import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Save, Settings as SettingsIcon, Mail, Lock, Bell, CreditCard, Users, Globe } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

export function SettingsPage() {
  const { t } = useTranslation();
  const [isSaving, setIsSaving] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState({
    siteName: 'Sailing Serai',
    siteDescription: 'Luxury sailing experiences in New Zealand',
    contactEmail: '<EMAIL>',
    contactPhone: '+64 9 123 4567',
    address: '123 Harbour Road, Auckland, New Zealand',
    currency: 'NZD',
    timezone: 'Pacific/Auckland',
    dateFormat: 'DD/MM/YYYY',
    notificationsEnabled: true,
    emailNotifications: true,
    smsNotifications: false,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      // Show success message
    }, 1000);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">
          {t('settings.title', 'Settings')}
        </h2>
        <p className="text-muted-foreground">
          {t('settings.subtitle', 'Manage your account and site settings')}
        </p>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">
            <SettingsIcon className="mr-2 h-4 w-4" />
            {t('settings.general', 'General')}
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="mr-2 h-4 w-4" />
            {t('settings.notifications', 'Notifications')}
          </TabsTrigger>
          <TabsTrigger value="billing">
            <CreditCard className="mr-2 h-4 w-4" />
            {t('settings.billing', 'Billing')}
          </TabsTrigger>
          <TabsTrigger value="team">
            <Users className="mr-2 h-4 w-4" />
            {t('settings.team', 'Team')}
          </TabsTrigger>
          <TabsTrigger value="localization">
            <Globe className="mr-2 h-4 w-4" />
            {t('settings.localization', 'Localization')}
          </TabsTrigger>
        </TabsList>

        <form onSubmit={handleSubmit}>
          <TabsContent value="general" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('settings.general', 'General Settings')}</CardTitle>
                <CardDescription>
                  {t('settings.generalDescription', 'Manage your site settings and preferences')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="siteName">{t('settings.siteName', 'Site Name')}</Label>
                    <Input
                      id="siteName"
                      name="siteName"
                      value={formData.siteName}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="contactEmail">
                      {t('settings.contactEmail', 'Contact Email')}
                    </Label>
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 text-muted-foreground mr-2" />
                      <Input
                        id="contactEmail"
                        name="contactEmail"
                        type="email"
                        value={formData.contactEmail}
                        onChange={handleChange}
                      />
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="siteDescription">
                    {t('settings.siteDescription', 'Site Description')}
                  </Label>
                  <Input
                    id="siteDescription"
                    name="siteDescription"
                    value={formData.siteDescription}
                    onChange={handleChange}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contactPhone">
                      {t('settings.contactPhone', 'Contact Phone')}
                    </Label>
                    <Input
                      id="contactPhone"
                      name="contactPhone"
                      value={formData.contactPhone}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">
                      {t('settings.address', 'Business Address')}
                    </Label>
                    <Input
                      id="address"
                      name="address"
                      value={formData.address}
                      onChange={handleChange}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="localization" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('settings.localization', 'Localization')}</CardTitle>
                <CardDescription>
                  {t('settings.localizationDescription', 'Set your preferred language, timezone, and date formats')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="currency">
                      {t('settings.currency', 'Currency')}
                    </Label>
                    <select
                      id="currency"
                      name="currency"
                      value={formData.currency}
                      onChange={(e) => setFormData({...formData, currency: e.target.value})}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="NZD">NZD - New Zealand Dollar</option>
                      <option value="USD">USD - US Dollar</option>
                      <option value="EUR">EUR - Euro</option>
                      <option value="GBP">GBP - British Pound</option>
                      <option value="AUD">AUD - Australian Dollar</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="timezone">
                      {t('settings.timezone', 'Timezone')}
                    </Label>
                    <select
                      id="timezone"
                      name="timezone"
                      value={formData.timezone}
                      onChange={(e) => setFormData({...formData, timezone: e.target.value})}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="Pacific/Auckland">Auckland, New Zealand (GMT+12:00)</option>
                      <option value="Pacific/Chatham">Chatham Islands, New Zealand (GMT+12:45)</option>
                      <option value="Australia/Sydney">Sydney, Australia (GMT+10:00)</option>
                      <option value="UTC">UTC</option>
                    </select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="dateFormat">
                    {t('settings.dateFormat', 'Date Format')}
                  </Label>
                  <select
                    id="dateFormat"
                    name="dateFormat"
                    value={formData.dateFormat}
                    onChange={(e) => setFormData({...formData, dateFormat: e.target.value})}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="DD/MM/YYYY">DD/MM/YYYY (e.g., 31/12/2023)</option>
                    <option value="MM/DD/YYYY">MM/DD/YYYY (e.g., 12/31/2023)</option>
                    <option value="YYYY-MM-DD">YYYY-MM-DD (e.g., 2023-12-31)</option>
                    <option value="DD MMM YYYY">DD MMM YYYY (e.g., 31 Dec 2023)</option>
                  </select>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('settings.notifications', 'Notifications')}</CardTitle>
                <CardDescription>
                  {t('settings.notificationsDescription', 'Configure how you receive notifications')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between space-x-2">
                  <div className="space-y-0.5">
                    <Label htmlFor="notificationsEnabled">
                      {t('settings.enableNotifications', 'Enable Notifications')}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {t('settings.enableNotificationsDescription', 'Receive notifications about important updates')}
                    </p>
                  </div>
                  <Switch
                    id="notificationsEnabled"
                    checked={formData.notificationsEnabled}
                    onCheckedChange={(checked) => 
                      setFormData({...formData, notificationsEnabled: checked})
                    }
                  />
                </div>
                
                <div className="space-y-4 pl-6">
                  <div className="flex items-center justify-between space-x-2">
                    <div className="space-y-0.5">
                      <Label htmlFor="emailNotifications">
                        {t('settings.emailNotifications', 'Email Notifications')}
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        {t('settings.emailNotificationsDescription', 'Receive notifications via email')}
                      </p>
                    </div>
                    <Switch
                      id="emailNotifications"
                      checked={formData.emailNotifications}
                      onCheckedChange={(checked) => 
                        setFormData({...formData, emailNotifications: checked})
                      }
                      disabled={!formData.notificationsEnabled}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between space-x-2">
                    <div className="space-y-0.5">
                      <Label htmlFor="smsNotifications">
                        {t('settings.smsNotifications', 'SMS Notifications')}
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        {t('settings.smsNotificationsDescription', 'Receive notifications via SMS')}
                      </p>
                    </div>
                    <Switch
                      id="smsNotifications"
                      checked={formData.smsNotifications}
                      onCheckedChange={(checked) => 
                        setFormData({...formData, smsNotifications: checked})
                      }
                      disabled={!formData.notificationsEnabled}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="billing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('settings.billing', 'Billing')}</CardTitle>
                <CardDescription>
                  {t('settings.billingDescription', 'Manage your billing information and subscription')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="rounded-md border p-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">
                        {t('settings.currentPlan', 'Current Plan')}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {t('settings.proPlan', 'Pro Plan - $29/month')}
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      {t('settings.manageSubscription', 'Manage Subscription')}
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="cardName">
                      {t('settings.cardName', 'Name on Card')}
                    </Label>
                    <Input
                      id="cardName"
                      placeholder={t('settings.cardNamePlaceholder', 'John Doe')}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cardNumber">
                      {t('settings.cardNumber', 'Card Number')}
                    </Label>
                    <div className="flex">
                      <Input
                        id="cardNumber"
                        placeholder="4242 4242 4242 4242"
                        className="rounded-r-none"
                      />
                      <div className="flex items-center justify-center rounded-r-md border border-l-0 bg-muted px-3">
                        <CreditCard className="h-5 w-5 text-muted-foreground" />
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="expiry">
                      {t('settings.expiry', 'Expiry')}
                    </Label>
                    <Input
                      id="expiry"
                      placeholder="MM/YY"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cvc">
                      {t('settings.cvc', 'CVC')}
                    </Label>
                    <div className="flex">
                      <Input
                        id="cvc"
                        placeholder="CVC"
                        className="rounded-r-none"
                      />
                      <div className="flex items-center justify-center rounded-r-md border border-l-0 bg-muted px-3">
                        <Lock className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="team" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('settings.team', 'Team')}</CardTitle>
                <CardDescription>
                  {t('settings.teamDescription', 'Manage your team members and their permissions')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="rounded-md border">
                  <div className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium">
                          {t('settings.teamMembers', 'Team Members')}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {t('settings.teamMembersDescription', 'Manage who has access to the admin panel')}
                        </p>
                      </div>
                      <Button size="sm">
                        {t('settings.inviteMember', 'Invite Member')}
                      </Button>
                    </div>
                    
                    <div className="mt-4 space-y-4">
                      {[
                        { name: 'Alex Johnson', email: '<EMAIL>', role: 'Admin' },
                        { name: 'Taylor Smith', email: '<EMAIL>', role: 'Manager' },
                        { name: 'Jordan Lee', email: '<EMAIL>', role: 'Staff' },
                      ].map((member, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                              <span className="text-sm font-medium">
                                {member.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                              </span>
                            </div>
                            <div>
                              <p className="text-sm font-medium">{member.name}</p>
                              <p className="text-xs text-muted-foreground">{member.email}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-muted-foreground">{member.role}</span>
                            <Button variant="ghost" size="sm">
                              {t('settings.edit', 'Edit')}
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <div className="flex justify-end pt-4">
            <Button type="button" variant="outline" className="mr-2">
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {t('common.saveChanges', 'Save Changes')}
                </>
              )}
            </Button>
          </div>
        </form>
      </Tabs>
    </div>
  );
}

export default SettingsPage;
