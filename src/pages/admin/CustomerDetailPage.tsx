import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { ArrowLeft, Calendar, Edit, Mail, Phone, User } from 'lucide-react';

import { Customer } from '@/types/customer';
import { getCustomerById, updateCustomer } from '@/lib/api/customers';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// Avatar components are not currently used in this file
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatCurrency } from '@/lib/utils';

export function CustomerDetailPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  // Active tab state is kept for potential future use
  const [, setActiveTab] = useState('overview');
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<Omit<Customer, 'id' | 'joinDate' | 'totalBookings' | 'totalSpent'>>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    avatar: '',
    notes: '',
    tags: [],
    address: {},
    preferences: { communication: { email: false, sms: false } },
    metadata: {}
  });

  // Fetch customer data
  useEffect(() => {
    const fetchCustomer = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const data = await getCustomerById(id);
        if (data) {
          setCustomer(data);
          // Only set the form fields that we allow editing
          setFormData({
            firstName: data.firstName || '',
            lastName: data.lastName || '',
            email: data.email || '',
            phone: data.phone || '',
            avatar: data.avatar || '',
            notes: data.notes || '',
            tags: data.tags || [],
            address: data.address || {},
            preferences: data.preferences || { communication: { email: false, sms: false } },
            metadata: data.metadata || {}
          });
        } else {
          navigate('/admin/customers');
        }
      } catch (error) {
        console.error('Error fetching customer:', error);
        if (error instanceof Error) {
          console.error(error.message);
        }
        // Show error to user if needed
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [id, navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    // Handle different input types
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      // Handle nested preferences.communication fields
      if (name.startsWith('preferences.communication.')) {
        const field = name.split('.')[2] as 'email' | 'sms';
        setFormData(prev => ({
          ...prev,
          preferences: {
            ...prev.preferences,
            communication: {
              ...(prev.preferences?.communication || { email: false, sms: false }),
              [field]: checked
            }
          }
        }));
      } else {
        setFormData(prev => ({ ...prev, [name]: checked }));
      }
    } else {
      // Handle text inputs
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id || !customer) return;
    
    try {
      // Only send the fields that can be updated
      const updateData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        avatar: formData.avatar,
        notes: formData.notes,
        tags: formData.tags,
        address: formData.address,
        preferences: formData.preferences,
        metadata: formData.metadata
      };
      
      const updatedCustomer = await updateCustomer(id, updateData);
      if (updatedCustomer) {
        setCustomer(updatedCustomer);
        setIsEditing(false);
      }
    } catch (error) {
      console.error('Error updating customer:', error);
      // Show error to user if needed
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!customer) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium">
          {t('customers.customerNotFound', 'Customer not found')}
        </h3>
        <div className="mt-6">
          <Button onClick={() => navigate('/admin/customers')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('customers.backToCustomers', 'Back to Customers')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="outline" size="icon" onClick={() => navigate('/admin/customers')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-2xl font-bold">
          {isEditing ? (
            <input
              type="text"
              name="firstName"
              value={formData.firstName || ''}
              onChange={handleInputChange}
              className="bg-transparent border-b border-muted-foreground"
            />
          ) : (
            `${customer.firstName} ${customer.lastName}`
          )}
        </h2>
        <div className="ml-auto flex items-center space-x-2">
          {!isEditing ? (
            <Button variant="outline" onClick={() => setIsEditing(true)}>
              <Edit className="mr-2 h-4 w-4" />
              {t('common.edit', 'Edit')}
            </Button>
          ) : (
            <>
              <Button variant="outline" onClick={() => {
                setFormData(customer);
                setIsEditing(false);
              }}>
                {t('common.cancel', 'Cancel')}
              </Button>
              <Button onClick={handleSubmit}>
                {t('common.saveChanges', 'Save Changes')}
              </Button>
            </>
          )}
        </div>
      </div>

      <Tabs defaultValue="overview" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">
            <User className="mr-2 h-4 w-4" />
            {t('common.overview', 'Overview')}
          </TabsTrigger>
          <TabsTrigger value="bookings">
            <Calendar className="mr-2 h-4 w-4" />
            {t('bookings.title', 'Bookings')}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <div className="text-sm font-medium">
                  {t('customers.customerSince', 'Customer Since')}
                </div>
                <div className="text-2xl font-bold">
                  {format(new Date(customer.joinDate), 'MMM yyyy')}
                </div>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader>
                <div className="text-sm font-medium">
                  {t('bookings.totalBookings', 'Total Bookings')}
                </div>
                <div className="text-2xl font-bold">{customer.totalBookings}</div>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader>
                <div className="text-sm font-medium">
                  {t('customers.totalSpent', 'Total Spent')}
                </div>
                <div className="text-2xl font-bold">
                  {formatCurrency(customer.totalSpent)}
                </div>
              </CardHeader>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>{t('common.contactInformation', 'Contact Information')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    {t('common.email', 'Email')}
                  </label>
                  {isEditing ? (
                    <input
                      type="email"
                      name="email"
                      value={formData.email || ''}
                      onChange={handleInputChange}
                      className="w-full p-2 border rounded"
                    />
                  ) : (
                    <div className="flex items-center">
                      <Mail className="mr-2 h-4 w-4" />
                      {customer.email}
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    {t('common.phone', 'Phone')}
                  </label>
                  {isEditing ? (
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone || ''}
                      onChange={handleInputChange}
                      className="w-full p-2 border rounded"
                    />
                  ) : (
                    <div className="flex items-center">
                      <Phone className="mr-2 h-4 w-4" />
                      {customer.phone || t('common.notProvided', 'Not provided')}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bookings">
          <Card>
            <CardHeader>
              <CardTitle>{t('bookings.bookingHistory', 'Booking History')}</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('bookings.bookingId', 'ID')}</TableHead>
                    <TableHead>{t('common.date', 'Date')}</TableHead>
                    <TableHead>{t('common.experience', 'Experience')}</TableHead>
                    <TableHead className="text-right">{t('common.total', 'Total')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>B001</TableCell>
                    <TableCell>{format(new Date(), 'MMM d, yyyy')}</TableCell>
                    <TableCell>Sunset Sail</TableCell>
                    <TableCell className="text-right">$198.00</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default CustomerDetailPage;
