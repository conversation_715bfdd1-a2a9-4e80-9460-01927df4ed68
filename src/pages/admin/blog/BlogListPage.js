import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Plus, Search, Loader2, Edit, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { getBlogPosts, deleteBlogPost } from '@/api/blog';
import { format } from 'date-fns';
import { toast } from '@/components/ui/use-toast';
export const BlogListPage = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [posts, setPosts] = useState([]);
    const [filteredPosts, setFilteredPosts] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [isDeleting, setIsDeleting] = useState({});
    // Fetch blog posts
    useEffect(() => {
        const fetchPosts = async () => {
            try {
                setIsLoading(true);
                const data = await getBlogPosts();
                setPosts(data);
                setFilteredPosts(data);
            }
            catch (error) {
                console.error('Error fetching blog posts:', error);
                toast({
                    title: t('error', 'Error'),
                    description: t('blog.errorFetchingPosts', 'Failed to load blog posts. Please try again.'),
                    variant: 'destructive',
                });
            }
            finally {
                setIsLoading(false);
            }
        };
        fetchPosts();
    }, [t]);
    // Filter posts based on search term
    useEffect(() => {
        if (!searchTerm.trim()) {
            setFilteredPosts(posts);
            return;
        }
        const lowercasedFilter = searchTerm.toLowerCase();
        const filtered = posts.filter((post) => post.title.toLowerCase().includes(lowercasedFilter) ||
            post.excerpt.toLowerCase().includes(lowercasedFilter) ||
            post.tags?.some((tag) => tag.toLowerCase().includes(lowercasedFilter)) ||
            post.author.name.toLowerCase().includes(lowercasedFilter));
        setFilteredPosts(filtered);
    }, [searchTerm, posts]);
    const handleDelete = async (postId) => {
        if (!window.confirm(t('blog.deleteConfirm', 'Are you sure you want to delete this post?'))) {
            return;
        }
        try {
            setIsDeleting(prev => ({ ...prev, [postId]: true }));
            await deleteBlogPost(postId);
            // Update the posts list
            const updatedPosts = posts.filter((post) => post.id !== postId);
            setPosts(updatedPosts);
            toast({
                title: t('success', 'Success'),
                description: t('blog.postDeleted', 'The blog post has been deleted.'),
            });
        }
        catch (error) {
            console.error('Error deleting blog post:', error);
            toast({
                title: t('error', 'Error'),
                description: t('blog.errorDeletingPost', 'Failed to delete the blog post. Please try again.'),
                variant: 'destructive',
            });
        }
        finally {
            setIsDeleting(prev => {
                const newState = { ...prev };
                delete newState[postId];
                return newState;
            });
        }
    };
    const getStatusBadge = (status) => {
        switch (status) {
            case 'published':
                return _jsx(Badge, { variant: "success", children: t('blog.published', 'Published') });
            case 'draft':
                return _jsx(Badge, { variant: "outline", children: t('blog.draft', 'Draft') });
            case 'archived':
                return _jsx(Badge, { variant: "secondary", children: t('blog.archived', 'Archived') });
            default:
                return _jsx(Badge, { variant: "outline", children: status });
        }
    };
    if (isLoading && posts.length === 0) {
        return (_jsxs("div", { className: "flex items-center justify-center h-64", children: [_jsx(Loader2, { className: "h-8 w-8 animate-spin text-primary" }), _jsx("span", { className: "ml-2", children: t('blog.loadingPosts', 'Loading blog posts...') })] }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-2xl font-bold tracking-tight", children: t('blog.blogPosts', 'Blog Posts') }), _jsx("p", { className: "text-muted-foreground", children: t('blog.managePosts', 'Manage your blog posts and content') })] }), _jsxs(Button, { onClick: () => navigate('/admin/blog/new'), children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), t('blog.newPost', 'New Post')] })] }), _jsxs("div", { className: "bg-white dark:bg-gray-800 rounded-lg border shadow-sm", children: [_jsx("div", { className: "p-4 border-b", children: _jsxs("div", { className: "relative max-w-md", children: [_jsx(Search, { className: "absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" }), _jsx(Input, { type: "search", placeholder: t('blog.searchPosts', 'Search posts...'), className: "pl-9", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value) })] }) }), _jsx("div", { className: "overflow-x-auto", children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: t('blog.title', 'Title') }), _jsx(TableHead, { children: t('blog.author', 'Author') }), _jsx(TableHead, { children: t('blog.status', 'Status') }), _jsx(TableHead, { children: t('blog.publishedDate', 'Published') }), _jsx(TableHead, { className: "text-right", children: t('common.actions', 'Actions') })] }) }), _jsx(TableBody, { children: filteredPosts.length > 0 ? (filteredPosts.map((post) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: _jsxs("div", { className: "flex items-center", children: [post.featuredImage && (_jsx("img", { src: post.featuredImage, alt: post.title, className: "h-10 w-10 rounded-md object-cover mr-3" })), _jsxs("div", { children: [_jsx("div", { className: "font-medium", children: post.title }), _jsx("div", { className: "text-xs text-muted-foreground line-clamp-1", children: post.excerpt })] })] }) }), _jsx(TableCell, { children: _jsxs("div", { className: "flex items-center", children: [post.author.avatar && (_jsx("img", { src: post.author.avatar, alt: post.author.name, className: "h-6 w-6 rounded-full mr-2" })), _jsx("span", { children: post.author.name })] }) }), _jsx(TableCell, { children: getStatusBadge(post.status) }), _jsx(TableCell, { children: format(new Date(post.publishedAt), 'MMM d, yyyy') }), _jsx(TableCell, { className: "text-right", children: _jsxs("div", { className: "flex justify-end space-x-2", children: [_jsxs(Button, { variant: "ghost", size: "icon", onClick: () => navigate(`/admin/blog/edit/${post.id}`), children: [_jsx(Edit, { className: "h-4 w-4" }), _jsx("span", { className: "sr-only", children: t('common.edit', 'Edit') })] }), _jsxs(Button, { variant: "ghost", size: "icon", onClick: () => handleDelete(post.id), disabled: isDeleting[post.id], children: [isDeleting[post.id] ? (_jsx(Loader2, { className: "h-4 w-4 animate-spin" })) : (_jsx(Trash2, { className: "h-4 w-4 text-destructive" })), _jsx("span", { className: "sr-only", children: t('common.delete', 'Delete') })] })] }) })] }, post.id)))) : (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: 5, className: "text-center py-8 text-muted-foreground", children: searchTerm
                                                ? t('blog.noMatchingPosts', 'No blog posts match your search.')
                                                : t('blog.noPosts', 'No blog posts found.') }) })) })] }) })] })] }));
};
export default BlogListPage;
