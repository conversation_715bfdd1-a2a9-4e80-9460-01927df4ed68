import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Plus, Search, Loader2, Edit, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { BlogPost } from '@/types/blog';
import { getBlogPosts, deleteBlogPost } from '@/api/blog';
import { format } from 'date-fns';
import { toast } from '@/components/ui/use-toast';

export const BlogListPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDeleting, setIsDeleting] = useState<Record<string, boolean>>({});

  // Fetch blog posts
  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setIsLoading(true);
        const data = await getBlogPosts();
        setPosts(data);
        setFilteredPosts(data);
      } catch (error) {
        console.error('Error fetching blog posts:', error);
        toast({
          title: t('error', 'Error'),
          description: t('blog.errorFetchingPosts', 'Failed to load blog posts. Please try again.'),
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchPosts();
  }, [t]);

  // Filter posts based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredPosts(posts);
      return;
    }

    const lowercasedFilter = searchTerm.toLowerCase();
    const filtered = posts.filter(
      (post) =>
        post.title.toLowerCase().includes(lowercasedFilter) ||
        post.excerpt.toLowerCase().includes(lowercasedFilter) ||
        post.tags?.some((tag) => tag.toLowerCase().includes(lowercasedFilter)) ||
        post.author.name.toLowerCase().includes(lowercasedFilter)
    );
    setFilteredPosts(filtered);
  }, [searchTerm, posts]);

  const handleDelete = async (postId: string) => {
    if (!window.confirm(t('blog.deleteConfirm', 'Are you sure you want to delete this post?'))) {
      return;
    }

    try {
      setIsDeleting(prev => ({ ...prev, [postId]: true }));
      await deleteBlogPost(postId);
      
      // Update the posts list
      const updatedPosts = posts.filter((post) => post.id !== postId);
      setPosts(updatedPosts);
      
      toast({
        title: t('success', 'Success'),
        description: t('blog.postDeleted', 'The blog post has been deleted.'),
      });
    } catch (error) {
      console.error('Error deleting blog post:', error);
      toast({
        title: t('error', 'Error'),
        description: t('blog.errorDeletingPost', 'Failed to delete the blog post. Please try again.'),
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(prev => {
        const newState = { ...prev };
        delete newState[postId];
        return newState;
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge variant="success">{t('blog.published', 'Published')}</Badge>;
      case 'draft':
        return <Badge variant="outline">{t('blog.draft', 'Draft')}</Badge>;
      case 'archived':
        return <Badge variant="secondary">{t('blog.archived', 'Archived')}</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (isLoading && posts.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">
          {t('blog.loadingPosts', 'Loading blog posts...')}
        </span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            {t('blog.blogPosts', 'Blog Posts')}
          </h1>
          <p className="text-muted-foreground">
            {t('blog.managePosts', 'Manage your blog posts and content')}
          </p>
        </div>
        <Button onClick={() => navigate('/admin/blog/new')}>
          <Plus className="h-4 w-4 mr-2" />
          {t('blog.newPost', 'New Post')}
        </Button>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg border shadow-sm">
        <div className="p-4 border-b">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              type="search"
              placeholder={t('blog.searchPosts', 'Search posts...')}
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('blog.title', 'Title')}</TableHead>
                <TableHead>{t('blog.author', 'Author')}</TableHead>
                <TableHead>{t('blog.status', 'Status')}</TableHead>
                <TableHead>{t('blog.publishedDate', 'Published')}</TableHead>
                <TableHead className="text-right">
                  {t('common.actions', 'Actions')}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPosts.length > 0 ? (
                filteredPosts.map((post) => (
                  <TableRow key={post.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        {post.featuredImage && (
                          <img
                            src={post.featuredImage}
                            alt={post.title}
                            className="h-10 w-10 rounded-md object-cover mr-3"
                          />
                        )}
                        <div>
                          <div className="font-medium">{post.title}</div>
                          <div className="text-xs text-muted-foreground line-clamp-1">
                            {post.excerpt}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {post.author.avatar && (
                          <img
                            src={post.author.avatar}
                            alt={post.author.name}
                            className="h-6 w-6 rounded-full mr-2"
                          />
                        )}
                        <span>{post.author.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(post.status)}</TableCell>
                    <TableCell>
                      {format(new Date(post.publishedAt), 'MMM d, yyyy')}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => navigate(`/admin/blog/edit/${post.id}`)}
                        >
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">
                            {t('common.edit', 'Edit')}
                          </span>
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDelete(post.id)}
                          disabled={isDeleting[post.id]}
                        >
                          {isDeleting[post.id] ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4 text-destructive" />
                          )}
                          <span className="sr-only">
                            {t('common.delete', 'Delete')}
                          </span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                    {searchTerm
                      ? t('blog.noMatchingPosts', 'No blog posts match your search.')
                      : t('blog.noPosts', 'No blog posts found.')}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default BlogListPage;
