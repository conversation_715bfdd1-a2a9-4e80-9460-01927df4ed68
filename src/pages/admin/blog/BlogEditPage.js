import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, Loader2, Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { BlogPostForm } from '@/components/blog/BlogPostForm';
import { getBlogPostBySlug, createBlogPost, updateBlogPost } from '@/api/blog';
import { toast } from '@/components/ui/use-toast';
export const BlogEditPage = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { id } = useParams();
    const [isLoading, setIsLoading] = useState(!!id);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [post, setPost] = useState(null);
    // Fetch post data if in edit mode
    useEffect(() => {
        if (!id)
            return;
        const fetchPost = async () => {
            try {
                setIsLoading(true);
                const data = await getBlogPostBySlug(id);
                setPost(data);
            }
            catch (error) {
                console.error('Error fetching blog post:', error);
                toast({
                    title: t('error', 'Error'),
                    description: t('blog.errorFetchingPost', 'Failed to load the blog post. Please try again.'),
                    variant: 'destructive',
                });
                navigate('/admin/blog');
            }
            finally {
                setIsLoading(false);
            }
        };
        fetchPost();
    }, [id, navigate, t]);
    const handleSubmit = async (data) => {
        try {
            setIsSubmitting(true);
            if (id) {
                // Update existing post
                await updateBlogPost(id, data);
                toast({
                    title: t('success', 'Success'),
                    description: t('blog.postUpdated', 'Blog post has been updated successfully.'),
                });
            }
            else {
                // Create new post
                await createBlogPost({
                    ...data,
                    author: {
                        name: 'Admin', // This would come from auth context in a real app
                        avatar: '',
                    },
                    publishedAt: new Date().toISOString(),
                });
                toast({
                    title: t('success', 'Success'),
                    description: t('blog.postCreated', 'Blog post has been created successfully.'),
                });
            }
            // Redirect to blog list after successful submission
            navigate('/admin/blog');
        }
        catch (error) {
            console.error('Error saving blog post:', error);
            toast({
                title: t('error', 'Error'),
                description: t('blog.errorSavingPost', 'Failed to save the blog post. Please try again.'),
                variant: 'destructive',
            });
        }
        finally {
            setIsSubmitting(false);
        }
    };
    if (isLoading) {
        return (_jsxs("div", { className: "flex items-center justify-center h-64", children: [_jsx(Loader2, { className: "h-8 w-8 animate-spin text-primary" }), _jsx("span", { className: "ml-2", children: id
                        ? t('blog.loadingPost', 'Loading blog post...')
                        : t('blog.preparingEditor', 'Preparing editor...') })] }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs(Button, { variant: "ghost", onClick: () => navigate('/admin/blog'), disabled: isSubmitting, children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), t('common.back', 'Back')] }), _jsx("h1", { className: "text-2xl font-bold tracking-tight", children: id ? t('blog.editPost', 'Edit Post') : t('blog.newPost', 'New Post') }), _jsx("div", { children: _jsx(Button, { type: "submit", form: "blog-post-form", disabled: isSubmitting, children: isSubmitting ? (_jsxs(_Fragment, { children: [_jsx(Loader2, { className: "mr-2 h-4 w-4 animate-spin" }), t('common.saving', 'Saving...')] })) : (_jsxs(_Fragment, { children: [_jsx(Save, { className: "h-4 w-4 mr-2" }), t('common.save', 'Save')] })) }) })] }), _jsx("div", { className: "bg-white dark:bg-gray-800 rounded-lg border shadow-sm p-6", children: _jsx(BlogPostForm, { initialData: post || undefined, onSubmit: handleSubmit, isSubmitting: isSubmitting }) })] }));
};
export default BlogEditPage;
