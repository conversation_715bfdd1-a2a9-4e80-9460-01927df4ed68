import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getDashboardData } from '@/lib/api/dashboard';

// Define types for dashboard data
interface Activity {
  action: string;
  timestamp: string;
  user: string;
}

interface Booking {
  id: string;
  customer: string; // Just store the customer name as a string
  experience: string;
  date: string;
  time: string;
  guests: number;
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  amount: number;
}

// Separate interface for the API response booking format
interface ApiBooking {
  id: string;
  customer: {
    name: string;
    email: string;
    avatar: string;
  };
  experience: string;
  date: string;
  time: string;
  guests: number;
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  amount: number;
}

interface DashboardData {
  totalBookings: number;
  revenue: number;
  avgBookingValue: number;
  upcomingBookings: number;
  bookingTrend: number;
  revenueTrend: number;
  avgBookingValueTrend: number;
  revenueData: Array<{ date: string; revenue: number; bookings: number }>;
  recentBookings: Booking[];
  recentActivity: Activity[];
}

export function DashboardPage() {
  const { t } = useTranslation();
  
  // Define the API response type
  type ApiDashboardData = Omit<DashboardData, 'recentBookings'> & {
    recentBookings: ApiBooking[];
  };

  // Fetch dashboard data with proper typing and default values
  const { data: dashboardData, isLoading, error } = useQuery<DashboardData>({
    queryKey: ['dashboard'],
    queryFn: async (): Promise<DashboardData> => {
      const data = await getDashboardData() as unknown as ApiDashboardData;
      
      // Transform the API response to match our DashboardData interface
      return {
        totalBookings: data?.totalBookings || 0,
        revenue: data?.revenue || 0,
        avgBookingValue: data?.avgBookingValue || 0,
        upcomingBookings: data?.upcomingBookings || 0,
        bookingTrend: data?.bookingTrend || 0,
        revenueTrend: data?.revenueTrend || 0,
        avgBookingValueTrend: data?.avgBookingValueTrend || 0,
        revenueData: data?.revenueData || [],
        recentBookings: data?.recentBookings?.map((booking: ApiBooking): Booking => ({
          id: booking.id,
          customer: booking.customer.name, // Convert customer object to just the name
          experience: booking.experience,
          date: booking.date,
          time: booking.time,
          guests: booking.guests,
          status: booking.status,
          amount: booking.amount
        })) || [],
        recentActivity: data?.recentActivity || []
      };
    },
  });

  // Use default values for the dashboard data
  const safeData: DashboardData = {
    totalBookings: dashboardData?.totalBookings || 0,
    revenue: dashboardData?.revenue || 0,
    avgBookingValue: dashboardData?.avgBookingValue || 0,
    upcomingBookings: dashboardData?.upcomingBookings || 0,
    bookingTrend: dashboardData?.bookingTrend || 0,
    revenueTrend: dashboardData?.revenueTrend || 0,
    avgBookingValueTrend: dashboardData?.avgBookingValueTrend || 0,
    revenueData: dashboardData?.revenueData || [],
    recentBookings: dashboardData?.recentBookings || [],
    recentActivity: dashboardData?.recentActivity || []
  };

  // Handle error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="mx-auto h-12 w-12 text-red-500" />
          <h3 className="mt-2 text-lg font-medium text-gray-900">
            {t('error.loadingData', 'Error loading dashboard data')}
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {t('error.tryAgain', 'Please try again later.')}
          </p>
          <div className="mt-4">
            <Button onClick={() => window.location.reload()} variant="outline">
              {t('common.retry', 'Retry')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Handle loading state
  if (isLoading) {
    return (
      <div className="space-y-8 p-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i: number) => (
            <div key={i} className="h-[110px] rounded-lg bg-gray-200 animate-pulse" />
          ))}
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          <div className="col-span-4 h-[400px] rounded-lg bg-gray-200 animate-pulse" />
          <div className="col-span-3 h-[400px] rounded-lg bg-gray-200 animate-pulse" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            {t('admin.dashboard', 'Dashboard')}
          </h2>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[
          { 
            title: t('admin.totalBookings', 'Total Bookings'), 
            value: safeData.totalBookings.toString(),
            description: `+${safeData.bookingTrend.toFixed(1)}% from last month`
          },
          { 
            title: t('admin.revenue', 'Revenue'), 
            value: `$${safeData.revenue.toLocaleString()}`,
            description: `+${safeData.revenueTrend.toFixed(1)}% from last month`
          },
          { 
            title: t('admin.upcomingBookings', 'Upcoming Bookings'), 
            value: safeData.upcomingBookings.toString(),
            description: t('admin.scheduledNext30Days', 'Scheduled for next 30 days')
          },
          { 
            title: t('admin.avgBookingValue', 'Avg. Booking Value'), 
            value: `$${safeData.avgBookingValue.toFixed(2)}`,
            description: `+${safeData.avgBookingValueTrend.toFixed(1)}% from last month`
          }
        ].map((stat, index: number) => (
          <div key={index} className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
            <h3 className="text-sm font-medium leading-none">{stat.title}</h3>
            <p className="text-2xl font-bold mt-2">{stat.value}</p>
            <p className="text-xs text-muted-foreground mt-1">{stat.description}</p>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h2 className="text-lg font-semibold mb-4">
          {t('admin.recentActivity', 'Recent Activity')}
        </h2>
        {safeData.recentActivity.length > 0 ? (
          <div className="space-y-4">
            {safeData.recentActivity.map((activity: Activity, index: number) => (
              <div key={index} className="flex items-center justify-between border-b pb-2">
                <div>
                  <p className="font-medium">{activity.action}</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(activity.timestamp).toLocaleString()}
                  </p>
                </div>
                <span className="text-sm text-muted-foreground">
                  {activity.user}
                </span>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-muted-foreground">
            {t('admin.noRecentActivity', 'No recent activity')}
          </p>
        )}
      </div>
    </div>
  );
}

export default DashboardPage;
