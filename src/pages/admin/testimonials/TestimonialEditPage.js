import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Loader2, ArrowLeft, Save, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { TestimonialForm } from '@/components/testimonials/TestimonialForm';
import { getTestimonial, createTestimonial, updateTestimonial } from '@/api/testimonials';
import { toast } from '@/components/ui/use-toast';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
export const TestimonialEditPage = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { id } = useParams();
    const [isLoading, setIsLoading] = useState(!!id);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [testimonial, setTestimonial] = useState(null);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const isEditMode = !!id;
    // Fetch testimonial data if in edit mode
    useEffect(() => {
        if (!id) {
            setIsLoading(false);
            return;
        }
        const fetchTestimonial = async () => {
            try {
                const data = await getTestimonial(id);
                setTestimonial(data);
            }
            catch (error) {
                console.error('Error fetching testimonial:', error);
                toast({
                    title: t('error', 'Error'),
                    description: t('testimonials.errorFetchingSingle', 'Failed to load testimonial.'),
                    variant: 'destructive',
                });
                navigate('/admin/testimonials');
            }
            finally {
                setIsLoading(false);
            }
        };
        fetchTestimonial();
    }, [id, navigate, t]);
    // Handle form submission
    const handleSubmit = async (data) => {
        try {
            setIsSubmitting(true);
            if (isEditMode && id) {
                // Update existing testimonial
                await updateTestimonial(id, data);
                toast({
                    title: t('success', 'Success'),
                    description: t('testimonials.updated', 'Testimonial updated successfully.'),
                });
            }
            else {
                // Create new testimonial
                await createTestimonial(data);
                toast({
                    title: t('success', 'Success'),
                    description: t('testimonials.created', 'Testimonial created successfully.'),
                });
                navigate('/admin/testimonials');
            }
            // If we're editing, update the local state
            if (isEditMode && testimonial) {
                setTestimonial({
                    ...testimonial,
                    ...data,
                    author: {
                        ...testimonial.author,
                        name: data.authorName,
                        role: data.authorRole || '',
                        location: data.authorLocation || '',
                        avatar: data.authorAvatar || testimonial.author.avatar,
                    },
                });
            }
        }
        catch (error) {
            console.error('Error saving testimonial:', error);
            toast({
                title: t('error', 'Error'),
                description: t('testimonials.errorSaving', 'Failed to save testimonial. Please try again.'),
                variant: 'destructive',
            });
        }
        finally {
            setIsSubmitting(false);
        }
    };
    // Handle testimonial deletion
    const handleDelete = async () => {
        if (!id)
            return;
        try {
            setIsDeleting(true);
            // In a real app, you would call deleteTestimonial(id) here
            // For now, we'll just navigate back to the list
            await new Promise(resolve => setTimeout(resolve, 500));
            toast({
                title: t('success', 'Success'),
                description: t('testimonials.deleted', 'Testimonial has been deleted.'),
            });
            navigate('/admin/testimonials');
        }
        catch (error) {
            console.error('Error deleting testimonial:', error);
            toast({
                title: t('error', 'Error'),
                description: t('testimonials.errorDeleting', 'Failed to delete testimonial.'),
                variant: 'destructive',
            });
        }
        finally {
            setIsDeleting(false);
            setShowDeleteDialog(false);
        }
    };
    if (isLoading) {
        return (_jsxs("div", { className: "flex items-center justify-center h-64", children: [_jsx(Loader2, { className: "h-8 w-8 animate-spin text-primary" }), _jsx("span", { className: "ml-2", children: t('loading', 'Loading...') })] }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsxs(Button, { variant: "ghost", size: "sm", onClick: () => navigate('/admin/testimonials'), className: "mb-2", children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), t('back', 'Back')] }), _jsx("h1", { className: "text-2xl font-bold tracking-tight", children: isEditMode
                                    ? t('testimonials.editTestimonial', 'Edit Testimonial')
                                    : t('testimonials.addTestimonial', 'Add New Testimonial') }), _jsx("p", { className: "text-muted-foreground", children: isEditMode
                                    ? t('testimonials.editDescription', 'Update the testimonial details below.')
                                    : t('testimonials.addDescription', 'Fill in the details to add a new testimonial.') })] }), isEditMode && (_jsxs("div", { className: "flex space-x-2", children: [_jsxs(Button, { variant: "outline", onClick: () => setShowDeleteDialog(true), disabled: isDeleting, className: "text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700 dark:border-red-900 dark:text-red-400 dark:hover:bg-red-900/30", children: [isDeleting ? (_jsx(Loader2, { className: "h-4 w-4 mr-2 animate-spin" })) : (_jsx(Trash2, { className: "h-4 w-4 mr-2" })), t('delete', 'Delete')] }), _jsxs(Button, { onClick: () => document.getElementById('testimonial-form')?.requestSubmit(), disabled: isSubmitting, children: [isSubmitting ? (_jsx(Loader2, { className: "h-4 w-4 mr-2 animate-spin" })) : (_jsx(Save, { className: "h-4 w-4 mr-2" })), t('save', 'Save')] })] }))] }), _jsx("div", { className: "bg-white dark:bg-gray-800 rounded-lg border p-6", children: _jsx(TestimonialForm, { initialData: testimonial, onSubmit: handleSubmit, isSubmitting: isSubmitting, isAdmin: true }) }), _jsx(ConfirmDialog, { open: showDeleteDialog, onOpenChange: setShowDeleteDialog, title: t('testimonials.confirmDeleteTitle', 'Delete Testimonial'), description: t('testimonials.confirmDeleteMessage', 'Are you sure you want to delete this testimonial? This action cannot be undone.'), confirmText: t('delete', 'Delete'), cancelText: t('cancel', 'Cancel'), onConfirm: handleDelete, variant: "destructive", isLoading: isDeleting })] }));
};
export default TestimonialEditPage;
