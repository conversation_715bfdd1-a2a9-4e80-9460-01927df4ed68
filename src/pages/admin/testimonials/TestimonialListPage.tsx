import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Search, Filter, Plus, Loader2, Check, X, Star, StarHalf } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Testimonial, TestimonialFilters } from '@/types/testimonial';
import { getTestimonials, updateTestimonial, deleteTestimonial } from '@/api/testimonials';
import { toast } from '@/components/ui/use-toast';
import { format } from 'date-fns';

export const TestimonialListPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [filteredTestimonials, setFilteredTestimonials] = useState<Testimonial[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState<Record<string, boolean>>({});
  const [isDeleting, setIsDeleting] = useState<Record<string, boolean>>({});
  
  // Filters
  const [filters, setFilters] = useState<TestimonialFilters>({
    status: 'all',
    rating: 0,
    featured: undefined,
    search: '',
    sortBy: 'newest',
  });

  // Fetch testimonials
  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        setIsLoading(true);
        const data = await getTestimonials();
        setTestimonials(data);
        setFilteredTestimonials(data);
      } catch (error) {
        console.error('Error fetching testimonials:', error);
        toast({
          title: t('error', 'Error'),
          description: t('testimonials.errorFetching', 'Failed to load testimonials. Please try again.'),
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTestimonials();
  }, [t]);

  // Apply filters
  useEffect(() => {
    let result = [...testimonials];
    
    // Apply status filter
    if (filters.status && filters.status !== 'all') {
      result = result.filter(t => t.status === filters.status);
    }
    
    // Apply rating filter
    if (filters.rating && filters.rating > 0) {
      result = result.filter(t => t.rating === filters.rating);
    }
    
    // Apply featured filter
    if (filters.featured !== undefined) {
      result = result.filter(t => t.featured === filters.featured);
    }
    
    // Apply search
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      result = result.filter(
        t =>
          t.content.toLowerCase().includes(searchTerm) ||
          t.author.name.toLowerCase().includes(searchTerm) ||
          (t.author.location?.toLowerCase().includes(searchTerm) ?? false)
      );
    }
    
    // Apply sorting
    switch (filters.sortBy) {
      case 'newest':
        result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'oldest':
        result.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        break;
      case 'highest':
        result.sort((a, b) => b.rating - a.rating);
        break;
      case 'lowest':
        result.sort((a, b) => a.rating - b.rating);
        break;
    }
    
    setFilteredTestimonials(result);
  }, [testimonials, filters]);

  // Handle status update
  const handleStatusUpdate = async (id: string, status: 'approved' | 'pending' | 'rejected') => {
    try {
      setIsUpdating(prev => ({ ...prev, [id]: true }));
      
      await updateTestimonial(id, { status });
      
      // Update local state
      setTestimonials(prev =>
        prev.map(t =>
          t.id === id ? { ...t, status } : t
        )
      );
      
      toast({
        title: t('success', 'Success'),
        description: t('testimonials.statusUpdated', 'Testimonial status updated.'),
      });
    } catch (error) {
      console.error('Error updating testimonial status:', error);
      toast({
        title: t('error', 'Error'),
        description: t('testimonials.errorUpdating', 'Failed to update testimonial status.'),
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(prev => ({ ...prev, [id]: false }));
    }
  };

  // Handle featured toggle
  const toggleFeatured = async (id: string, currentValue: boolean) => {
    try {
      setIsUpdating(prev => ({ ...prev, [id]: true }));
      
      await updateTestimonial(id, { featured: !currentValue });
      
      // Update local state
      setTestimonials(prev =>
        prev.map(t =>
          t.id === id ? { ...t, featured: !currentValue } : t
        )
      );
      
      toast({
        title: t('success', 'Success'),
        description: currentValue
          ? t('testimonials.removedFromFeatured', 'Testimonial removed from featured.')
          : t('testimonials.addedToFeatured', 'Testimonial added to featured.'),
      });
    } catch (error) {
      console.error('Error toggling featured status:', error);
      toast({
        title: t('error', 'Error'),
        description: t('testimonials.errorUpdating', 'Failed to update testimonial status.'),
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(prev => ({ ...prev, [id]: false }));
    }
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    if (!window.confirm(t('testimonials.confirmDelete', 'Are you sure you want to delete this testimonial?'))) {
      return;
    }

    try {
      setIsDeleting(prev => ({ ...prev, [id]: true }));
      
      await deleteTestimonial(id);
      
      // Update local state
      setTestimonials(prev => prev.filter(t => t.id !== id));
      
      toast({
        title: t('success', 'Success'),
        description: t('testimonials.deleted', 'Testimonial has been deleted.'),
      });
    } catch (error) {
      console.error('Error deleting testimonial:', error);
      toast({
        title: t('error', 'Error'),
        description: t('testimonials.errorDeleting', 'Failed to delete testimonial.'),
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(prev => ({ ...prev, [id]: false }));
    }
  };

  // Render star rating
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 1; i <= 5; i++) {
      if (i <= fullStars) {
        stars.push(<Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />);
      } else if (i === fullStars + 1 && hasHalfStar) {
        stars.push(<StarHalf key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />);
      } else {
        stars.push(<Star key={i} className="h-4 w-4 text-gray-200 dark:text-gray-700" />);
      }
    }

    return stars;
  };

  // Render status badge
  const getStatusBadge = (status: string) => {
    const statusMap = {
      approved: {
        label: t('testimonials.status.approved', 'Approved'),
        className: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      },
      pending: {
        label: t('testimonials.status.pending', 'Pending'),
        className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      },
      rejected: {
        label: t('testimonials.status.rejected', 'Rejected'),
        className: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
      },
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || {
      label: status,
      className: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
    };

    return (
      <Badge className={statusInfo.className}>
        {statusInfo.label}
      </Badge>
    );
  };

  if (isLoading && testimonials.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">
          {t('testimonials.loading', 'Loading testimonials...')}
        </span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            {t('testimonials.title', 'Testimonials')}
          </h1>
          <p className="text-muted-foreground">
            {t('testimonials.manageTestimonials', 'Manage and moderate customer testimonials')}
          </p>
        </div>
        <Button onClick={() => navigate('/admin/testimonials/new')}>
          <Plus className="h-4 w-4 mr-2" />
          {t('testimonials.addTestimonial', 'Add Testimonial')}
        </Button>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border p-4 space-y-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                type="search"
                placeholder={t('testimonials.searchPlaceholder', 'Search testimonials...')}
                className="pl-9"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
            <Select
              value={filters.status}
              onValueChange={(value) => setFilters({ ...filters, status: value as any })}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t('testimonials.filterByStatus', 'Status')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('all', 'All')}</SelectItem>
                <SelectItem value="approved">{t('testimonials.status.approved', 'Approved')}</SelectItem>
                <SelectItem value="pending">{t('testimonials.status.pending', 'Pending')}</SelectItem>
                <SelectItem value="rejected">{t('testimonials.status.rejected', 'Rejected')}</SelectItem>
              </SelectContent>
            </Select>
            
            <Select
              value={filters.rating ? filters.rating.toString() : '0'}
              onValueChange={(value) => setFilters({ ...filters, rating: parseInt(value) })}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t('testimonials.filterByRating', 'Rating')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">{t('all', 'All')}</SelectItem>
                <SelectItem value="5">5 ★</SelectItem>
                <SelectItem value="4">4 ★ & up</SelectItem>
                <SelectItem value="3">3 ★ & up</SelectItem>
                <SelectItem value="2">2 ★ & up</SelectItem>
                <SelectItem value="1">1 ★ & up</SelectItem>
              </SelectContent>
            </Select>
            
            <Select
              value={filters.sortBy}
              onValueChange={(value) => setFilters({ ...filters, sortBy: value as any })}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t('testimonials.sortBy', 'Sort by')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">{t('testimonials.newest', 'Newest')}</SelectItem>
                <SelectItem value="oldest">{t('testimonials.oldest', 'Oldest')}</SelectItem>
                <SelectItem value="highest">{t('testimonials.highestRated', 'Highest Rated')}</SelectItem>
                <SelectItem value="lowest">{t('testimonials.lowestRated', 'Lowest Rated')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Button
            variant={filters.featured === true ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilters({
              ...filters,
              featured: filters.featured === true ? undefined : true,
            })}
          >
            <Star className="h-4 w-4 mr-1" />
            {t('testimonials.featured', 'Featured')}
          </Button>
          
          {(filters.status !== 'all' || filters.rating > 0 || filters.featured !== undefined) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFilters({
                status: 'all',
                rating: 0,
                featured: undefined,
                search: '',
                sortBy: 'newest',
              })}
            >
              {t('testimonials.clearFilters', 'Clear filters')}
            </Button>
          )}
        </div>
      </div>

      {/* Testimonials Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border shadow-sm">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('testimonials.author', 'Author')}</TableHead>
                <TableHead>{t('testimonials.testimonial', 'Testimonial')}</TableHead>
                <TableHead className="w-24">{t('testimonials.rating', 'Rating')}</TableHead>
                <TableHead className="w-32">{t('testimonials.status', 'Status')}</TableHead>
                <TableHead className="w-32">{t('testimonials.date', 'Date')}</TableHead>
                <TableHead className="w-24 text-right">{t('actions', 'Actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTestimonials.length > 0 ? (
                filteredTestimonials.map((testimonial) => (
                  <TableRow key={testimonial.id}>
                    <TableCell>
                      <div className="flex items-center">
                        <Avatar className="h-9 w-9">
                          <AvatarImage src={testimonial.author.avatar} alt={testimonial.author.name} />
                          <AvatarFallback>
                            {testimonial.author.name
                              .split(' ')
                              .map(n => n[0])
                              .join('')
                              .toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="ml-3">
                          <div className="font-medium">{testimonial.author.name}</div>
                          {testimonial.author.location && (
                            <div className="text-xs text-muted-foreground">
                              {testimonial.author.location}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="line-clamp-2 text-sm">
                        {testimonial.content}
                      </div>
                      {testimonial.source && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {t('testimonials.source', 'Source')}: {testimonial.source}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {renderStars(testimonial.rating)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(testimonial.status)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {format(new Date(testimonial.createdAt), 'MMM d, yyyy')}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {testimonial.testimonialDate && format(new Date(testimonial.testimonialDate), 'MMM d, yyyy')}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center justify-end space-x-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => navigate(`/admin/testimonials/edit/${testimonial.id}`)}
                          title={t('edit', 'Edit')}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                            <path d="M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z" />
                          </svg>
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => toggleFeatured(testimonial.id, testimonial.featured || false)}
                          disabled={isUpdating[testimonial.id]}
                          title={testimonial.featured ? t('testimonials.removeFeatured', 'Remove featured') : t('testimonials.markFeatured', 'Mark as featured')}
                        >
                          {isUpdating[testimonial.id] ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : testimonial.featured ? (
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          ) : (
                            <Star className="h-4 w-4 text-muted-foreground" />
                          )}
                        </Button>
                        
                        {testimonial.status !== 'approved' && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleStatusUpdate(testimonial.id, 'approved')}
                            disabled={isUpdating[testimonial.id]}
                            title={t('testimonials.approve', 'Approve')}
                          >
                            {isUpdating[testimonial.id] ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Check className="h-4 w-4 text-green-600" />
                            )}
                          </Button>
                        )}
                        
                        {testimonial.status !== 'rejected' && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleStatusUpdate(testimonial.id, 'rejected')}
                            disabled={isUpdating[testimonial.id]}
                            title={t('testimonials.reject', 'Reject')}
                          >
                            {isUpdating[testimonial.id] ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <X className="h-4 w-4 text-red-600" />
                            )}
                          </Button>
                        )}
                        
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDelete(testimonial.id)}
                          disabled={isDeleting[testimonial.id]}
                          className="text-red-600 hover:text-red-700"
                          title={t('delete', 'Delete')}
                        >
                          {isDeleting[testimonial.id] ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M3 6h18" />
                              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                            </svg>
                          )}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                    {filters.search || filters.status !== 'all' || filters.rating > 0 || filters.featured !== undefined
                      ? t('testimonials.noMatchingTestimonials', 'No testimonials match your filters.')
                      : t('testimonials.noTestimonials', 'No testimonials found.')}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default TestimonialListPage;
