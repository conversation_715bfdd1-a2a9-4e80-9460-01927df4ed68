import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Search, Plus, Loader2, Check, X, Star, StarHalf } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { getTestimonials, updateTestimonial, deleteTestimonial } from '@/api/testimonials';
import { toast } from '@/components/ui/use-toast';
import { format } from 'date-fns';
export const TestimonialListPage = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [testimonials, setTestimonials] = useState([]);
    const [filteredTestimonials, setFilteredTestimonials] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isUpdating, setIsUpdating] = useState({});
    const [isDeleting, setIsDeleting] = useState({});
    // Filters
    const [filters, setFilters] = useState({
        status: 'all',
        rating: 0,
        featured: undefined,
        search: '',
        sortBy: 'newest',
    });
    // Fetch testimonials
    useEffect(() => {
        const fetchTestimonials = async () => {
            try {
                setIsLoading(true);
                const data = await getTestimonials();
                setTestimonials(data);
                setFilteredTestimonials(data);
            }
            catch (error) {
                console.error('Error fetching testimonials:', error);
                toast({
                    title: t('error', 'Error'),
                    description: t('testimonials.errorFetching', 'Failed to load testimonials. Please try again.'),
                    variant: 'destructive',
                });
            }
            finally {
                setIsLoading(false);
            }
        };
        fetchTestimonials();
    }, [t]);
    // Apply filters
    useEffect(() => {
        let result = [...testimonials];
        // Apply status filter
        if (filters.status && filters.status !== 'all') {
            result = result.filter(t => t.status === filters.status);
        }
        // Apply rating filter
        if (filters.rating && filters.rating > 0) {
            result = result.filter(t => t.rating === filters.rating);
        }
        // Apply featured filter
        if (filters.featured !== undefined) {
            result = result.filter(t => t.featured === filters.featured);
        }
        // Apply search
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            result = result.filter(t => t.content.toLowerCase().includes(searchTerm) ||
                t.author.name.toLowerCase().includes(searchTerm) ||
                (t.author.location?.toLowerCase().includes(searchTerm) ?? false));
        }
        // Apply sorting
        switch (filters.sortBy) {
            case 'newest':
                result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
                break;
            case 'oldest':
                result.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
                break;
            case 'highest':
                result.sort((a, b) => b.rating - a.rating);
                break;
            case 'lowest':
                result.sort((a, b) => a.rating - b.rating);
                break;
        }
        setFilteredTestimonials(result);
    }, [testimonials, filters]);
    // Handle status update
    const handleStatusUpdate = async (id, status) => {
        try {
            setIsUpdating(prev => ({ ...prev, [id]: true }));
            await updateTestimonial(id, { status });
            // Update local state
            setTestimonials(prev => prev.map(t => t.id === id ? { ...t, status } : t));
            toast({
                title: t('success', 'Success'),
                description: t('testimonials.statusUpdated', 'Testimonial status updated.'),
            });
        }
        catch (error) {
            console.error('Error updating testimonial status:', error);
            toast({
                title: t('error', 'Error'),
                description: t('testimonials.errorUpdating', 'Failed to update testimonial status.'),
                variant: 'destructive',
            });
        }
        finally {
            setIsUpdating(prev => ({ ...prev, [id]: false }));
        }
    };
    // Handle featured toggle
    const toggleFeatured = async (id, currentValue) => {
        try {
            setIsUpdating(prev => ({ ...prev, [id]: true }));
            await updateTestimonial(id, { featured: !currentValue });
            // Update local state
            setTestimonials(prev => prev.map(t => t.id === id ? { ...t, featured: !currentValue } : t));
            toast({
                title: t('success', 'Success'),
                description: currentValue
                    ? t('testimonials.removedFromFeatured', 'Testimonial removed from featured.')
                    : t('testimonials.addedToFeatured', 'Testimonial added to featured.'),
            });
        }
        catch (error) {
            console.error('Error toggling featured status:', error);
            toast({
                title: t('error', 'Error'),
                description: t('testimonials.errorUpdating', 'Failed to update testimonial status.'),
                variant: 'destructive',
            });
        }
        finally {
            setIsUpdating(prev => ({ ...prev, [id]: false }));
        }
    };
    // Handle delete
    const handleDelete = async (id) => {
        if (!window.confirm(t('testimonials.confirmDelete', 'Are you sure you want to delete this testimonial?'))) {
            return;
        }
        try {
            setIsDeleting(prev => ({ ...prev, [id]: true }));
            await deleteTestimonial(id);
            // Update local state
            setTestimonials(prev => prev.filter(t => t.id !== id));
            toast({
                title: t('success', 'Success'),
                description: t('testimonials.deleted', 'Testimonial has been deleted.'),
            });
        }
        catch (error) {
            console.error('Error deleting testimonial:', error);
            toast({
                title: t('error', 'Error'),
                description: t('testimonials.errorDeleting', 'Failed to delete testimonial.'),
                variant: 'destructive',
            });
        }
        finally {
            setIsDeleting(prev => ({ ...prev, [id]: false }));
        }
    };
    // Render star rating
    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        for (let i = 1; i <= 5; i++) {
            if (i <= fullStars) {
                stars.push(_jsx(Star, { className: "h-4 w-4 fill-yellow-400 text-yellow-400" }, i));
            }
            else if (i === fullStars + 1 && hasHalfStar) {
                stars.push(_jsx(StarHalf, { className: "h-4 w-4 fill-yellow-400 text-yellow-400" }, i));
            }
            else {
                stars.push(_jsx(Star, { className: "h-4 w-4 text-gray-200 dark:text-gray-700" }, i));
            }
        }
        return stars;
    };
    // Render status badge
    const getStatusBadge = (status) => {
        const statusMap = {
            approved: {
                label: t('testimonials.status.approved', 'Approved'),
                className: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
            },
            pending: {
                label: t('testimonials.status.pending', 'Pending'),
                className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
            },
            rejected: {
                label: t('testimonials.status.rejected', 'Rejected'),
                className: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
            },
        };
        const statusInfo = statusMap[status] || {
            label: status,
            className: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
        };
        return (_jsx(Badge, { className: statusInfo.className, children: statusInfo.label }));
    };
    if (isLoading && testimonials.length === 0) {
        return (_jsxs("div", { className: "flex items-center justify-center h-64", children: [_jsx(Loader2, { className: "h-8 w-8 animate-spin text-primary" }), _jsx("span", { className: "ml-2", children: t('testimonials.loading', 'Loading testimonials...') })] }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-2xl font-bold tracking-tight", children: t('testimonials.title', 'Testimonials') }), _jsx("p", { className: "text-muted-foreground", children: t('testimonials.manageTestimonials', 'Manage and moderate customer testimonials') })] }), _jsxs(Button, { onClick: () => navigate('/admin/testimonials/new'), children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), t('testimonials.addTestimonial', 'Add Testimonial')] })] }), _jsxs("div", { className: "bg-white dark:bg-gray-800 rounded-lg border p-4 space-y-4", children: [_jsxs("div", { className: "flex flex-col md:flex-row gap-4", children: [_jsx("div", { className: "flex-1", children: _jsxs("div", { className: "relative", children: [_jsx(Search, { className: "absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" }), _jsx(Input, { type: "search", placeholder: t('testimonials.searchPlaceholder', 'Search testimonials...'), className: "pl-9", value: filters.search, onChange: (e) => setFilters({ ...filters, search: e.target.value }) })] }) }), _jsxs("div", { className: "grid grid-cols-2 sm:grid-cols-3 gap-2", children: [_jsxs(Select, { value: filters.status, onValueChange: (value) => setFilters({ ...filters, status: value }), children: [_jsx(SelectTrigger, { className: "w-full", children: _jsx(SelectValue, { placeholder: t('testimonials.filterByStatus', 'Status') }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "all", children: t('all', 'All') }), _jsx(SelectItem, { value: "approved", children: t('testimonials.status.approved', 'Approved') }), _jsx(SelectItem, { value: "pending", children: t('testimonials.status.pending', 'Pending') }), _jsx(SelectItem, { value: "rejected", children: t('testimonials.status.rejected', 'Rejected') })] })] }), _jsxs(Select, { value: filters.rating ? filters.rating.toString() : '0', onValueChange: (value) => setFilters({ ...filters, rating: parseInt(value) }), children: [_jsx(SelectTrigger, { className: "w-full", children: _jsx(SelectValue, { placeholder: t('testimonials.filterByRating', 'Rating') }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "0", children: t('all', 'All') }), _jsx(SelectItem, { value: "5", children: "5 \u2605" }), _jsx(SelectItem, { value: "4", children: "4 \u2605 & up" }), _jsx(SelectItem, { value: "3", children: "3 \u2605 & up" }), _jsx(SelectItem, { value: "2", children: "2 \u2605 & up" }), _jsx(SelectItem, { value: "1", children: "1 \u2605 & up" })] })] }), _jsxs(Select, { value: filters.sortBy, onValueChange: (value) => setFilters({ ...filters, sortBy: value }), children: [_jsx(SelectTrigger, { className: "w-full", children: _jsx(SelectValue, { placeholder: t('testimonials.sortBy', 'Sort by') }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "newest", children: t('testimonials.newest', 'Newest') }), _jsx(SelectItem, { value: "oldest", children: t('testimonials.oldest', 'Oldest') }), _jsx(SelectItem, { value: "highest", children: t('testimonials.highestRated', 'Highest Rated') }), _jsx(SelectItem, { value: "lowest", children: t('testimonials.lowestRated', 'Lowest Rated') })] })] })] })] }), _jsxs("div", { className: "flex flex-wrap gap-2", children: [_jsxs(Button, { variant: filters.featured === true ? 'default' : 'outline', size: "sm", onClick: () => setFilters({
                                    ...filters,
                                    featured: filters.featured === true ? undefined : true,
                                }), children: [_jsx(Star, { className: "h-4 w-4 mr-1" }), t('testimonials.featured', 'Featured')] }), (filters.status !== 'all' || filters.rating > 0 || filters.featured !== undefined) && (_jsx(Button, { variant: "ghost", size: "sm", onClick: () => setFilters({
                                    status: 'all',
                                    rating: 0,
                                    featured: undefined,
                                    search: '',
                                    sortBy: 'newest',
                                }), children: t('testimonials.clearFilters', 'Clear filters') }))] })] }), _jsx("div", { className: "bg-white dark:bg-gray-800 rounded-lg border shadow-sm", children: _jsx("div", { className: "overflow-x-auto", children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: t('testimonials.author', 'Author') }), _jsx(TableHead, { children: t('testimonials.testimonial', 'Testimonial') }), _jsx(TableHead, { className: "w-24", children: t('testimonials.rating', 'Rating') }), _jsx(TableHead, { className: "w-32", children: t('testimonials.status', 'Status') }), _jsx(TableHead, { className: "w-32", children: t('testimonials.date', 'Date') }), _jsx(TableHead, { className: "w-24 text-right", children: t('actions', 'Actions') })] }) }), _jsx(TableBody, { children: filteredTestimonials.length > 0 ? (filteredTestimonials.map((testimonial) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: _jsxs("div", { className: "flex items-center", children: [_jsxs(Avatar, { className: "h-9 w-9", children: [_jsx(AvatarImage, { src: testimonial.author.avatar, alt: testimonial.author.name }), _jsx(AvatarFallback, { children: testimonial.author.name
                                                                    .split(' ')
                                                                    .map(n => n[0])
                                                                    .join('')
                                                                    .toUpperCase() })] }), _jsxs("div", { className: "ml-3", children: [_jsx("div", { className: "font-medium", children: testimonial.author.name }), testimonial.author.location && (_jsx("div", { className: "text-xs text-muted-foreground", children: testimonial.author.location }))] })] }) }), _jsxs(TableCell, { children: [_jsx("div", { className: "line-clamp-2 text-sm", children: testimonial.content }), testimonial.source && (_jsxs("div", { className: "text-xs text-muted-foreground mt-1", children: [t('testimonials.source', 'Source'), ": ", testimonial.source] }))] }), _jsx(TableCell, { children: _jsx("div", { className: "flex items-center", children: renderStars(testimonial.rating) }) }), _jsx(TableCell, { children: getStatusBadge(testimonial.status) }), _jsxs(TableCell, { children: [_jsx("div", { className: "text-sm", children: format(new Date(testimonial.createdAt), 'MMM d, yyyy') }), _jsx("div", { className: "text-xs text-muted-foreground", children: testimonial.testimonialDate && format(new Date(testimonial.testimonialDate), 'MMM d, yyyy') })] }), _jsx(TableCell, { children: _jsxs("div", { className: "flex items-center justify-end space-x-1", children: [_jsx(Button, { variant: "ghost", size: "icon", onClick: () => navigate(`/admin/testimonials/edit/${testimonial.id}`), title: t('edit', 'Edit'), children: _jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "16", height: "16", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", children: [_jsx("path", { d: "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" }), _jsx("path", { d: "M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z" })] }) }), _jsx(Button, { variant: "ghost", size: "icon", onClick: () => toggleFeatured(testimonial.id, testimonial.featured || false), disabled: isUpdating[testimonial.id], title: testimonial.featured ? t('testimonials.removeFeatured', 'Remove featured') : t('testimonials.markFeatured', 'Mark as featured'), children: isUpdating[testimonial.id] ? (_jsx(Loader2, { className: "h-4 w-4 animate-spin" })) : testimonial.featured ? (_jsx(Star, { className: "h-4 w-4 fill-yellow-400 text-yellow-400" })) : (_jsx(Star, { className: "h-4 w-4 text-muted-foreground" })) }), testimonial.status !== 'approved' && (_jsx(Button, { variant: "ghost", size: "icon", onClick: () => handleStatusUpdate(testimonial.id, 'approved'), disabled: isUpdating[testimonial.id], title: t('testimonials.approve', 'Approve'), children: isUpdating[testimonial.id] ? (_jsx(Loader2, { className: "h-4 w-4 animate-spin" })) : (_jsx(Check, { className: "h-4 w-4 text-green-600" })) })), testimonial.status !== 'rejected' && (_jsx(Button, { variant: "ghost", size: "icon", onClick: () => handleStatusUpdate(testimonial.id, 'rejected'), disabled: isUpdating[testimonial.id], title: t('testimonials.reject', 'Reject'), children: isUpdating[testimonial.id] ? (_jsx(Loader2, { className: "h-4 w-4 animate-spin" })) : (_jsx(X, { className: "h-4 w-4 text-red-600" })) })), _jsx(Button, { variant: "ghost", size: "icon", onClick: () => handleDelete(testimonial.id), disabled: isDeleting[testimonial.id], className: "text-red-600 hover:text-red-700", title: t('delete', 'Delete'), children: isDeleting[testimonial.id] ? (_jsx(Loader2, { className: "h-4 w-4 animate-spin" })) : (_jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "16", height: "16", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", children: [_jsx("path", { d: "M3 6h18" }), _jsx("path", { d: "M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" }), _jsx("path", { d: "M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" })] })) })] }) })] }, testimonial.id)))) : (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: 6, className: "text-center py-8 text-muted-foreground", children: filters.search || filters.status !== 'all' || filters.rating > 0 || filters.featured !== undefined
                                            ? t('testimonials.noMatchingTestimonials', 'No testimonials match your filters.')
                                            : t('testimonials.noTestimonials', 'No testimonials found.') }) })) })] }) }) })] }));
};
export default TestimonialListPage;
