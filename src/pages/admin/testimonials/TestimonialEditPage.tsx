import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Loader2, ArrowLeft, Save, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { TestimonialForm } from '@/components/testimonials/TestimonialForm';
import { getTestimonial, createTestimonial, updateTestimonial } from '@/api/testimonials';
import { Testimonial, TestimonialFormData } from '@/types/testimonial';
import { toast } from '@/components/ui/use-toast';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';

export const TestimonialEditPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id?: string }>();
  
  const [isLoading, setIsLoading] = useState(!!id);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [testimonial, setTestimonial] = useState<Testimonial | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const isEditMode = !!id;

  // Fetch testimonial data if in edit mode
  useEffect(() => {
    if (!id) {
      setIsLoading(false);
      return;
    }

    const fetchTestimonial = async () => {
      try {
        const data = await getTestimonial(id);
        setTestimonial(data);
      } catch (error) {
        console.error('Error fetching testimonial:', error);
        toast({
          title: t('error', 'Error'),
          description: t('testimonials.errorFetchingSingle', 'Failed to load testimonial.'),
          variant: 'destructive',
        });
        navigate('/admin/testimonials');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTestimonial();
  }, [id, navigate, t]);

  // Handle form submission
  const handleSubmit = async (data: TestimonialFormData) => {
    try {
      setIsSubmitting(true);
      
      if (isEditMode && id) {
        // Update existing testimonial
        await updateTestimonial(id, data);
        toast({
          title: t('success', 'Success'),
          description: t('testimonials.updated', 'Testimonial updated successfully.'),
        });
      } else {
        // Create new testimonial
        await createTestimonial(data);
        toast({
          title: t('success', 'Success'),
          description: t('testimonials.created', 'Testimonial created successfully.'),
        });
        navigate('/admin/testimonials');
      }
      
      // If we're editing, update the local state
      if (isEditMode && testimonial) {
        setTestimonial({
          ...testimonial,
          ...data,
          author: {
            ...testimonial.author,
            name: data.authorName,
            role: data.authorRole || '',
            location: data.authorLocation || '',
            avatar: data.authorAvatar || testimonial.author.avatar,
          },
        });
      }
      
    } catch (error) {
      console.error('Error saving testimonial:', error);
      toast({
        title: t('error', 'Error'),
        description: t('testimonials.errorSaving', 'Failed to save testimonial. Please try again.'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle testimonial deletion
  const handleDelete = async () => {
    if (!id) return;
    
    try {
      setIsDeleting(true);
      
      // In a real app, you would call deleteTestimonial(id) here
      // For now, we'll just navigate back to the list
      await new Promise(resolve => setTimeout(resolve, 500));
      
      toast({
        title: t('success', 'Success'),
        description: t('testimonials.deleted', 'Testimonial has been deleted.'),
      });
      
      navigate('/admin/testimonials');
    } catch (error) {
      console.error('Error deleting testimonial:', error);
      toast({
        title: t('error', 'Error'),
        description: t('testimonials.errorDeleting', 'Failed to delete testimonial.'),
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">
          {t('loading', 'Loading...')}
        </span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/admin/testimonials')}
            className="mb-2"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('back', 'Back')}
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">
            {isEditMode 
              ? t('testimonials.editTestimonial', 'Edit Testimonial')
              : t('testimonials.addTestimonial', 'Add New Testimonial')}
          </h1>
          <p className="text-muted-foreground">
            {isEditMode
              ? t('testimonials.editDescription', 'Update the testimonial details below.')
              : t('testimonials.addDescription', 'Fill in the details to add a new testimonial.')}
          </p>
        </div>
        
        {isEditMode && (
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(true)}
              disabled={isDeleting}
              className="text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700 dark:border-red-900 dark:text-red-400 dark:hover:bg-red-900/30"
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              {t('delete', 'Delete')}
            </Button>
            
            <Button onClick={() => document.getElementById('testimonial-form')?.requestSubmit()} disabled={isSubmitting}>
              {isSubmitting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {t('save', 'Save')}
            </Button>
          </div>
        )}
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg border p-6">
        <TestimonialForm
          initialData={testimonial}
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
          isAdmin={true}
        />
      </div>
      
      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title={t('testimonials.confirmDeleteTitle', 'Delete Testimonial')}
        description={t('testimonials.confirmDeleteMessage', 'Are you sure you want to delete this testimonial? This action cannot be undone.')}
        confirmText={t('delete', 'Delete')}
        cancelText={t('cancel', 'Cancel')}
        onConfirm={handleDelete}
        variant="destructive"
        isLoading={isDeleting}
      />
    </div>
  );
};

export default TestimonialEditPage;
