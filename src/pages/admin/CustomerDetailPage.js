import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { ArrowLeft, Calendar, Edit, Mail, Phone, User } from 'lucide-react';
import { getCustomerById, updateCustomer } from '@/lib/api/customers';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// Avatar components are not currently used in this file
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatCurrency } from '@/lib/utils';
export function CustomerDetailPage() {
    const { id } = useParams();
    const navigate = useNavigate();
    const { t } = useTranslation();
    const [customer, setCustomer] = useState(null);
    const [loading, setLoading] = useState(true);
    // Active tab state is kept for potential future use
    const [, setActiveTab] = useState('overview');
    const [isEditing, setIsEditing] = useState(false);
    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        avatar: '',
        notes: '',
        tags: [],
        address: {},
        preferences: { communication: { email: false, sms: false } },
        metadata: {}
    });
    // Fetch customer data
    useEffect(() => {
        const fetchCustomer = async () => {
            if (!id)
                return;
            try {
                setLoading(true);
                const data = await getCustomerById(id);
                if (data) {
                    setCustomer(data);
                    // Only set the form fields that we allow editing
                    setFormData({
                        firstName: data.firstName || '',
                        lastName: data.lastName || '',
                        email: data.email || '',
                        phone: data.phone || '',
                        avatar: data.avatar || '',
                        notes: data.notes || '',
                        tags: data.tags || [],
                        address: data.address || {},
                        preferences: data.preferences || { communication: { email: false, sms: false } },
                        metadata: data.metadata || {}
                    });
                }
                else {
                    navigate('/admin/customers');
                }
            }
            catch (error) {
                console.error('Error fetching customer:', error);
                if (error instanceof Error) {
                    console.error(error.message);
                }
                // Show error to user if needed
            }
            finally {
                setLoading(false);
            }
        };
        fetchCustomer();
    }, [id, navigate]);
    const handleInputChange = (e) => {
        const { name, value, type } = e.target;
        // Handle different input types
        if (type === 'checkbox') {
            const checked = e.target.checked;
            // Handle nested preferences.communication fields
            if (name.startsWith('preferences.communication.')) {
                const field = name.split('.')[2];
                setFormData(prev => ({
                    ...prev,
                    preferences: {
                        ...prev.preferences,
                        communication: {
                            ...(prev.preferences?.communication || { email: false, sms: false }),
                            [field]: checked
                        }
                    }
                }));
            }
            else {
                setFormData(prev => ({ ...prev, [name]: checked }));
            }
        }
        else {
            // Handle text inputs
            setFormData(prev => ({ ...prev, [name]: value }));
        }
    };
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!id || !customer)
            return;
        try {
            // Only send the fields that can be updated
            const updateData = {
                firstName: formData.firstName,
                lastName: formData.lastName,
                email: formData.email,
                phone: formData.phone,
                avatar: formData.avatar,
                notes: formData.notes,
                tags: formData.tags,
                address: formData.address,
                preferences: formData.preferences,
                metadata: formData.metadata
            };
            const updatedCustomer = await updateCustomer(id, updateData);
            if (updatedCustomer) {
                setCustomer(updatedCustomer);
                setIsEditing(false);
            }
        }
        catch (error) {
            console.error('Error updating customer:', error);
            // Show error to user if needed
        }
    };
    if (loading) {
        return _jsx("div", { children: "Loading..." });
    }
    if (!customer) {
        return (_jsxs("div", { className: "text-center py-12", children: [_jsx("h3", { className: "text-lg font-medium", children: t('customers.customerNotFound', 'Customer not found') }), _jsx("div", { className: "mt-6", children: _jsxs(Button, { onClick: () => navigate('/admin/customers'), children: [_jsx(ArrowLeft, { className: "mr-2 h-4 w-4" }), t('customers.backToCustomers', 'Back to Customers')] }) })] }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex items-center space-x-4", children: [_jsx(Button, { variant: "outline", size: "icon", onClick: () => navigate('/admin/customers'), children: _jsx(ArrowLeft, { className: "h-4 w-4" }) }), _jsx("h2", { className: "text-2xl font-bold", children: isEditing ? (_jsx("input", { type: "text", name: "firstName", value: formData.firstName || '', onChange: handleInputChange, className: "bg-transparent border-b border-muted-foreground" })) : (`${customer.firstName} ${customer.lastName}`) }), _jsx("div", { className: "ml-auto flex items-center space-x-2", children: !isEditing ? (_jsxs(Button, { variant: "outline", onClick: () => setIsEditing(true), children: [_jsx(Edit, { className: "mr-2 h-4 w-4" }), t('common.edit', 'Edit')] })) : (_jsxs(_Fragment, { children: [_jsx(Button, { variant: "outline", onClick: () => {
                                        setFormData(customer);
                                        setIsEditing(false);
                                    }, children: t('common.cancel', 'Cancel') }), _jsx(Button, { onClick: handleSubmit, children: t('common.saveChanges', 'Save Changes') })] })) })] }), _jsxs(Tabs, { defaultValue: "overview", onValueChange: setActiveTab, children: [_jsxs(TabsList, { children: [_jsxs(TabsTrigger, { value: "overview", children: [_jsx(User, { className: "mr-2 h-4 w-4" }), t('common.overview', 'Overview')] }), _jsxs(TabsTrigger, { value: "bookings", children: [_jsx(Calendar, { className: "mr-2 h-4 w-4" }), t('bookings.title', 'Bookings')] })] }), _jsxs(TabsContent, { value: "overview", className: "space-y-4", children: [_jsxs("div", { className: "grid gap-4 md:grid-cols-3", children: [_jsx(Card, { children: _jsxs(CardHeader, { children: [_jsx("div", { className: "text-sm font-medium", children: t('customers.customerSince', 'Customer Since') }), _jsx("div", { className: "text-2xl font-bold", children: format(new Date(customer.joinDate), 'MMM yyyy') })] }) }), _jsx(Card, { children: _jsxs(CardHeader, { children: [_jsx("div", { className: "text-sm font-medium", children: t('bookings.totalBookings', 'Total Bookings') }), _jsx("div", { className: "text-2xl font-bold", children: customer.totalBookings })] }) }), _jsx(Card, { children: _jsxs(CardHeader, { children: [_jsx("div", { className: "text-sm font-medium", children: t('customers.totalSpent', 'Total Spent') }), _jsx("div", { className: "text-2xl font-bold", children: formatCurrency(customer.totalSpent) })] }) })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: t('common.contactInformation', 'Contact Information') }) }), _jsx(CardContent, { className: "space-y-4", children: _jsxs("div", { className: "grid gap-4 md:grid-cols-2", children: [_jsxs("div", { className: "space-y-2", children: [_jsx("label", { className: "text-sm font-medium", children: t('common.email', 'Email') }), isEditing ? (_jsx("input", { type: "email", name: "email", value: formData.email || '', onChange: handleInputChange, className: "w-full p-2 border rounded" })) : (_jsxs("div", { className: "flex items-center", children: [_jsx(Mail, { className: "mr-2 h-4 w-4" }), customer.email] }))] }), _jsxs("div", { className: "space-y-2", children: [_jsx("label", { className: "text-sm font-medium", children: t('common.phone', 'Phone') }), isEditing ? (_jsx("input", { type: "tel", name: "phone", value: formData.phone || '', onChange: handleInputChange, className: "w-full p-2 border rounded" })) : (_jsxs("div", { className: "flex items-center", children: [_jsx(Phone, { className: "mr-2 h-4 w-4" }), customer.phone || t('common.notProvided', 'Not provided')] }))] })] }) })] })] }), _jsx(TabsContent, { value: "bookings", children: _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: t('bookings.bookingHistory', 'Booking History') }) }), _jsx(CardContent, { children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: t('bookings.bookingId', 'ID') }), _jsx(TableHead, { children: t('common.date', 'Date') }), _jsx(TableHead, { children: t('common.experience', 'Experience') }), _jsx(TableHead, { className: "text-right", children: t('common.total', 'Total') })] }) }), _jsx(TableBody, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { children: "B001" }), _jsx(TableCell, { children: format(new Date(), 'MMM d, yyyy') }), _jsx(TableCell, { children: "Sunset Sail" }), _jsx(TableCell, { className: "text-right", children: "$198.00" })] }) })] }) })] }) })] })] }));
}
export default CustomerDetailPage;
