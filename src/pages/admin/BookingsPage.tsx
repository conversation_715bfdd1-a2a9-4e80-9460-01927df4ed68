import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery, QueryFunction, UseQueryResult } from '@tanstack/react-query';
import { format, parseISO } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { MoreHorizontal, Search, Calendar as CalendarIcon, Filter } from 'lucide-react';
import { getBookings } from '@/lib/api/bookings';
import { Booking, BookingStatus } from '@/types/booking';
import { formatCurrency } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';

// Define status variant mapping with proper type safety
const statusVariant: Record<BookingStatus, string> = {
  confirmed: 'bg-green-100 text-green-800',
  pending: 'bg-yellow-100 text-yellow-800',
  cancelled: 'bg-red-100 text-red-800',
  completed: 'bg-blue-100 text-blue-800',
} as const;

export function BookingsPage() {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<BookingStatus | 'all'>('all');
  const [selectedBookings, setSelectedBookings] = useState<string[]>([]);

  // Define query function with proper type
  const fetchBookings: QueryFunction<Booking[], readonly unknown[]> = async () => {
    const result = await getBookings({});
    return Array.isArray(result) ? result : [];
  };

  // Fetch bookings data with proper type
  const { data: bookings = [], isLoading }: UseQueryResult<Booking[], Error> = useQuery({
    queryKey: ['bookings'],
    queryFn: fetchBookings,
    initialData: []
  });

  // Filter and search bookings with proper type safety
  const filteredBookings: Booking[] = useMemo(() => {
    return bookings.filter((booking: Booking) => {
      const searchLower = searchQuery.toLowerCase();
      const matchesSearch =
        (booking.customer?.name?.toLowerCase().includes(searchLower) ?? false) ||
        (booking.experience?.toLowerCase().includes(searchLower) ?? false) ||
        (booking.id?.toLowerCase().includes(searchLower) ?? false);
      
      const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    });
  }, [bookings, searchQuery, statusFilter]);

  // Toggle selection for a single booking
  const toggleBookingSelection = (id: string) => {
    setSelectedBookings((prev) =>
      prev.includes(id)
        ? prev.filter((bookingId) => bookingId !== id)
        : [...prev, id]
    );
  };

  // Toggle selection for all bookings on current page
  const toggleSelectAllBookings = () => {
    if (selectedBookings.length === filteredBookings.length) {
      setSelectedBookings([]);
    } else {
      setSelectedBookings(filteredBookings.map((booking: Booking) => booking.id));
    }
  };

  // Handle bulk actions
  const handleBulkAction = (action: 'confirm' | 'cancel' | 'delete') => {
    // In a real app, this would make an API call to update the bookings
    console.log(`Performing ${action} on:`, selectedBookings);
    // Reset selection after action
    setSelectedBookings([]);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between space-y-4 sm:flex-row sm:items-center sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            {t('bookings.title', 'Bookings')}
          </h2>
          <p className="text-muted-foreground">
            {t('bookings.subtitle', 'Manage and track all your bookings')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button>
            <CalendarIcon className="mr-2 h-4 w-4" />
            {t('bookings.newBooking', 'New Booking')}
          </Button>
        </div>
      </div>

      {/* Filters and search */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div className="relative w-full md:max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder={t('bookings.searchPlaceholder', 'Search bookings...')}
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="flex items-center space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                {statusFilter === 'all' 
                  ? t('bookings.filterByStatus', 'Filter by status')
                  : t(`booking.status.${statusFilter}`, statusFilter)}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                {t('common.all', 'All')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {Object.entries(statusVariant).map(([status, _]) => (
                <DropdownMenuItem 
                  key={status} 
                  onClick={() => setStatusFilter(status as BookingStatus)}
                >
                  {t(`booking.status.${status}`, status)}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {selectedBookings.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  {t('bookings.bulkActions', 'Bulk Actions')}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>
                  {t('bookings.selectedItems', { count: selectedBookings.length })}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleBulkAction('confirm')}>
                  {t('bookings.confirmSelected', 'Confirm Selected')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('cancel')}>
                  {t('bookings.cancelSelected', 'Cancel Selected')}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  className="text-red-600"
                  onClick={() => handleBulkAction('delete')}
                >
                  {t('bookings.deleteSelected', 'Delete Selected')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>

      {/* Bookings table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox 
                  checked={
                    filteredBookings.length > 0 && 
                    selectedBookings.length === filteredBookings.length
                  }
                  onCheckedChange={toggleSelectAllBookings}
                  aria-label="Select all"
                />
              </TableHead>
              <TableHead>{t('bookings.bookingId', 'Booking ID')}</TableHead>
              <TableHead>{t('bookings.customer', 'Customer')}</TableHead>
              <TableHead>{t('bookings.experience', 'Experience')}</TableHead>
              <TableHead>{t('bookings.date', 'Date')}</TableHead>
              <TableHead className="text-right">{t('bookings.amount', 'Amount')}</TableHead>
              <TableHead>{t('bookings.status', 'Status')}</TableHead>
              <TableHead className="text-right">
                <span className="sr-only">{t('common.actions', 'Actions')}</span>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Loading skeleton
              Array.from({ length: 5 }).map((_, i) => (
                <TableRow key={i}>
                  <TableCell><Skeleton className="h-4 w-4" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell className="text-right"><Skeleton className="h-4 w-6" /></TableCell>
                </TableRow>
              ))
            ) : filteredBookings.length > 0 ? (
              // Actual booking data
              filteredBookings.map((booking: Booking) => (
                <TableRow key={booking.id}>
                  <TableCell>
                    <Checkbox 
                      checked={selectedBookings.includes(booking.id)}
                      onCheckedChange={() => toggleBookingSelection(booking.id)}
                      aria-label="Select row"
                    />
                  </TableCell>
                  <TableCell className="font-medium">
                    <div className="font-mono text-sm">{booking.id}</div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{booking.customer.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {booking.customer.email}
                    </div>
                  </TableCell>
                  <TableCell className="text-sm">
                    {booking.experience}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {format(parseISO(booking.date), 'MMM d, yyyy')}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {booking.time}
                    </div>
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {formatCurrency(booking.amount)}
                  </TableCell>
                  <TableCell>
                    <Badge className={statusVariant[booking.status]}>
                      {t(`booking.status.${booking.status}`, { defaultValue: booking.status })}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <span className="sr-only">
                            {t('common.openMenu', 'Open menu')}
                          </span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>
                          {t('common.actions', 'Actions')}
                        </DropdownMenuLabel>
                        <DropdownMenuItem>
                          {t('common.view', 'View')}
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          {t('common.edit', 'Edit')}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {booking.status === 'pending' && (
                          <DropdownMenuItem>
                            {t('bookings.confirm', 'Confirm')}
                          </DropdownMenuItem>
                        )}
                        {booking.status !== 'cancelled' && (
                          <DropdownMenuItem>
                            {t('bookings.cancel', 'Cancel')}
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          {t('common.delete', 'Delete')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              // No results
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center">
                  {t('bookings.noBookingsFound', 'No bookings found.')}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export default BookingsPage;
