import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { Search, Plus, MoreHorizontal, Download, Filter } from 'lucide-react';
import { format } from 'date-fns';
import { getCustomers } from '@/lib/api/customers';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { formatCurrency } from '@/lib/utils';
export function CustomersPage() {
    const { t } = useTranslation();
    const [customers, setCustomers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedRows, setSelectedRows] = useState(new Set());
    const [filters, setFilters] = useState({
        page: 1,
        pageSize: 10,
        sortBy: 'joinDate',
        sortOrder: 'desc',
    });
    const [pagination, setPagination] = useState({
        total: 0,
        page: 1,
        pageSize: 10,
        totalPages: 1,
    });
    // Fetch customers when filters change
    useEffect(() => {
        const fetchCustomers = async () => {
            try {
                setLoading(true);
                const response = await getCustomers(filters);
                setCustomers(response.data);
                setPagination({
                    total: response.total,
                    page: response.page,
                    pageSize: response.pageSize,
                    totalPages: response.totalPages,
                });
            }
            catch (error) {
                console.error('Error fetching customers:', error);
            }
            finally {
                setLoading(false);
            }
        };
        fetchCustomers();
    }, [filters]);
    // Handle row selection
    const toggleRowSelection = (customerId) => {
        const newSelection = new Set(selectedRows);
        if (newSelection.has(customerId)) {
            newSelection.delete(customerId);
        }
        else {
            newSelection.add(customerId);
        }
        setSelectedRows(newSelection);
    };
    // Handle select all rows
    const toggleSelectAll = () => {
        if (selectedRows.size === customers.length) {
            setSelectedRows(new Set());
        }
        else {
            setSelectedRows(new Set(customers.map(c => c.id)));
        }
    };
    // Handle sorting
    const handleSort = (column) => {
        setFilters(prev => ({
            ...prev,
            sortBy: column,
            sortOrder: prev.sortBy === column && prev.sortOrder === 'asc' ? 'desc' : 'asc',
        }));
    };
    // Handle search
    const handleSearch = (e) => {
        setFilters(prev => ({
            ...prev,
            searchQuery: e.target.value,
            page: 1, // Reset to first page on new search
        }));
    };
    // Handle page change
    const handlePageChange = (newPage) => {
        setFilters(prev => ({
            ...prev,
            page: newPage,
        }));
        window.scrollTo(0, 0);
    };
    // Handle page size change
    const handlePageSizeChange = (newPageSize) => {
        setFilters(prev => ({
            ...prev,
            page: 1, // Reset to first page when changing page size
            pageSize: Number(newPageSize),
        }));
    };
    // Render sort indicator
    const renderSortIndicator = (column) => {
        if (filters.sortBy !== column)
            return null;
        return filters.sortOrder === 'asc' ? '↑' : '↓';
    };
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex flex-col justify-between space-y-4 sm:flex-row sm:items-center sm:space-y-0", children: [_jsxs("div", { children: [_jsx("h2", { className: "text-2xl font-bold tracking-tight", children: t('customers.title', 'Customers') }), _jsx("p", { className: "text-muted-foreground", children: t('customers.subtitle', 'Manage your customers and view their details') })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsxs(Button, { variant: "outline", size: "sm", className: "h-10", children: [_jsx(Download, { className: "mr-2 h-4 w-4" }), t('common.export', 'Export')] }), _jsx(Button, { asChild: true, children: _jsxs(Link, { to: "/admin/customers/new", children: [_jsx(Plus, { className: "mr-2 h-4 w-4" }), t('customers.addCustomer', 'Add Customer')] }) })] })] }), _jsxs("div", { className: "rounded-md border bg-white", children: [_jsxs("div", { className: "flex items-center justify-between p-4 border-b", children: [_jsxs("div", { className: "relative w-full max-w-md", children: [_jsx(Search, { className: "absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" }), _jsx(Input, { type: "search", placeholder: t('customers.searchPlaceholder', 'Search customers...'), className: "pl-9", value: filters.searchQuery || '', onChange: handleSearch })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsxs(Select, { value: filters.pageSize?.toString(), onValueChange: handlePageSizeChange, children: [_jsx(SelectTrigger, { className: "w-[120px]", children: _jsx(SelectValue, { placeholder: "10 per page" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "10", children: "10 per page" }), _jsx(SelectItem, { value: "25", children: "25 per page" }), _jsx(SelectItem, { value: "50", children: "50 per page" }), _jsx(SelectItem, { value: "100", children: "100 per page" })] })] }), _jsxs(Button, { variant: "outline", size: "sm", className: "h-10", children: [_jsx(Filter, { className: "mr-2 h-4 w-4" }), t('common.filters', 'Filters')] })] })] }), _jsx("div", { className: "relative overflow-auto", children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { className: "w-[50px]", children: _jsx(Checkbox, { checked: selectedRows.size === customers.length && customers.length > 0, onCheckedChange: toggleSelectAll, "aria-label": "Select all", className: "translate-y-0.5" }) }), _jsx(TableHead, { className: "cursor-pointer hover:bg-muted/50", onClick: () => handleSort('name'), children: _jsxs("div", { className: "flex items-center", children: [t('customers.name', 'Name'), _jsx("span", { className: "ml-1", children: renderSortIndicator('name') })] }) }), _jsx(TableHead, { children: t('customers.contact', 'Contact') }), _jsx(TableHead, { className: "cursor-pointer text-right", onClick: () => handleSort('totalSpent'), children: _jsxs("div", { className: "flex justify-end items-center", children: [t('customers.totalSpent', 'Total Spent'), _jsx("span", { className: "ml-1", children: renderSortIndicator('totalSpent') })] }) }), _jsx(TableHead, { className: "cursor-pointer text-right", onClick: () => handleSort('totalBookings'), children: _jsxs("div", { className: "flex justify-end items-center", children: [t('customers.bookings', 'Bookings'), _jsx("span", { className: "ml-1", children: renderSortIndicator('totalBookings') })] }) }), _jsx(TableHead, { className: "cursor-pointer", onClick: () => handleSort('joinDate'), children: _jsxs("div", { className: "flex items-center", children: [t('customers.memberSince', 'Member Since'), _jsx("span", { className: "ml-1", children: renderSortIndicator('joinDate') })] }) }), _jsx(TableHead, { className: "w-[50px]" })] }) }), _jsx(TableBody, { children: loading ? (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: 7, className: "h-24 text-center", children: t('common.loading', 'Loading...') }) })) : customers.length === 0 ? (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: 7, className: "h-24 text-center", children: t('customers.noCustomersFound', 'No customers found') }) })) : (customers.map((customer) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: _jsx(Checkbox, { checked: selectedRows.has(customer.id), onCheckedChange: () => toggleRowSelection(customer.id), "aria-label": `Select ${customer.firstName} ${customer.lastName}`, className: "translate-y-0.5" }) }), _jsx(TableCell, { children: _jsxs("div", { className: "flex items-center", children: [_jsxs(Avatar, { className: "h-9 w-9 mr-3", children: [_jsx(AvatarImage, { src: customer.avatar, alt: `${customer.firstName} ${customer.lastName}` }), _jsx(AvatarFallback, { children: `${customer.firstName[0]}${customer.lastName[0]}`.toUpperCase() })] }), _jsxs("div", { children: [_jsx("div", { className: "font-medium", children: _jsx(Link, { to: `/admin/customers/${customer.id}`, className: "hover:underline hover:text-primary", children: `${customer.firstName} ${customer.lastName}` }) }), _jsx("div", { className: "text-sm text-muted-foreground", children: customer.id })] })] }) }), _jsx(TableCell, { children: _jsxs("div", { className: "space-y-1", children: [_jsx("div", { className: "text-sm", children: customer.email }), customer.phone && (_jsx("div", { className: "text-sm text-muted-foreground", children: customer.phone }))] }) }), _jsx(TableCell, { className: "text-right font-medium", children: formatCurrency(customer.totalSpent) }), _jsx(TableCell, { className: "text-right", children: _jsxs(Badge, { variant: "outline", className: "text-sm", children: [customer.totalBookings, " ", t('common.bookings', 'bookings')] }) }), _jsx(TableCell, { children: format(new Date(customer.joinDate), 'MMM d, yyyy') }), _jsx(TableCell, { children: _jsxs(DropdownMenu, { children: [_jsx(DropdownMenuTrigger, { asChild: true, children: _jsxs(Button, { variant: "ghost", size: "icon", className: "h-8 w-8", children: [_jsx(MoreHorizontal, { className: "h-4 w-4" }), _jsx("span", { className: "sr-only", children: "Open menu" })] }) }), _jsxs(DropdownMenuContent, { align: "end", children: [_jsx(DropdownMenuItem, { asChild: true, children: _jsx(Link, { to: `/admin/customers/${customer.id}`, children: t('common.view', 'View') }) }), _jsx(DropdownMenuItem, { asChild: true, children: _jsx(Link, { to: `/admin/customers/${customer.id}/edit`, children: t('common.edit', 'Edit') }) })] })] }) })] }, customer.id)))) })] }) }), _jsxs("div", { className: "flex items-center justify-between p-4 border-t", children: [_jsx("div", { className: "text-sm text-muted-foreground", children: t('common.showingResults', 'Showing {{from}}-{{to}} of {{total}} results', {
                                    from: (pagination.page - 1) * pagination.pageSize + 1,
                                    to: Math.min(pagination.page * pagination.pageSize, pagination.total),
                                    total: pagination.total,
                                }) }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Button, { variant: "outline", size: "sm", onClick: () => handlePageChange(pagination.page - 1), disabled: pagination.page === 1, children: t('common.previous', 'Previous') }), _jsxs("div", { className: "flex items-center space-x-1", children: [Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                                                // Calculate page numbers to show (current page in the middle when possible)
                                                let pageNum;
                                                if (pagination.totalPages <= 5) {
                                                    pageNum = i + 1;
                                                }
                                                else if (pagination.page <= 3) {
                                                    pageNum = i + 1;
                                                }
                                                else if (pagination.page >= pagination.totalPages - 2) {
                                                    pageNum = pagination.totalPages - 4 + i;
                                                }
                                                else {
                                                    pageNum = pagination.page - 2 + i;
                                                }
                                                // Skip if we've gone past the last page
                                                if (pageNum > pagination.totalPages)
                                                    return null;
                                                return (_jsx(Button, { variant: pagination.page === pageNum ? 'default' : 'outline', size: "sm", onClick: () => handlePageChange(pageNum), className: pagination.page === pageNum ? 'font-bold' : '', children: pageNum }, pageNum));
                                            }), pagination.totalPages > 5 && pagination.page < pagination.totalPages - 2 && (_jsx("span", { className: "px-2 py-1", children: "..." })), pagination.totalPages > 5 && pagination.page < pagination.totalPages - 2 && (_jsx(Button, { variant: "outline", size: "sm", onClick: () => handlePageChange(pagination.totalPages), children: pagination.totalPages }))] }), _jsx(Button, { variant: "outline", size: "sm", onClick: () => handlePageChange(pagination.page + 1), disabled: pagination.page >= pagination.totalPages, children: t('common.next', 'Next') })] })] })] })] }));
}
export default CustomersPage;
