import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { Search, Plus, MoreHorizontal, Download, Filter } from 'lucide-react';
import { format } from 'date-fns';
import { Customer, CustomerFilters } from '@/types/customer';
import { getCustomers } from '@/lib/api/customers';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { formatCurrency } from '@/lib/utils';

export function CustomersPage() {
  const { t } = useTranslation();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [filters, setFilters] = useState<CustomerFilters>({
    page: 1,
    pageSize: 10,
    sortBy: 'joinDate',
    sortOrder: 'desc',
  });
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 1,
  });

  // Fetch customers when filters change
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setLoading(true);
        const response = await getCustomers(filters);
        setCustomers(response.data);
        setPagination({
          total: response.total,
          page: response.page,
          pageSize: response.pageSize,
          totalPages: response.totalPages,
        });
      } catch (error) {
        console.error('Error fetching customers:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCustomers();
  }, [filters]);

  // Handle row selection
  const toggleRowSelection = (customerId: string) => {
    const newSelection = new Set(selectedRows);
    if (newSelection.has(customerId)) {
      newSelection.delete(customerId);
    } else {
      newSelection.add(customerId);
    }
    setSelectedRows(newSelection);
  };

  // Handle select all rows
  const toggleSelectAll = () => {
    if (selectedRows.size === customers.length) {
      setSelectedRows(new Set());
    } else {
      setSelectedRows(new Set(customers.map(c => c.id)));
    }
  };

  // Handle sorting
  const handleSort = (column: 'name' | 'joinDate' | 'totalBookings' | 'totalSpent') => {
    setFilters(prev => ({
      ...prev,
      sortBy: column,
      sortOrder: prev.sortBy === column && prev.sortOrder === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({
      ...prev,
      searchQuery: e.target.value,
      page: 1, // Reset to first page on new search
    }));
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({
      ...prev,
      page: newPage,
    }));
    window.scrollTo(0, 0);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: string) => {
    setFilters(prev => ({
      ...prev,
      page: 1, // Reset to first page when changing page size
      pageSize: Number(newPageSize),
    }));
  };

  // Render sort indicator
  const renderSortIndicator = (column: string) => {
    if (filters.sortBy !== column) return null;
    return filters.sortOrder === 'asc' ? '↑' : '↓';
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between space-y-4 sm:flex-row sm:items-center sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            {t('customers.title', 'Customers')}
          </h2>
          <p className="text-muted-foreground">
            {t('customers.subtitle', 'Manage your customers and view their details')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" className="h-10">
            <Download className="mr-2 h-4 w-4" />
            {t('common.export', 'Export')}
          </Button>
          <Button asChild>
            <Link to="/admin/customers/new">
              <Plus className="mr-2 h-4 w-4" />
              {t('customers.addCustomer', 'Add Customer')}
            </Link>
          </Button>
        </div>
      </div>

      <div className="rounded-md border bg-white">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="relative w-full max-w-md">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              type="search"
              placeholder={t('customers.searchPlaceholder', 'Search customers...')}
              className="pl-9"
              value={filters.searchQuery || ''}
              onChange={handleSearch}
            />
          </div>
          <div className="flex items-center space-x-2">
            <Select
              value={filters.pageSize?.toString()}
              onValueChange={handlePageSizeChange}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="10 per page" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10 per page</SelectItem>
                <SelectItem value="25">25 per page</SelectItem>
                <SelectItem value="50">50 per page</SelectItem>
                <SelectItem value="100">100 per page</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" className="h-10">
              <Filter className="mr-2 h-4 w-4" />
              {t('common.filters', 'Filters')}
            </Button>
          </div>
        </div>

        <div className="relative overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">
                  <Checkbox 
                    checked={selectedRows.size === customers.length && customers.length > 0}
                    onCheckedChange={toggleSelectAll}
                    aria-label="Select all"
                    className="translate-y-0.5"
                  />
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center">
                    {t('customers.name', 'Name')}
                    <span className="ml-1">{renderSortIndicator('name')}</span>
                  </div>
                </TableHead>
                <TableHead>{t('customers.contact', 'Contact')}</TableHead>
                <TableHead 
                  className="cursor-pointer text-right"
                  onClick={() => handleSort('totalSpent')}
                >
                  <div className="flex justify-end items-center">
                    {t('customers.totalSpent', 'Total Spent')}
                    <span className="ml-1">{renderSortIndicator('totalSpent')}</span>
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer text-right"
                  onClick={() => handleSort('totalBookings')}
                >
                  <div className="flex justify-end items-center">
                    {t('customers.bookings', 'Bookings')}
                    <span className="ml-1">{renderSortIndicator('totalBookings')}</span>
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort('joinDate')}
                >
                  <div className="flex items-center">
                    {t('customers.memberSince', 'Member Since')}
                    <span className="ml-1">{renderSortIndicator('joinDate')}</span>
                  </div>
                </TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    {t('common.loading', 'Loading...')}
                  </TableCell>
                </TableRow>
              ) : customers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    {t('customers.noCustomersFound', 'No customers found')}
                  </TableCell>
                </TableRow>
              ) : (
                customers.map((customer) => (
                  <TableRow key={customer.id}>
                    <TableCell>
                      <Checkbox 
                        checked={selectedRows.has(customer.id)}
                        onCheckedChange={() => toggleRowSelection(customer.id)}
                        aria-label={`Select ${customer.firstName} ${customer.lastName}`}
                        className="translate-y-0.5"
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Avatar className="h-9 w-9 mr-3">
                          <AvatarImage src={customer.avatar} alt={`${customer.firstName} ${customer.lastName}`} />
                          <AvatarFallback>
                            {`${customer.firstName[0]}${customer.lastName[0]}`.toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            <Link 
                              to={`/admin/customers/${customer.id}`}
                              className="hover:underline hover:text-primary"
                            >
                              {`${customer.firstName} ${customer.lastName}`}
                            </Link>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {customer.id}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm">{customer.email}</div>
                        {customer.phone && (
                          <div className="text-sm text-muted-foreground">
                            {customer.phone}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {formatCurrency(customer.totalSpent)}
                    </TableCell>
                    <TableCell className="text-right">
                      <Badge variant="outline" className="text-sm">
                        {customer.totalBookings} {t('common.bookings', 'bookings')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {format(new Date(customer.joinDate), 'MMM d, yyyy')}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link to={`/admin/customers/${customer.id}`}>
                              {t('common.view', 'View')}
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link to={`/admin/customers/${customer.id}/edit`}>
                              {t('common.edit', 'Edit')}
                            </Link>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between p-4 border-t">
          <div className="text-sm text-muted-foreground">
            {t('common.showingResults', 'Showing {{from}}-{{to}} of {{total}} results', {
              from: (pagination.page - 1) * pagination.pageSize + 1,
              to: Math.min(pagination.page * pagination.pageSize, pagination.total),
              total: pagination.total,
            })}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
            >
              {t('common.previous', 'Previous')}
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                // Calculate page numbers to show (current page in the middle when possible)
                let pageNum;
                if (pagination.totalPages <= 5) {
                  pageNum = i + 1;
                } else if (pagination.page <= 3) {
                  pageNum = i + 1;
                } else if (pagination.page >= pagination.totalPages - 2) {
                  pageNum = pagination.totalPages - 4 + i;
                } else {
                  pageNum = pagination.page - 2 + i;
                }

                // Skip if we've gone past the last page
                if (pageNum > pagination.totalPages) return null;

                return (
                  <Button
                    key={pageNum}
                    variant={pagination.page === pageNum ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className={pagination.page === pageNum ? 'font-bold' : ''}
                  >
                    {pageNum}
                  </Button>
                );
              })}
              {pagination.totalPages > 5 && pagination.page < pagination.totalPages - 2 && (
                <span className="px-2 py-1">...</span>
              )}
              {pagination.totalPages > 5 && pagination.page < pagination.totalPages - 2 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.totalPages)}
                >
                  {pagination.totalPages}
                </Button>
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages}
            >
              {t('common.next', 'Next')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CustomersPage;
