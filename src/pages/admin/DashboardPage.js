import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getDashboardData } from '@/lib/api/dashboard';
export function DashboardPage() {
    const { t } = useTranslation();
    // Fetch dashboard data with proper typing and default values
    const { data: dashboardData, isLoading, error } = useQuery({
        queryKey: ['dashboard'],
        queryFn: async () => {
            const data = await getDashboardData();
            // Transform the API response to match our DashboardData interface
            return {
                totalBookings: data?.totalBookings || 0,
                revenue: data?.revenue || 0,
                avgBookingValue: data?.avgBookingValue || 0,
                upcomingBookings: data?.upcomingBookings || 0,
                bookingTrend: data?.bookingTrend || 0,
                revenueTrend: data?.revenueTrend || 0,
                avgBookingValueTrend: data?.avgBookingValueTrend || 0,
                revenueData: data?.revenueData || [],
                recentBookings: data?.recentBookings?.map((booking) => ({
                    id: booking.id,
                    customer: booking.customer.name, // Convert customer object to just the name
                    experience: booking.experience,
                    date: booking.date,
                    time: booking.time,
                    guests: booking.guests,
                    status: booking.status,
                    amount: booking.amount
                })) || [],
                recentActivity: data?.recentActivity || []
            };
        },
    });
    // Use default values for the dashboard data
    const safeData = {
        totalBookings: dashboardData?.totalBookings || 0,
        revenue: dashboardData?.revenue || 0,
        avgBookingValue: dashboardData?.avgBookingValue || 0,
        upcomingBookings: dashboardData?.upcomingBookings || 0,
        bookingTrend: dashboardData?.bookingTrend || 0,
        revenueTrend: dashboardData?.revenueTrend || 0,
        avgBookingValueTrend: dashboardData?.avgBookingValueTrend || 0,
        revenueData: dashboardData?.revenueData || [],
        recentBookings: dashboardData?.recentBookings || [],
        recentActivity: dashboardData?.recentActivity || []
    };
    // Handle error state
    if (error) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "text-center", children: [_jsx(AlertCircle, { className: "mx-auto h-12 w-12 text-red-500" }), _jsx("h3", { className: "mt-2 text-lg font-medium text-gray-900", children: t('error.loadingData', 'Error loading dashboard data') }), _jsx("p", { className: "mt-1 text-sm text-gray-500", children: t('error.tryAgain', 'Please try again later.') }), _jsx("div", { className: "mt-4", children: _jsx(Button, { onClick: () => window.location.reload(), variant: "outline", children: t('common.retry', 'Retry') }) })] }) }));
    }
    // Handle loading state
    if (isLoading) {
        return (_jsxs("div", { className: "space-y-8 p-4", children: [_jsx("div", { className: "grid gap-4 md:grid-cols-2 lg:grid-cols-4", children: [1, 2, 3, 4].map((i) => (_jsx("div", { className: "h-[110px] rounded-lg bg-gray-200 animate-pulse" }, i))) }), _jsxs("div", { className: "grid gap-4 md:grid-cols-2 lg:grid-cols-7", children: [_jsx("div", { className: "col-span-4 h-[400px] rounded-lg bg-gray-200 animate-pulse" }), _jsx("div", { className: "col-span-3 h-[400px] rounded-lg bg-gray-200 animate-pulse" })] })] }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsx("div", { className: "flex items-center justify-between", children: _jsx("div", { children: _jsx("h2", { className: "text-2xl font-bold tracking-tight", children: t('admin.dashboard', 'Dashboard') }) }) }), _jsx("div", { className: "grid gap-4 md:grid-cols-2 lg:grid-cols-4", children: [
                    {
                        title: t('admin.totalBookings', 'Total Bookings'),
                        value: safeData.totalBookings.toString(),
                        description: `+${safeData.bookingTrend.toFixed(1)}% from last month`
                    },
                    {
                        title: t('admin.revenue', 'Revenue'),
                        value: `$${safeData.revenue.toLocaleString()}`,
                        description: `+${safeData.revenueTrend.toFixed(1)}% from last month`
                    },
                    {
                        title: t('admin.upcomingBookings', 'Upcoming Bookings'),
                        value: safeData.upcomingBookings.toString(),
                        description: t('admin.scheduledNext30Days', 'Scheduled for next 30 days')
                    },
                    {
                        title: t('admin.avgBookingValue', 'Avg. Booking Value'),
                        value: `$${safeData.avgBookingValue.toFixed(2)}`,
                        description: `+${safeData.avgBookingValueTrend.toFixed(1)}% from last month`
                    }
                ].map((stat, index) => (_jsxs("div", { className: "rounded-lg border bg-card text-card-foreground shadow-sm p-6", children: [_jsx("h3", { className: "text-sm font-medium leading-none", children: stat.title }), _jsx("p", { className: "text-2xl font-bold mt-2", children: stat.value }), _jsx("p", { className: "text-xs text-muted-foreground mt-1", children: stat.description })] }, index))) }), _jsxs("div", { className: "rounded-lg border bg-card text-card-foreground shadow-sm p-6", children: [_jsx("h2", { className: "text-lg font-semibold mb-4", children: t('admin.recentActivity', 'Recent Activity') }), safeData.recentActivity.length > 0 ? (_jsx("div", { className: "space-y-4", children: safeData.recentActivity.map((activity, index) => (_jsxs("div", { className: "flex items-center justify-between border-b pb-2", children: [_jsxs("div", { children: [_jsx("p", { className: "font-medium", children: activity.action }), _jsx("p", { className: "text-sm text-muted-foreground", children: new Date(activity.timestamp).toLocaleString() })] }), _jsx("span", { className: "text-sm text-muted-foreground", children: activity.user })] }, index))) })) : (_jsx("p", { className: "text-muted-foreground", children: t('admin.noRecentActivity', 'No recent activity') }))] })] }));
}
export default DashboardPage;
