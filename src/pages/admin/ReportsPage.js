import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Calendar as CalendarIcon, BarChart2, DollarSign, Users, TrendingUp, Download } from 'lucide-react';
import { format, subMonths, isBefore, isSameMonth } from 'date-fns';
// UI Components - Using relative paths since shadcn/ui components are in the same directory
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { formatCurrency } from '@/lib/utils';
// Generate mock revenue data with proper typing
const generateMockRevenueData = (months = 12) => {
    const data = [];
    const now = new Date();
    for (let i = months - 1; i >= 0; i--) {
        const date = subMonths(now, i);
        const month = format(date, 'MMM yyyy');
        const revenue = Math.floor(Math.random() * 50000) + 10000; // $10k - $60k
        const bookings = Math.floor(Math.random() * 50) + 20; // 20-70 bookings
        const avgBookingValue = Math.round(revenue / bookings);
        const dailyData = Array.from({ length: 30 }, (_, i) => {
            const dayDate = new Date(date.getFullYear(), date.getMonth(), i + 1);
            return {
                date: dayDate,
                revenue: Math.floor(Math.random() * 3000) + 500,
                bookings: Math.floor(Math.random() * 5) + 1,
            };
        }).filter(day => isBefore(day.date, now) && isSameMonth(day.date, date));
        data.push({
            month,
            revenue,
            bookings,
            avgBookingValue,
            dailyData,
        });
    }
    return data;
};
// Generate mock customer data with proper typing
const generateMockCustomerData = (months = 12) => {
    const data = [];
    let totalCustomers = 1000;
    for (let i = months - 1; i >= 0; i--) {
        const newCustomers = Math.floor(Math.random() * 50) + 20; // 20-70 new customers
        const churned = Math.floor(Math.random() * 10) + 5; // 5-15 churned
        totalCustomers = totalCustomers + newCustomers - churned;
        data.push({
            month: i,
            totalCustomers,
            newCustomers,
            churned,
            growthRate: ((newCustomers - churned) / (totalCustomers - newCustomers + churned)) * 100,
        });
    }
    return data;
};
export function ReportsPage() {
    const { t } = useTranslation();
    const [timeRange, setTimeRange] = useState('30');
    const [revenueData, setRevenueData] = useState([]);
    const [customerData, setCustomerData] = useState([]);
    const [activeTab, setActiveTab] = useState('revenue');
    const [loading, setLoading] = useState(true);
    // Load mock data
    useEffect(() => {
        setLoading(true);
        // Simulate API call
        const timer = setTimeout(() => {
            setRevenueData(generateMockRevenueData(12));
            setCustomerData(generateMockCustomerData(12));
            setLoading(false);
        }, 800);
        return () => clearTimeout(timer);
    }, []);
    // Get current period data based on selected time range
    const getCurrentPeriodData = () => {
        if (revenueData.length === 0)
            return [];
        const months = parseInt(timeRange) / 30;
        return revenueData.slice(-Math.max(1, months));
    };
    // Calculate summary metrics
    const calculateSummary = () => {
        const currentData = getCurrentPeriodData();
        if (currentData.length === 0)
            return null;
        const totalRevenue = currentData.reduce((sum, month) => sum + month.revenue, 0);
        const totalBookings = currentData.reduce((sum, month) => sum + month.bookings, 0);
        const avgBookingValue = totalBookings > 0 ? totalRevenue / totalBookings : 0;
        // Calculate growth compared to previous period
        let revenueGrowth = 0;
        let bookingGrowth = 0;
        if (currentData.length > 1) {
            const currentPeriod = currentData[currentData.length - 1];
            const previousPeriod = currentData[currentData.length - 2];
            revenueGrowth = ((currentPeriod.revenue - previousPeriod.revenue) / (previousPeriod.revenue || 1)) * 100;
            bookingGrowth = ((currentPeriod.bookings - previousPeriod.bookings) / (previousPeriod.bookings || 1)) * 100;
        }
        return {
            totalRevenue,
            totalBookings,
            avgBookingValue,
            revenueGrowth,
            bookingGrowth,
        };
    };
    const summary = calculateSummary();
    // Handle loading state
    if (loading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto" }), _jsx("p", { className: "mt-2 text-muted-foreground", children: t('common.loading', 'Loading...') })] }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex flex-col justify-between space-y-4 sm:flex-row sm:items-center sm:space-y-0", children: [_jsxs("div", { children: [_jsx("h2", { className: "text-2xl font-bold tracking-tight", children: t('reports.title', 'Reports & Analytics') }), _jsx("p", { className: "text-muted-foreground", children: t('reports.subtitle', 'Track your business performance and gain insights') })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsxs(Select, { value: timeRange, onValueChange: setTimeRange, children: [_jsxs(SelectTrigger, { className: "w-[180px]", children: [_jsx(CalendarIcon, { className: "mr-2 h-4 w-4" }), _jsx(SelectValue, { placeholder: "Select time range" })] }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "7", children: "Last 7 days" }), _jsx(SelectItem, { value: "30", children: "Last 30 days" }), _jsx(SelectItem, { value: "90", children: "Last 90 days" }), _jsx(SelectItem, { value: "180", children: "Last 6 months" }), _jsx(SelectItem, { value: "365", children: "Last 12 months" })] })] }), _jsxs(Button, { variant: "outline", size: "sm", className: "h-10", children: [_jsx(Download, { className: "mr-2 h-4 w-4" }), t('common.export', 'Export')] })] })] }), loading ? (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsx("div", { className: "animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary" }) })) : (_jsxs(_Fragment, { children: [_jsxs("div", { className: "grid gap-4 md:grid-cols-2 lg:grid-cols-4", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: t('reports.totalRevenue', 'Total Revenue') }), _jsx(DollarSign, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: summary ? formatCurrency(summary.totalRevenue) : '-' }), summary && summary.revenueGrowth !== 0 && (_jsxs("p", { className: `text-xs ${summary.revenueGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`, children: [summary.revenueGrowth >= 0 ? '↑' : '↓', " ", Math.abs(summary.revenueGrowth).toFixed(1), "% ", t('reports.vsPreviousPeriod', 'vs previous period')] }))] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: t('reports.totalBookings', 'Total Bookings') }), _jsx(CalendarIcon, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: summary?.totalBookings || '-' }), summary && summary.bookingGrowth !== 0 && (_jsxs("p", { className: `text-xs ${summary.bookingGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`, children: [summary.bookingGrowth >= 0 ? '↑' : '↓', " ", Math.abs(summary.bookingGrowth).toFixed(1), "% ", t('reports.vsPreviousPeriod', 'vs previous period')] }))] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: t('reports.avgBookingValue', 'Avg. Booking Value') }), _jsx(BarChart2, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: summary ? formatCurrency(summary.avgBookingValue) : '-' }), _jsx("p", { className: "text-xs text-muted-foreground", children: t('reports.perBooking', 'per booking') })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: t('reports.customers', 'Total Customers') }), _jsx(Users, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: customerData.length > 0 ? customerData[customerData.length - 1].totalCustomers.toLocaleString() : '-' }), customerData.length > 1 && (_jsxs("p", { className: `text-xs ${customerData[customerData.length - 1].growthRate >= 0 ? 'text-green-500' : 'text-red-500'}`, children: [customerData[customerData.length - 1].growthRate >= 0 ? '↑' : '↓', " ", Math.abs(customerData[customerData.length - 1].growthRate).toFixed(1), "% ", t('reports.growth', 'growth')] }))] })] })] }), _jsxs(Tabs, { value: activeTab, onValueChange: setActiveTab, className: "space-y-4", children: [_jsxs(TabsList, { children: [_jsxs(TabsTrigger, { value: "revenue", children: [_jsx(DollarSign, { className: "mr-2 h-4 w-4" }), t('reports.revenue', 'Revenue')] }), _jsxs(TabsTrigger, { value: "bookings", children: [_jsx(CalendarIcon, { className: "mr-2 h-4 w-4" }), t('bookings.title', 'Bookings')] }), _jsxs(TabsTrigger, { value: "customers", children: [_jsx(Users, { className: "mr-2 h-4 w-4" }), t('customers.title', 'Customers')] }), _jsxs(TabsTrigger, { value: "trends", children: [_jsx(TrendingUp, { className: "mr-2 h-4 w-4" }), t('reports.trends', 'Trends')] })] }), _jsxs(TabsContent, { value: "revenue", className: "space-y-4", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: t('reports.revenueOverview', 'Revenue Overview') }) }), _jsx(CardContent, { children: _jsx("div", { className: "h-[400px] flex items-center justify-center bg-muted/50 rounded-lg", children: _jsx("p", { className: "text-muted-foreground", children: t('reports.revenueChartPlaceholder', 'Revenue chart will be displayed here') }) }) })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: t('reports.topPerforming', 'Top Performing Experiences') }) }), _jsx(CardContent, { children: _jsx("div", { className: "space-y-4", children: ['Sunset Sail', 'Full Day Charter', 'Whale Watching', 'Private Charter', 'Lunch Cruise'].map((exp, i) => (_jsxs("div", { className: "flex items-center", children: [_jsx("div", { className: "w-48 font-medium", children: exp }), _jsx("div", { className: "flex-1", children: _jsx("div", { className: "h-2 bg-primary/10 rounded-full overflow-hidden", children: _jsx("div", { className: "h-full bg-primary rounded-full", style: { width: `${Math.floor(Math.random() * 60) + 20}%` } }) }) }), _jsx("div", { className: "w-24 text-right font-medium", children: formatCurrency(Math.floor(Math.random() * 50000) + 10000) })] }, i))) }) })] })] }), _jsxs(TabsContent, { value: "bookings", className: "space-y-4", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: t('reports.bookingTrends', 'Booking Trends') }) }), _jsx(CardContent, { children: _jsx("div", { className: "h-[400px] flex items-center justify-center bg-muted/50 rounded-lg", children: _jsx("p", { className: "text-muted-foreground", children: t('reports.bookingChartPlaceholder', 'Booking trends chart will be displayed here') }) }) })] }), _jsxs("div", { className: "grid gap-4 md:grid-cols-2", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: t('reports.bookingSources', 'Booking Sources') }) }), _jsx(CardContent, { children: _jsx("div", { className: "space-y-4", children: [
                                                                { name: 'Direct', value: 35 },
                                                                { name: 'Website', value: 25 },
                                                                { name: 'Travel Agents', value: 20 },
                                                                { name: 'Referrals', value: 15 },
                                                                { name: 'Other', value: 5 },
                                                            ].map((source, i) => (_jsxs("div", { className: "flex items-center", children: [_jsx("div", { className: "w-32 text-sm", children: source.name }), _jsx("div", { className: "flex-1", children: _jsx("div", { className: "h-2 bg-muted rounded-full overflow-hidden", children: _jsx("div", { className: "h-full bg-primary rounded-full", style: { width: `${source.value}%` } }) }) }), _jsxs("div", { className: "w-12 text-right text-sm font-medium", children: [source.value, "%"] })] }, i))) }) })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: t('reports.bookingStatus', 'Booking Status') }) }), _jsx(CardContent, { children: _jsx("div", { className: "space-y-4", children: [
                                                                { name: 'Confirmed', value: 60, color: 'bg-green-500' },
                                                                { name: 'Pending', value: 20, color: 'bg-yellow-500' },
                                                                { name: 'Cancelled', value: 10, color: 'bg-red-500' },
                                                                { name: 'Completed', value: 10, color: 'bg-blue-500' },
                                                            ].map((status, i) => (_jsxs("div", { className: "flex items-center", children: [_jsx("div", { className: "w-32 text-sm", children: status.name }), _jsx("div", { className: "flex-1", children: _jsx("div", { className: "h-2 bg-muted rounded-full overflow-hidden", children: _jsx("div", { className: `h-full rounded-full ${status.color}`, style: { width: `${status.value}%` } }) }) }), _jsxs("div", { className: "w-12 text-right text-sm font-medium", children: [status.value, "%"] })] }, i))) }) })] })] })] }), _jsxs(TabsContent, { value: "customers", className: "space-y-4", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: t('reports.customerGrowth', 'Customer Growth') }) }), _jsx(CardContent, { children: _jsx("div", { className: "h-[400px] flex items-center justify-center bg-muted/50 rounded-lg", children: _jsx("p", { className: "text-muted-foreground", children: t('reports.customerChartPlaceholder', 'Customer growth chart will be displayed here') }) }) })] }), _jsxs("div", { className: "grid gap-4 md:grid-cols-2", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: t('reports.customerAcquisition', 'Customer Acquisition') }) }), _jsx(CardContent, { children: _jsx("div", { className: "space-y-4", children: [
                                                                { name: 'New Customers', value: customerData.length > 0 ? customerData[customerData.length - 1].newCustomers : 0, change: '+12%' },
                                                                { name: 'Returning Customers', value: customerData.length > 0 ? Math.floor(customerData[customerData.length - 1].totalCustomers * 0.65) : 0, change: '+5%' },
                                                                { name: 'Churned Customers', value: customerData.length > 0 ? customerData[customerData.length - 1].churned : 0, change: '-2%' },
                                                            ].map((metric, i) => (_jsxs("div", { className: "flex items-center justify-between", children: [_jsx("div", { className: "text-sm font-medium", children: metric.name }), _jsxs("div", { className: "flex items-center", children: [_jsx("span", { className: "font-medium", children: metric.value }), _jsx("span", { className: "ml-2 text-xs text-green-500", children: metric.change })] })] }, i))) }) })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: t('reports.customerSegments', 'Customer Segments') }) }), _jsx(CardContent, { children: _jsx("div", { className: "space-y-4", children: [
                                                                { name: 'First-time', value: 35 },
                                                                { name: 'Repeat', value: 45 },
                                                                { name: 'VIP', value: 15 },
                                                                { name: 'At Risk', value: 5 },
                                                            ].map((segment, i) => (_jsxs("div", { className: "flex items-center", children: [_jsx("div", { className: "w-24 text-sm", children: segment.name }), _jsx("div", { className: "flex-1", children: _jsx("div", { className: "h-2 bg-muted rounded-full overflow-hidden", children: _jsx("div", { className: "h-full bg-primary rounded-full", style: { width: `${segment.value}%` } }) }) }), _jsxs("div", { className: "w-12 text-right text-sm font-medium", children: [segment.value, "%"] })] }, i))) }) })] })] })] }), _jsxs(TabsContent, { value: "trends", className: "space-y-4", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: t('reports.seasonalTrends', 'Seasonal Trends') }) }), _jsx(CardContent, { children: _jsx("div", { className: "h-[400px] flex items-center justify-center bg-muted/50 rounded-lg", children: _jsx("p", { className: "text-muted-foreground", children: t('reports.seasonalChartPlaceholder', 'Seasonal trends chart will be displayed here') }) }) })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsx(CardTitle, { children: t('reports.forecast', 'Forecast & Projections') }), _jsxs(Select, { defaultValue: "next-6", children: [_jsx(SelectTrigger, { className: "w-[180px]", children: _jsx(SelectValue, { placeholder: "Select time period" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "next-3", children: "Next 3 Months" }), _jsx(SelectItem, { value: "next-6", children: "Next 6 Months" }), _jsx(SelectItem, { value: "next-12", children: "Next 12 Months" })] })] })] }) }), _jsx(CardContent, { children: _jsx("div", { className: "space-y-6", children: ['Revenue', 'Bookings', 'New Customers'].map((metric, i) => (_jsxs("div", { children: [_jsxs("div", { className: "flex items-center justify-between mb-2", children: [_jsx("span", { className: "text-sm font-medium", children: metric }), _jsxs("span", { className: "text-sm font-medium text-green-500", children: ["+", Math.floor(Math.random() * 20) + 5, "%"] })] }), _jsx("div", { className: "h-2 bg-muted rounded-full overflow-hidden", children: _jsx("div", { className: "h-full bg-primary rounded-full", style: { width: `${Math.floor(Math.random() * 80) + 20}%` } }) }), _jsxs("div", { className: "flex justify-between mt-1", children: [_jsx("span", { className: "text-xs text-muted-foreground", children: format(new Date(), 'MMM yyyy') }), _jsx("span", { className: "text-xs text-muted-foreground", children: format(new Date(new Date().setMonth(new Date().getMonth() + 5)), 'MMM yyyy') })] })] }, i))) }) })] })] })] })] }))] }));
}
export default ReportsPage;
