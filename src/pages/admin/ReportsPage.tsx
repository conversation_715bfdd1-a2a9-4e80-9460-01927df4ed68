import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Calendar as CalendarIcon, BarChart2, DollarSign, Users, TrendingUp, Download } from 'lucide-react';
import { format, subMonths, isBefore, isSameMonth } from 'date-fns';

// UI Components - Using relative paths since shadcn/ui components are in the same directory
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { formatCurrency } from '@/lib/utils';

// Type definitions
interface DailyData {
  date: Date;
  revenue: number;
  bookings: number;
}

interface RevenueData {
  month: string;
  revenue: number;
  bookings: number;
  avgBookingValue: number;
  dailyData: DailyData[];
}

interface CustomerData {
  month: number;
  totalCustomers: number;
  newCustomers: number;
  churned: number;
  growthRate: number;
}

interface SummaryMetrics {
  totalRevenue: number;
  totalBookings: number;
  avgBookingValue: number;
  revenueGrowth: number;
  bookingGrowth: number;
}

// Generate mock revenue data with proper typing
const generateMockRevenueData = (months: number = 12): RevenueData[] => {
  const data: RevenueData[] = [];
  const now = new Date();
  
  for (let i = months - 1; i >= 0; i--) {
    const date = subMonths(now, i);
    const month = format(date, 'MMM yyyy');
    const revenue = Math.floor(Math.random() * 50000) + 10000; // $10k - $60k
    const bookings = Math.floor(Math.random() * 50) + 20; // 20-70 bookings
    const avgBookingValue = Math.round(revenue / bookings);
    
    const dailyData: DailyData[] = Array.from({ length: 30 }, (_, i) => {
      const dayDate = new Date(date.getFullYear(), date.getMonth(), i + 1);
      return {
        date: dayDate,
        revenue: Math.floor(Math.random() * 3000) + 500,
        bookings: Math.floor(Math.random() * 5) + 1,
      };
    }).filter(day => isBefore(day.date, now) && isSameMonth(day.date, date));
    
    data.push({
      month,
      revenue,
      bookings,
      avgBookingValue,
      dailyData,
    });
  }
  
  return data;
};

// Generate mock customer data with proper typing
const generateMockCustomerData = (months: number = 12): CustomerData[] => {
  const data: CustomerData[] = [];
  let totalCustomers = 1000;
  
  for (let i = months - 1; i >= 0; i--) {
    const newCustomers = Math.floor(Math.random() * 50) + 20; // 20-70 new customers
    const churned = Math.floor(Math.random() * 10) + 5; // 5-15 churned
    totalCustomers = totalCustomers + newCustomers - churned;
    
    data.push({
      month: i,
      totalCustomers,
      newCustomers,
      churned,
      growthRate: ((newCustomers - churned) / (totalCustomers - newCustomers + churned)) * 100,
    });
  }
  
  return data;
};

export function ReportsPage() {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState<string>('30');
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [customerData, setCustomerData] = useState<CustomerData[]>([]);
  const [activeTab, setActiveTab] = useState<string>('revenue');
  const [loading, setLoading] = useState<boolean>(true);

  // Load mock data
  useEffect(() => {
    setLoading(true);
    
    // Simulate API call
    const timer = setTimeout(() => {
      setRevenueData(generateMockRevenueData(12));
      setCustomerData(generateMockCustomerData(12));
      setLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, []);

  // Get current period data based on selected time range
  const getCurrentPeriodData = (): RevenueData[] => {
    if (revenueData.length === 0) return [];
    
    const months = parseInt(timeRange) / 30;
    return revenueData.slice(-Math.max(1, months));
  };

  // Calculate summary metrics
  const calculateSummary = (): SummaryMetrics | null => {
    const currentData = getCurrentPeriodData();
    if (currentData.length === 0) return null;
    
    const totalRevenue = currentData.reduce((sum, month) => sum + month.revenue, 0);
    const totalBookings = currentData.reduce((sum, month) => sum + month.bookings, 0);
    const avgBookingValue = totalBookings > 0 ? totalRevenue / totalBookings : 0;
    
    // Calculate growth compared to previous period
    let revenueGrowth = 0;
    let bookingGrowth = 0;
    
    if (currentData.length > 1) {
      const currentPeriod = currentData[currentData.length - 1];
      const previousPeriod = currentData[currentData.length - 2];
      
      revenueGrowth = ((currentPeriod.revenue - previousPeriod.revenue) / (previousPeriod.revenue || 1)) * 100;
      bookingGrowth = ((currentPeriod.bookings - previousPeriod.bookings) / (previousPeriod.bookings || 1)) * 100;
    }
    
    return {
      totalRevenue,
      totalBookings,
      avgBookingValue,
      revenueGrowth,
      bookingGrowth,
    };
  };

  const summary = calculateSummary();
  
  // Handle loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">{t('common.loading', 'Loading...')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between space-y-4 sm:flex-row sm:items-center sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            {t('reports.title', 'Reports & Analytics')}
          </h2>
          <p className="text-muted-foreground">
            {t('reports.subtitle', 'Track your business performance and gain insights')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <CalendarIcon className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="180">Last 6 months</SelectItem>
              <SelectItem value="365">Last 12 months</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" className="h-10">
            <Download className="mr-2 h-4 w-4" />
            {t('common.export', 'Export')}
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {/* Summary Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t('reports.totalRevenue', 'Total Revenue')}
                </CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {summary ? formatCurrency(summary.totalRevenue) : '-'}
                </div>
                {summary && summary.revenueGrowth !== 0 && (
                  <p className={`text-xs ${summary.revenueGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {summary.revenueGrowth >= 0 ? '↑' : '↓'} {Math.abs(summary.revenueGrowth).toFixed(1)}% {t('reports.vsPreviousPeriod', 'vs previous period')}
                  </p>
                )}
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t('reports.totalBookings', 'Total Bookings')}
                </CardTitle>
                <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{summary?.totalBookings || '-'}</div>
                {summary && summary.bookingGrowth !== 0 && (
                  <p className={`text-xs ${summary.bookingGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {summary.bookingGrowth >= 0 ? '↑' : '↓'} {Math.abs(summary.bookingGrowth).toFixed(1)}% {t('reports.vsPreviousPeriod', 'vs previous period')}
                  </p>
                )}
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t('reports.avgBookingValue', 'Avg. Booking Value')}
                </CardTitle>
                <BarChart2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {summary ? formatCurrency(summary.avgBookingValue) : '-'}
                </div>
                <p className="text-xs text-muted-foreground">
                  {t('reports.perBooking', 'per booking')}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t('reports.customers', 'Total Customers')}
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {customerData.length > 0 ? customerData[customerData.length - 1].totalCustomers.toLocaleString() : '-'}
                </div>
                {customerData.length > 1 && (
                  <p className={`text-xs ${customerData[customerData.length - 1].growthRate >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {customerData[customerData.length - 1].growthRate >= 0 ? '↑' : '↓'} {Math.abs(customerData[customerData.length - 1].growthRate).toFixed(1)}% {t('reports.growth', 'growth')}
                  </p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Main Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="revenue">
                <DollarSign className="mr-2 h-4 w-4" />
                {t('reports.revenue', 'Revenue')}
              </TabsTrigger>
              <TabsTrigger value="bookings">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {t('bookings.title', 'Bookings')}
              </TabsTrigger>
              <TabsTrigger value="customers">
                <Users className="mr-2 h-4 w-4" />
                {t('customers.title', 'Customers')}
              </TabsTrigger>
              <TabsTrigger value="trends">
                <TrendingUp className="mr-2 h-4 w-4" />
                {t('reports.trends', 'Trends')}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="revenue" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>{t('reports.revenueOverview', 'Revenue Overview')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px] flex items-center justify-center bg-muted/50 rounded-lg">
                    <p className="text-muted-foreground">
                      {t('reports.revenueChartPlaceholder', 'Revenue chart will be displayed here')}
                    </p>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>{t('reports.topPerforming', 'Top Performing Experiences')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {['Sunset Sail', 'Full Day Charter', 'Whale Watching', 'Private Charter', 'Lunch Cruise'].map((exp, i) => (
                      <div key={i} className="flex items-center">
                        <div className="w-48 font-medium">{exp}</div>
                        <div className="flex-1">
                          <div className="h-2 bg-primary/10 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-primary rounded-full" 
                              style={{ width: `${Math.floor(Math.random() * 60) + 20}%` }}
                            />
                          </div>
                        </div>
                        <div className="w-24 text-right font-medium">
                          {formatCurrency(Math.floor(Math.random() * 50000) + 10000)}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="bookings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>{t('reports.bookingTrends', 'Booking Trends')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px] flex items-center justify-center bg-muted/50 rounded-lg">
                    <p className="text-muted-foreground">
                      {t('reports.bookingChartPlaceholder', 'Booking trends chart will be displayed here')}
                    </p>
                  </div>
                </CardContent>
              </Card>
              
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>{t('reports.bookingSources', 'Booking Sources')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { name: 'Direct', value: 35 },
                        { name: 'Website', value: 25 },
                        { name: 'Travel Agents', value: 20 },
                        { name: 'Referrals', value: 15 },
                        { name: 'Other', value: 5 },
                      ].map((source, i) => (
                        <div key={i} className="flex items-center">
                          <div className="w-32 text-sm">{source.name}</div>
                          <div className="flex-1">
                            <div className="h-2 bg-muted rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-primary rounded-full" 
                                style={{ width: `${source.value}%` }}
                              />
                            </div>
                          </div>
                          <div className="w-12 text-right text-sm font-medium">
                            {source.value}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>{t('reports.bookingStatus', 'Booking Status')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { name: 'Confirmed', value: 60, color: 'bg-green-500' },
                        { name: 'Pending', value: 20, color: 'bg-yellow-500' },
                        { name: 'Cancelled', value: 10, color: 'bg-red-500' },
                        { name: 'Completed', value: 10, color: 'bg-blue-500' },
                      ].map((status, i) => (
                        <div key={i} className="flex items-center">
                          <div className="w-32 text-sm">{status.name}</div>
                          <div className="flex-1">
                            <div className="h-2 bg-muted rounded-full overflow-hidden">
                              <div 
                                className={`h-full rounded-full ${status.color}`} 
                                style={{ width: `${status.value}%` }}
                              />
                            </div>
                          </div>
                          <div className="w-12 text-right text-sm font-medium">
                            {status.value}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="customers" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>{t('reports.customerGrowth', 'Customer Growth')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px] flex items-center justify-center bg-muted/50 rounded-lg">
                    <p className="text-muted-foreground">
                      {t('reports.customerChartPlaceholder', 'Customer growth chart will be displayed here')}
                    </p>
                  </div>
                </CardContent>
              </Card>
              
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>{t('reports.customerAcquisition', 'Customer Acquisition')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { name: 'New Customers', value: customerData.length > 0 ? customerData[customerData.length - 1].newCustomers : 0, change: '+12%' },
                        { name: 'Returning Customers', value: customerData.length > 0 ? Math.floor(customerData[customerData.length - 1].totalCustomers * 0.65) : 0, change: '+5%' },
                        { name: 'Churned Customers', value: customerData.length > 0 ? customerData[customerData.length - 1].churned : 0, change: '-2%' },
                      ].map((metric, i) => (
                        <div key={i} className="flex items-center justify-between">
                          <div className="text-sm font-medium">{metric.name}</div>
                          <div className="flex items-center">
                            <span className="font-medium">{metric.value}</span>
                            <span className="ml-2 text-xs text-green-500">{metric.change}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>{t('reports.customerSegments', 'Customer Segments')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { name: 'First-time', value: 35 },
                        { name: 'Repeat', value: 45 },
                        { name: 'VIP', value: 15 },
                        { name: 'At Risk', value: 5 },
                      ].map((segment, i) => (
                        <div key={i} className="flex items-center">
                          <div className="w-24 text-sm">{segment.name}</div>
                          <div className="flex-1">
                            <div className="h-2 bg-muted rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-primary rounded-full" 
                                style={{ width: `${segment.value}%` }}
                              />
                            </div>
                          </div>
                          <div className="w-12 text-right text-sm font-medium">
                            {segment.value}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="trends" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>{t('reports.seasonalTrends', 'Seasonal Trends')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px] flex items-center justify-center bg-muted/50 rounded-lg">
                    <p className="text-muted-foreground">
                      {t('reports.seasonalChartPlaceholder', 'Seasonal trends chart will be displayed here')}
                    </p>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>{t('reports.forecast', 'Forecast & Projections')}</CardTitle>
                    <Select defaultValue="next-6">
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select time period" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="next-3">Next 3 Months</SelectItem>
                        <SelectItem value="next-6">Next 6 Months</SelectItem>
                        <SelectItem value="next-12">Next 12 Months</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {['Revenue', 'Bookings', 'New Customers'].map((metric, i) => (
                      <div key={i}>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">{metric}</span>
                          <span className="text-sm font-medium text-green-500">+{Math.floor(Math.random() * 20) + 5}%</span>
                        </div>
                        <div className="h-2 bg-muted rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-primary rounded-full" 
                            style={{ width: `${Math.floor(Math.random() * 80) + 20}%` }}
                          />
                        </div>
                        <div className="flex justify-between mt-1">
                          <span className="text-xs text-muted-foreground">
                            {format(new Date(), 'MMM yyyy')}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {format(new Date(new Date().setMonth(new Date().getMonth() + 5)), 'MMM yyyy')}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}

export default ReportsPage;
