import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format, startOfWeek, addDays, startOfDay, endOfDay } from 'date-fns';
import { Calendar as BigCalendar, dateFnsLocalizer } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';
// Import shadcn/ui components
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarIcon } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
// Set up the localizer for the calendar
const localizer = dateFnsLocalizer({
    format,
    startOfWeek: () => startOfWeek(new Date(), { weekStartsOn: 1 }),
    getDay: (date) => date.getDay(),
    locales: {},
});
const BulkBlockModal = ({ isOpen, onClose, onConfirm }) => {
    const { t } = useTranslation();
    const [startDate, setStartDate] = useState(new Date());
    const [endDate, setEndDate] = useState(() => {
        const date = new Date();
        date.setDate(date.getDate() + 7); // Default to one week
        return date;
    });
    const [reason, setReason] = useState('');
    const [isStartDatePickerOpen, setIsStartDatePickerOpen] = useState(false);
    const [isEndDatePickerOpen, setIsEndDatePickerOpen] = useState(false);
    const handleSubmit = (e) => {
        e.preventDefault();
        if (startDate && endDate) {
            onConfirm(startDate, endDate, reason);
            onClose();
        }
    };
    return (_jsx(Dialog, { open: isOpen, onOpenChange: (open) => !open && onClose(), children: _jsxs(DialogContent, { className: "sm:max-w-[500px]", children: [_jsx(DialogHeader, { children: _jsx(DialogTitle, { children: t('calendar.bulkBlockTitle', 'Block Multiple Dates') }) }), _jsxs("form", { onSubmit: handleSubmit, className: "space-y-4", children: [_jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "start-date", children: t('calendar.startDate', 'Start Date') }), _jsxs(Popover, { children: [_jsx(PopoverTrigger, { asChild: true, children: _jsxs(Button, { variant: "outline", className: "w-full justify-start text-left font-normal", children: [_jsx(CalendarIcon, { className: "mr-2 h-4 w-4" }), startDate ? format(startDate, 'PPP') : _jsx("span", { children: "Pick a date" })] }) }), _jsx(PopoverContent, { className: "w-auto p-0", children: _jsx(Calendar, { mode: "single", selected: startDate, onSelect: setStartDate, initialFocus: true }) })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "end-date", children: t('calendar.endDate', 'End Date') }), _jsxs(Popover, { children: [_jsx(PopoverTrigger, { asChild: true, children: _jsxs(Button, { variant: "outline", className: "w-full justify-start text-left font-normal", children: [_jsx(CalendarIcon, { className: "mr-2 h-4 w-4" }), endDate ? format(endDate, 'PPP') : _jsx("span", { children: "Pick a date" })] }) }), _jsx(PopoverContent, { className: "w-auto p-0", children: _jsx(Calendar, { mode: "single", selected: endDate, onSelect: setEndDate, initialFocus: true }) })] })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "reason", children: t('calendar.reason', 'Reason (Optional)') }), _jsx("input", { id: "reason", className: "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", value: reason, onChange: (e) => setReason(e.target.value), placeholder: t('calendar.reasonPlaceholder', 'E.g., Maintenance, Holiday') })] }), _jsxs(DialogFooter, { children: [_jsx(Button, { type: "button", variant: "outline", onClick: onClose, children: t('common.cancel', 'Cancel') }), _jsx(Button, { type: "submit", disabled: !startDate || !endDate, children: t('calendar.blockDates', 'Block Dates') })] })] })] }) }));
};
export function CalendarPage() {
    const { t } = useTranslation();
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [selectedSlot, setSelectedSlot] = useState(null);
    const [events, setEvents] = useState([]);
    const [view, setView] = useState('month');
    const [date, setDate] = useState(new Date());
    const [eventStatus, setEventStatus] = useState('available');
    // State for bulk block modal
    const [isBulkBlockOpen, setIsBulkBlockOpen] = useState(false);
    const [isStartDatePickerOpen, setIsStartDatePickerOpen] = useState(false);
    const [isEndDatePickerOpen, setIsEndDatePickerOpen] = useState(false);
    // Handle slot selection
    const handleSelectSlot = useCallback((slotInfo) => {
        setSelectedSlot({
            start: startOfDay(slotInfo.start),
            end: endOfDay(slotInfo.end || slotInfo.start)
        });
        setEventStatus('available');
        setIsDialogOpen(true);
    }, []);
    // Handle bulk block confirm
    const handleBulkBlockConfirm = useCallback((startDate, endDate, reason) => {
        const newEvents = [];
        let currentDate = new Date(startDate);
        while (currentDate <= endDate) {
            newEvents.push({
                id: `block-${currentDate.toISOString()}`,
                title: reason || 'Blocked',
                start: startOfDay(new Date(currentDate)),
                end: endOfDay(new Date(currentDate)),
                status: 'blocked',
            });
            currentDate = addDays(currentDate, 1);
        }
        setEvents(prevEvents => [...prevEvents, ...newEvents]);
    }, []);
    // Handle event save
    const handleSaveEvent = useCallback(() => {
        if (!selectedSlot)
            return;
        const newEvent = {
            id: `event-${Date.now()}`,
            title: 'New Event',
            start: selectedSlot.start,
            end: selectedSlot.end,
            status: eventStatus,
        };
        setEvents(prev => [...prev, newEvent]);
        setIsDialogOpen(false);
    }, [selectedSlot, eventStatus]);
    // Handle event selection
    const handleSelectEvent = useCallback((selectedEvent) => {
        setSelectedSlot({
            start: selectedEvent.start,
            end: selectedEvent.end || selectedEvent.start
        });
        setEventStatus(selectedEvent.status);
        setIsDialogOpen(true);
    }, []);
    // Handle view change
    const handleViewChange = (newView) => {
        setView(newView);
    };
    // Handle navigation
    const handleNavigate = (newDate) => {
        setDate(newDate);
    };
    // Style events based on their status
    const eventStyleGetter = (event) => {
        let backgroundColor = '';
        switch (event.status) {
            case 'booked':
                backgroundColor = '#4f46e5'; // indigo-600
                break;
            case 'blocked':
                backgroundColor = '#ef4444'; // red-500
                break;
            default:
                backgroundColor = '#10b981'; // emerald-500
        }
        return {
            style: {
                backgroundColor,
                borderRadius: '4px',
                opacity: 0.8,
                color: 'white',
                border: '0',
                display: 'block',
            },
        };
    };
    // Custom event component
    const EventComponent = ({ event }) => (_jsx("div", { className: `p-1 rounded text-xs ${event.status === 'blocked' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`, children: event.title }));
    // Initialize with some mock events for demonstration
    useEffect(() => {
        const mockEvents = [
            {
                id: '1',
                title: 'Available',
                start: new Date(),
                end: new Date(new Date().setHours(new Date().getHours() + 2)),
                status: 'available'
            },
            {
                id: '2',
                title: 'Booked',
                start: new Date(new Date().setDate(new Date().getDate() + 1)),
                end: new Date(new Date().setDate(new Date().getDate() + 1)),
                status: 'booked'
            }
        ];
        setEvents(mockEvents);
    }, []);
    // Inline toolbar component for the calendar
    const CustomToolbar = (toolbar) => (_jsxs("div", { className: "flex justify-between mb-4", children: [_jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Button, { variant: "outline", size: "sm", onClick: () => toolbar.onNavigate('TODAY'), children: t('calendar.today', 'Today') }), _jsx(Button, { variant: "outline", size: "sm", onClick: () => toolbar.onNavigate('PREV'), children: "<" }), _jsx(Button, { variant: "outline", size: "sm", onClick: () => toolbar.onNavigate('NEXT'), children: ">" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Button, { variant: view === 'month' ? 'default' : 'outline', size: "sm", onClick: () => setView('month'), children: "Month" }), _jsx(Button, { variant: view === 'week' ? 'default' : 'outline', size: "sm", onClick: () => setView('week'), children: "Week" }), _jsx(Button, { variant: view === 'day' ? 'default' : 'outline', size: "sm", onClick: () => setView('day'), children: "Day" })] })] }));
    useEffect(() => {
        const mockEvents = [
            {
                id: '1',
                title: 'Available',
                start: new Date(),
                end: new Date(new Date().setHours(new Date().getHours() + 2)),
                status: 'available'
            },
            {
                id: '2',
                title: 'Booked',
                start: new Date(new Date().setDate(new Date().getDate() + 1)),
                end: new Date(new Date().setDate(new Date().getDate() + 1)),
                status: 'booked'
            }
        ];
        setEvents(mockEvents);
    }, []);
    return (_jsxs("div", { className: "p-6", children: [_jsxs("div", { className: "flex justify-between items-center mb-6", children: [_jsx("h1", { className: "text-2xl font-bold", children: "Calendar" }), _jsxs("div", { className: "flex space-x-2", children: [_jsx(BulkBlockModal, { isOpen: isBulkBlockOpen, onClose: () => setIsBulkBlockOpen(false), onConfirm: (start, end, reason) => {
                                    handleBulkBlockConfirm(start, end, reason);
                                    setIsBulkBlockOpen(false);
                                } }), _jsx(Button, { variant: "outline", onClick: () => setIsBulkBlockOpen(true), disabled: isBulkBlockOpen, children: "Bulk Block Dates" }), _jsx(Button, { onClick: () => {
                                    setSelectedSlot({ start: new Date(), end: new Date() });
                                    setEventStatus('available');
                                    setIsDialogOpen(true);
                                }, children: "Add Event" })] })] }), _jsx("div", { className: "bg-white p-4 rounded-lg shadow", children: _jsx("div", { className: "h-[600px] w-full", children: _jsx(BigCalendar, { localizer: localizer, events: events, startAccessor: "start", endAccessor: "end", style: { height: '100%' }, selectable: true, onSelectSlot: handleSelectSlot, onSelectEvent: handleSelectEvent, view: view, onView: handleViewChange, date: date, onNavigate: handleNavigate, eventPropGetter: eventStyleGetter, components: { event: EventComponent, toolbar: CustomToolbar }, messages: {
                            next: '>',
                            previous: '<',
                            today: 'Today',
                            month: 'Month',
                            week: 'Week',
                            day: 'Day',
                        } }) }) }), _jsx(Dialog, { open: isDialogOpen, onOpenChange: setIsDialogOpen, children: _jsxs(DialogContent, { children: [_jsx(DialogHeader, { children: _jsx(DialogTitle, { children: "Add Event" }) }), _jsxs("div", { className: "grid gap-4 py-4", children: [_jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium mb-2", children: "Date" }), _jsx("div", { className: "border rounded-md p-2", children: selectedSlot && (_jsx("p", { children: `${format(selectedSlot.start, 'PPP')} - ${format(selectedSlot.end, 'PPP')}` })) })] }), _jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium mb-2", children: "Status" }), _jsxs("select", { className: "w-full p-2 border rounded", value: eventStatus, onChange: (e) => setEventStatus(e.target.value), children: [_jsx("option", { value: "available", children: "Available" }), _jsx("option", { value: "booked", children: "Booked" }), _jsx("option", { value: "blocked", children: "Blocked" })] })] })] }), _jsxs(DialogFooter, { children: [_jsx(Button, { variant: "outline", onClick: () => setIsDialogOpen(false), children: "Cancel" }), _jsx(Button, { onClick: handleSaveEvent, children: "Save" })] })] }) })] }));
}
export default CalendarPage;
