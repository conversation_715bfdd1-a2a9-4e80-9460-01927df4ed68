import React, { useState } from 'react';
import { Mail, Phone, MapPin, Clock, Send, Instagram, Facebook, Twitter } from 'lucide-react';
import { motion } from 'framer-motion';
import { ContactForm } from '@/components/contact/ContactForm';
import { submitContactForm } from '@/api/contact';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

const ContactPage: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  // ReCAPTCHA site key (replace with your actual key)
  const recaptchaSiteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI'; // Default is test key

  // Handle form submission
  const handleSubmit = async (formData: any) => {
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      const result = await submitContactForm({
        ...formData,
        // In a real app, you might want to add additional metadata here
        // like the page URL, user agent, etc.
        _pageUrl: typeof window !== 'undefined' ? window.location.href : '',
        _timestamp: new Date().toISOString(),
      });

      setSubmitStatus({
        success: result.success,
        message: result.message,
      });

      return result.success;
    } catch (error) {
      console.error('Error submitting contact form:', error);
      setSubmitStatus({
        success: false,
        message: 'An unexpected error occurred. Please try again later or contact us directly.',
      });
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 10,
      },
    },
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-b from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-900 py-16 md:py-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Get in Touch
            </h1>
            <p className="text-xl text-muted-foreground">
              Have questions or want to book a sailing adventure? We'd love to hear from you!
            </p>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12 md:py-20">
        <div className="container mx-auto px-4">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start"
          >
            {/* Contact Form */}
            <motion.div variants={itemVariants} className="lg:sticky lg:top-8">
              <Card className="shadow-lg overflow-hidden">
                <CardHeader className="bg-primary/5 border-b">
                  <CardTitle className="text-2xl flex items-center gap-2">
                    <Send className="h-6 w-6" />
                    Send us a Message
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <ContactForm
                    recaptchaSiteKey={recaptchaSiteKey}
                    onSubmit={handleSubmit}
                    className="max-w-2xl mx-auto"
                  />
                </CardContent>
              </Card>
            </motion.div>

            {/* Contact Information */}
            <motion.div variants={itemVariants} className="space-y-8">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                  Contact Information
                </h2>
                <p className="text-lg text-muted-foreground mb-8">
                  We're here to help and answer any questions you might have. 
                  Reach out to us and we'll respond as soon as possible.
                </p>
              </div>

              {/* Contact Cards */}
              <div className="grid gap-6 sm:grid-cols-2">
                <Card className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="bg-primary/10 p-3 rounded-full text-primary">
                        <Mail className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg mb-1">Email Us</h3>
                        <p className="text-muted-foreground">
                          <a 
                            href="mailto:<EMAIL>" 
                            className="hover:text-primary hover:underline"
                          >
                            <EMAIL>
                          </a>
                        </p>
                        <p className="text-sm text-muted-foreground mt-2">
                          We'll respond within 24 hours
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="bg-primary/10 p-3 rounded-full text-primary">
                        <Phone className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg mb-1">Call Us</h3>
                        <p className="text-muted-foreground">
                          <a 
                            href="tel:+6495551234" 
                            className="hover:text-primary hover:underline"
                          >
                            +64 9 555 1234
                          </a>
                        </p>
                        <p className="text-sm text-muted-foreground mt-2">
                          Mon-Fri, 9am-5pm NZST
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow sm:col-span-2">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="bg-primary/10 p-3 rounded-full text-primary">
                        <MapPin className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg mb-1">Visit Us</h3>
                        <p className="text-muted-foreground">
                          123 Maritime Parade
                        </p>
                        <p className="text-muted-foreground">
                          Auckland Central, 1010
                        </p>
                        <p className="text-muted-foreground">
                          New Zealand
                        </p>
                        <Button variant="outline" size="sm" className="mt-3">
                          Get Directions
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Business Hours */}
              <div>
                <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
                  <Clock className="h-5 w-5 text-primary" />
                  Business Hours
                </h3>
                <div className="bg-muted/50 rounded-lg p-4 space-y-2">
                  {[
                    { day: 'Monday - Friday', hours: '9:00 AM - 5:00 PM' },
                    { day: 'Saturday', hours: '10:00 AM - 4:00 PM' },
                    { day: 'Sunday', hours: 'Closed' },
                  ].map((item, index) => (
                    <div key={index} className="flex justify-between">
                      <span className="text-muted-foreground">{item.day}</span>
                      <span className="font-medium">{item.hours}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Social Media */}
              <div>
                <h3 className="font-semibold text-lg mb-4">Follow Us</h3>
                <div className="flex gap-4">
                  {[
                    { icon: Instagram, url: 'https://instagram.com/sailingserai' },
                    { icon: Facebook, url: 'https://facebook.com/sailingserai' },
                    { icon: Twitter, url: 'https://twitter.com/sailingserai' },
                  ].map((social, index) => (
                    <a
                      key={index}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-muted hover:bg-primary/10 text-muted-foreground hover:text-primary transition-colors"
                      aria-label={social.icon.name}
                    >
                      <social.icon className="h-5 w-5" />
                    </a>
                  ))}
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Map Section */}
      <section className="bg-muted/50 py-12 md:py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="max-w-6xl mx-auto"
          >
            <h2 className="text-3xl font-bold text-center mb-8">Find Us on the Map</h2>
            <div 
              className="rounded-xl overflow-hidden shadow-lg h-96 bg-muted"
              style={{ 
                backgroundImage: 'url(/images/map-placeholder.jpg)',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
              }}
            >
              {/* In a real app, you would embed a Google Map or similar here */}
              <div className="w-full h-full flex items-center justify-center bg-black/20 backdrop-blur-sm">
                <Button asChild>
                  <a 
                    href="https://www.google.com/maps/search/?api=1&query=Sailing+Serai+Auckland" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="flex items-center gap-2"
                  >
                    <MapPin className="h-5 w-5" />
                    Open in Google Maps
                  </a>
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
