import { jsx as _jsx } from "react/jsx-runtime";
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { Suspense, lazy } from 'react';
import App from '@/App';
import { AuthLayout, DashboardLayout } from '@/layouts';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Loader2 } from 'lucide-react';
// Lazy load pages for better performance
const LoginPage = lazy(() => import('@/pages/LoginPage'));
const DashboardPage = lazy(() => import('@/pages/admin/DashboardPage'));
const BookingsPage = lazy(() => import('@/pages/admin/BookingsPage'));
const CalendarPage = lazy(() => import('@/pages/admin/CalendarPage'));
const CustomersPage = lazy(() => import('@/pages/admin/CustomersPage'));
const SettingsPage = lazy(() => import('@/pages/admin/SettingsPage'));
// Loading component for lazy-loaded routes
const LoadingFallback = () => (_jsx("div", { className: "flex items-center justify-center min-h-screen", children: _jsx(Loader2, { className: "h-8 w-8 animate-spin text-blue-600" }) }));
// Create a wrapper for lazy-loaded components with Suspense
const LazyComponent = ({ Component }) => (_jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(Component, {}) }));
export const router = createBrowserRouter([
    {
        path: '/',
        element: _jsx(App, {}),
        children: [
            {
                index: true,
                element: _jsx(Navigate, { to: "/home", replace: true }),
            },
            // Add more public routes here
        ],
    },
    // Auth routes (login, register, forgot-password, etc.)
    {
        path: '/auth',
        element: _jsx(AuthLayout, {}),
        children: [
            {
                path: 'login',
                element: _jsx(LazyComponent, { Component: LoginPage }),
            },
            // Add more auth routes as needed
        ],
    },
    // Protected admin routes
    {
        path: '/admin',
        element: (_jsx(ProtectedRoute, { children: _jsx(DashboardLayout, {}) })),
        children: [
            {
                index: true,
                element: _jsx(LazyComponent, { Component: DashboardPage }),
            },
            {
                path: 'bookings',
                element: _jsx(LazyComponent, { Component: BookingsPage }),
            },
            {
                path: 'calendar',
                element: _jsx(LazyComponent, { Component: CalendarPage }),
            },
            {
                path: 'customers',
                element: _jsx(LazyComponent, { Component: CustomersPage }),
            },
            {
                path: 'settings',
                element: _jsx(LazyComponent, { Component: SettingsPage }),
            },
        ],
    },
    // 404 route
    {
        path: '*',
        element: _jsx(Navigate, { to: "/", replace: true }),
    },
]);
