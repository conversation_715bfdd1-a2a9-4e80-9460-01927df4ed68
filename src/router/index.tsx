import { createBrowserRouter, Navigate } from 'react-router-dom';
import { Suspense, lazy } from 'react';
import App from '@/App';
import { AuthLayout, DashboardLayout } from '@/layouts';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Loader2 } from 'lucide-react';

// Lazy load pages for better performance
const LoginPage = lazy(() => import('@/pages/LoginPage'));
const DashboardPage = lazy(() => import('@/pages/admin/DashboardPage'));
const BookingsPage = lazy(() => import('@/pages/admin/BookingsPage'));
const CalendarPage = lazy(() => import('@/pages/admin/CalendarPage'));
const CustomersPage = lazy(() => import('@/pages/admin/CustomersPage'));
const SettingsPage = lazy(() => import('@/pages/admin/SettingsPage'));

// Loading component for lazy-loaded routes
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
  </div>
);

// Create a wrapper for lazy-loaded components with Suspense
const LazyComponent = ({ Component }: { Component: React.ComponentType }) => (
  <Suspense fallback={<LoadingFallback />}>
    <Component />
  </Suspense>
);

export const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: [
      {
        index: true,
        element: <Navigate to="/home" replace />,
      },
      // Add more public routes here
    ],
  },
  // Auth routes (login, register, forgot-password, etc.)
  {
    path: '/auth',
    element: <AuthLayout />,
    children: [
      {
        path: 'login',
        element: <LazyComponent Component={LoginPage} />,
      },
      // Add more auth routes as needed
    ],
  },
  // Protected admin routes
  {
    path: '/admin',
    element: (
      <ProtectedRoute>
        <DashboardLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <LazyComponent Component={DashboardPage} />,
      },
      {
        path: 'bookings',
        element: <LazyComponent Component={BookingsPage} />,
      },
      {
        path: 'calendar',
        element: <LazyComponent Component={CalendarPage} />,
      },
      {
        path: 'customers',
        element: <LazyComponent Component={CustomersPage} />,
      },
      {
        path: 'settings',
        element: <LazyComponent Component={SettingsPage} />,
      },
    ],
  },
  // 404 route
  {
    path: '*',
    element: <Navigate to="/" replace />,
  },
]);
