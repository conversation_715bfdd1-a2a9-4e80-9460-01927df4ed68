import { jsx as _jsx } from "react/jsx-runtime";
import { createContext, useContext, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
const AuthContext = createContext(undefined);
export function AuthProvider({ children }) {
    const [user, setUser] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const { t } = useTranslation();
    // Check if user is already authenticated on initial load
    useEffect(() => {
        const initializeAuth = async () => {
            try {
                await checkAuth();
            }
            catch (error) {
                console.error('Auth initialization error:', error);
            }
            finally {
                setIsLoading(false);
            }
        };
        initializeAuth();
    }, []);
    const login = async (email, password) => {
        try {
            setIsLoading(true);
            // In a real app, you would make an API call to your authentication service
            // For demo purposes, we'll simulate a successful login
            await new Promise(resolve => setTimeout(resolve, 500));
            // In a real app, you would validate the password here
            if (!password) {
                throw new Error('Password is required');
            }
            // Mock user data - replace with actual API response
            const mockUser = {
                id: '1',
                name: 'Admin User',
                email: email,
                role: 'admin',
                avatar: 'https://ui-avatars.com/api/?name=Admin+User&background=0D8ABC&color=fff',
            };
            localStorage.setItem('auth_token', 'dummy_token');
            localStorage.setItem('user', JSON.stringify(mockUser));
            setUser(mockUser);
        }
        catch (error) {
            console.error('Login error:', error);
            throw new Error(t('auth.loginError', 'Failed to login. Please check your credentials.'));
        }
        finally {
            setIsLoading(false);
        }
    };
    const logout = () => {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user');
        setUser(null);
    };
    const checkAuth = async () => {
        try {
            // In a real app, you would verify the token with your backend
            const token = localStorage.getItem('auth_token');
            const userData = localStorage.getItem('user');
            if (token && userData) {
                const parsedUser = JSON.parse(userData);
                setUser(parsedUser);
                return true;
            }
            setUser(null);
            return false;
        }
        catch (error) {
            console.error('Auth check error:', error);
            setUser(null);
            return false;
        }
        finally {
            setIsLoading(false);
        }
    };
    const value = {
        user,
        isAuthenticated: !!user,
        isLoading,
        login,
        logout,
        checkAuth,
    };
    return _jsx(AuthContext.Provider, { value: value, children: children });
}
export function useAuth() {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
