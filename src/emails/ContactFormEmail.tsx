import React from 'react';
import { ContactFormData } from '@/types/contact';

interface ContactFormEmailProps {
  formData: ContactFormData;
  isAdmin?: boolean;
}

export const ContactFormEmail: React.FC<ContactFormEmailProps> = ({ 
  formData, 
  isAdmin = false 
}) => {
  const { name, email, phone, subject, message } = formData;
  const currentDate = new Date().toLocaleString('en-NZ', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'Pacific/Auckland',
  });

  return (
    <div style={containerStyle}>
      <div style={headerStyle}>
        <h1 style={titleStyle}>
          {isAdmin ? 'New Contact Form Submission' : 'Thank You for Contacting Us'}
        </h1>
        <p style={dateStyle}>{currentDate}</p>
      </div>

      <div style={contentStyle}>
        {isAdmin ? (
          <>
            <p>You have received a new contact form submission with the following details:</p>
            
            <div style={detailsContainer}>
              <h2 style={sectionTitleStyle}>Contact Details</h2>
              <div style={detailRow}>
                <span style={labelStyle}>Name:</span>
                <span style={valueStyle}>{name}</span>
              </div>
              <div style={detailRow}>
                <span style={labelStyle}>Email:</span>
                <a href={`mailto:${email}`} style={linkStyle}>
                  {email}
                </a>
              </div>
              {phone && (
                <div style={detailRow}>
                  <span style={labelStyle}>Phone:</span>
                  <a href={`tel:${phone}`} style={linkStyle}>
                    {phone}
                  </a>
                </div>
              )}
              <div style={detailRow}>
                <span style={labelStyle}>Subject:</span>
                <span style={valueStyle}>{subject}</span>
              </div>
            </div>

            <div style={{ ...detailsContainer, marginTop: '24px' }}>
              <h2 style={sectionTitleStyle}>Message</h2>
              <div style={messageBox}>
                <p style={{ margin: 0, lineHeight: 1.6 }}>{message}</p>
              </div>
            </div>

            <div style={{ marginTop: '32px', textAlign: 'center' }}>
              <a 
                href={`mailto:${email}`} 
                style={buttonStyle}
              >
                Reply to {name.split(' ')[0]}
              </a>
            </div>
          </>
        ) : (
          <>
            <p>Dear {name},</p>
            
            <p>
              Thank you for reaching out to Sailing Serai. We've received your message and 
              our team will get back to you as soon as possible, usually within 24 hours.
            </p>
            
            <div style={{ ...detailsContainer, margin: '24px 0' }}>
              <h2 style={sectionTitleStyle}>Your Message</h2>
              <div style={messageBox}>
                <p style={{ margin: 0, lineHeight: 1.6 }}>{message}</p>
              </div>
            </div>
            
            <p>
              <strong>Subject:</strong> {subject}
            </p>
            
            <p>
              If you need immediate assistance, please don't hesitate to call us at 
              <a href="tel:+6495551234" style={linkStyle}> +64 9 555 1234</a> 
              during our business hours.
            </p>
            
            <p>
              Best regards,<br />
              The Sailing Serai Team
            </p>
            
            <div style={{ marginTop: '32px', paddingTop: '16px', borderTop: '1px solid #e2e8f0' }}>
              <p style={{ fontSize: '14px', color: '#64748b', margin: '4px 0' }}>
                <strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 5:00 PM NZST
              </p>
              <p style={{ fontSize: '14px', color: '#64748b', margin: '4px 0' }}>
                <strong>Email:</strong> <EMAIL>
              </p>
              <p style={{ fontSize: '14px', color: '#64748b', margin: '4px 0' }}>
                <strong>Phone:</strong> +64 9 555 1234
              </p>
            </div>
          </>
        )}
      </div>
      
      <div style={footerStyle}>
        <p style={{ margin: 0, fontSize: '12px', color: '#94a3b8' }}>
          &copy; {new Date().getFullYear()} Sailing Serai. All rights reserved.
        </p>
        <p style={{ margin: '8px 0 0', fontSize: '12px', color: '#94a3b8' }}>
          <a href="https://sailingserai.com/privacy-policy" style={{ color: '#94a3b8', textDecoration: 'underline' }}>
            Privacy Policy
          </a>
          {' | '}
          <a href="https://sailingserai.com/contact" style={{ color: '#94a3b8', textDecoration: 'underline' }}>
            Contact Us
          </a>
        </p>
      </div>
    </div>
  );
};

// Inline styles for email compatibility
const containerStyle = {
  maxWidth: '600px',
  margin: '0 auto',
  fontFamily: '"Inter", Arial, sans-serif',
  lineHeight: 1.6,
  color: '#1e293b',
  backgroundColor: '#ffffff',
  borderRadius: '8px',
  overflow: 'hidden',
  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
};

const headerStyle = {
  backgroundColor: '#1e40af',
  color: '#ffffff',
  padding: '24px',
  textAlign: 'center' as const,
};

const titleStyle = {
  margin: '0 0 8px',
  fontSize: '24px',
  fontWeight: 600,
  lineHeight: 1.2,
};

const dateStyle = {
  margin: 0,
  fontSize: '14px',
  opacity: 0.9,
};

const contentStyle = {
  padding: '24px',
  fontSize: '16px',
  lineHeight: 1.6,
};

const detailsContainer = {
  backgroundColor: '#f8fafc',
  borderRadius: '6px',
  padding: '16px',
  margin: '16px 0',
};

const sectionTitleStyle = {
  fontSize: '18px',
  fontWeight: 600,
  margin: '0 0 12px',
  color: '#1e40af',
};

const detailRow = {
  display: 'flex',
  marginBottom: '8px',
  fontSize: '15px',
};

const labelStyle = {
  fontWeight: 600,
  width: '80px',
  flexShrink: 0,
  color: '#475569',
};

const valueStyle = {
  flex: 1,
  color: '#1e293b',
};

const linkStyle = {
  color: '#1e40af',
  textDecoration: 'none',
};

const messageBox = {
  backgroundColor: '#ffffff',
  border: '1px solid #e2e8f0',
  borderRadius: '4px',
  padding: '12px',
  marginTop: '8px',
  fontSize: '15px',
  lineHeight: 1.6,
};

const buttonStyle = {
  display: 'inline-block',
  backgroundColor: '#1e40af',
  color: '#ffffff',
  textDecoration: 'none',
  padding: '10px 20px',
  borderRadius: '4px',
  fontWeight: 500,
  fontSize: '15px',
};

const footerStyle = {
  backgroundColor: '#f1f5f9',
  padding: '16px 24px',
  textAlign: 'center' as const,
  fontSize: '12px',
  color: '#64748b',
  borderTop: '1px solid #e2e8f0',
};
