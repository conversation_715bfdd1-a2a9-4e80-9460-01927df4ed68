import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
export const ContactFormEmail = ({ formData, isAdmin = false }) => {
    const { name, email, phone, subject, message } = formData;
    const currentDate = new Date().toLocaleString('en-NZ', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Pacific/Auckland',
    });
    return (_jsxs("div", { style: containerStyle, children: [_jsxs("div", { style: headerStyle, children: [_jsx("h1", { style: titleStyle, children: isAdmin ? 'New Contact Form Submission' : 'Thank You for Contacting Us' }), _jsx("p", { style: dateStyle, children: currentDate })] }), _jsx("div", { style: contentStyle, children: isAdmin ? (_jsxs(_Fragment, { children: [_jsx("p", { children: "You have received a new contact form submission with the following details:" }), _jsxs("div", { style: detailsContainer, children: [_jsx("h2", { style: sectionTitleStyle, children: "Contact Details" }), _jsxs("div", { style: detailRow, children: [_jsx("span", { style: labelStyle, children: "Name:" }), _jsx("span", { style: valueStyle, children: name })] }), _jsxs("div", { style: detailRow, children: [_jsx("span", { style: labelStyle, children: "Email:" }), _jsx("a", { href: `mailto:${email}`, style: linkStyle, children: email })] }), phone && (_jsxs("div", { style: detailRow, children: [_jsx("span", { style: labelStyle, children: "Phone:" }), _jsx("a", { href: `tel:${phone}`, style: linkStyle, children: phone })] })), _jsxs("div", { style: detailRow, children: [_jsx("span", { style: labelStyle, children: "Subject:" }), _jsx("span", { style: valueStyle, children: subject })] })] }), _jsxs("div", { style: { ...detailsContainer, marginTop: '24px' }, children: [_jsx("h2", { style: sectionTitleStyle, children: "Message" }), _jsx("div", { style: messageBox, children: _jsx("p", { style: { margin: 0, lineHeight: 1.6 }, children: message }) })] }), _jsx("div", { style: { marginTop: '32px', textAlign: 'center' }, children: _jsxs("a", { href: `mailto:${email}`, style: buttonStyle, children: ["Reply to ", name.split(' ')[0]] }) })] })) : (_jsxs(_Fragment, { children: [_jsxs("p", { children: ["Dear ", name, ","] }), _jsx("p", { children: "Thank you for reaching out to Sailing Serai. We've received your message and our team will get back to you as soon as possible, usually within 24 hours." }), _jsxs("div", { style: { ...detailsContainer, margin: '24px 0' }, children: [_jsx("h2", { style: sectionTitleStyle, children: "Your Message" }), _jsx("div", { style: messageBox, children: _jsx("p", { style: { margin: 0, lineHeight: 1.6 }, children: message }) })] }), _jsxs("p", { children: [_jsx("strong", { children: "Subject:" }), " ", subject] }), _jsxs("p", { children: ["If you need immediate assistance, please don't hesitate to call us at", _jsx("a", { href: "tel:+6495551234", style: linkStyle, children: " +64 9 555 1234" }), "during our business hours."] }), _jsxs("p", { children: ["Best regards,", _jsx("br", {}), "The Sailing Serai Team"] }), _jsxs("div", { style: { marginTop: '32px', paddingTop: '16px', borderTop: '1px solid #e2e8f0' }, children: [_jsxs("p", { style: { fontSize: '14px', color: '#64748b', margin: '4px 0' }, children: [_jsx("strong", { children: "Business Hours:" }), " Monday - Friday, 9:00 AM - 5:00 PM NZST"] }), _jsxs("p", { style: { fontSize: '14px', color: '#64748b', margin: '4px 0' }, children: [_jsx("strong", { children: "Email:" }), " <EMAIL>"] }), _jsxs("p", { style: { fontSize: '14px', color: '#64748b', margin: '4px 0' }, children: [_jsx("strong", { children: "Phone:" }), " +64 9 555 1234"] })] })] })) }), _jsxs("div", { style: footerStyle, children: [_jsxs("p", { style: { margin: 0, fontSize: '12px', color: '#94a3b8' }, children: ["\u00A9 ", new Date().getFullYear(), " Sailing Serai. All rights reserved."] }), _jsxs("p", { style: { margin: '8px 0 0', fontSize: '12px', color: '#94a3b8' }, children: [_jsx("a", { href: "https://sailingserai.com/privacy-policy", style: { color: '#94a3b8', textDecoration: 'underline' }, children: "Privacy Policy" }), ' | ', _jsx("a", { href: "https://sailingserai.com/contact", style: { color: '#94a3b8', textDecoration: 'underline' }, children: "Contact Us" })] })] })] }));
};
// Inline styles for email compatibility
const containerStyle = {
    maxWidth: '600px',
    margin: '0 auto',
    fontFamily: '"Inter", Arial, sans-serif',
    lineHeight: 1.6,
    color: '#1e293b',
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    overflow: 'hidden',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
};
const headerStyle = {
    backgroundColor: '#1e40af',
    color: '#ffffff',
    padding: '24px',
    textAlign: 'center',
};
const titleStyle = {
    margin: '0 0 8px',
    fontSize: '24px',
    fontWeight: 600,
    lineHeight: 1.2,
};
const dateStyle = {
    margin: 0,
    fontSize: '14px',
    opacity: 0.9,
};
const contentStyle = {
    padding: '24px',
    fontSize: '16px',
    lineHeight: 1.6,
};
const detailsContainer = {
    backgroundColor: '#f8fafc',
    borderRadius: '6px',
    padding: '16px',
    margin: '16px 0',
};
const sectionTitleStyle = {
    fontSize: '18px',
    fontWeight: 600,
    margin: '0 0 12px',
    color: '#1e40af',
};
const detailRow = {
    display: 'flex',
    marginBottom: '8px',
    fontSize: '15px',
};
const labelStyle = {
    fontWeight: 600,
    width: '80px',
    flexShrink: 0,
    color: '#475569',
};
const valueStyle = {
    flex: 1,
    color: '#1e293b',
};
const linkStyle = {
    color: '#1e40af',
    textDecoration: 'none',
};
const messageBox = {
    backgroundColor: '#ffffff',
    border: '1px solid #e2e8f0',
    borderRadius: '4px',
    padding: '12px',
    marginTop: '8px',
    fontSize: '15px',
    lineHeight: 1.6,
};
const buttonStyle = {
    display: 'inline-block',
    backgroundColor: '#1e40af',
    color: '#ffffff',
    textDecoration: 'none',
    padding: '10px 20px',
    borderRadius: '4px',
    fontWeight: 500,
    fontSize: '15px',
};
const footerStyle = {
    backgroundColor: '#f1f5f9',
    padding: '16px 24px',
    textAlign: 'center',
    fontSize: '12px',
    color: '#64748b',
    borderTop: '1px solid #e2e8f0',
};
