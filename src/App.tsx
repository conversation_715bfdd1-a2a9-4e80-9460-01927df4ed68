import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Navigation } from './components/navigation';
import { Hero } from './components/sections/hero';
import { Experiences } from './components/sections/experiences';
import './App.css';

function App() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('home');

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      <Navigation activeTab={activeTab} onTabChange={setActiveTab} />
      
      <main>
        {/* Hero Section */}
        <Hero />

        {/* Experiences Section */}
        <Experiences />
        
        {/* Featured Destinations - Moved after Experiences */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-6 text-gray-900">
              <span className="relative">
                {t('home.featured.title', { defaultValue: 'Featured Destinations' })}
                <span className="absolute bottom-0 left-0 w-full h-2 bg-blue-100 -z-10 transform translate-y-1"></span>
              </span>
            </h2>
            <p className="text-center text-gray-500 mb-12 max-w-2xl mx-auto">
              {t('home.featured.subtitle', { 
                defaultValue: "Explore our most popular sailing destinations around Auckland's stunning coastline and islands." 
              })}
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {['caribbean', 'mediterranean', 'southeastAsia'].map((destinationKey) => {
                const destinationName = t(`home.featured.destinations.${destinationKey}`);
                return (
                  <div key={destinationKey} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-transform hover:-translate-y-1">
                    <div className="h-48 bg-blue-200 flex items-center justify-center">
                      <span className="text-lg font-medium text-gray-600">
                        {t('common.imagePlaceholder', { destination: destinationName })}
                      </span>
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-2 text-gray-900">
                        {t('home.featured.destinations.sailingIn', { 
                          defaultValue: '{{destination}} Sailing',
                          destination: destinationName 
                        })}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {t('home.featured.destinations.description', {
                          destination: destinationName
                        })}
                      </p>
                      <Button variant="outline" className="w-full hover:bg-blue-50 hover:border-blue-200">
                        {t('home.featured.destinations.viewDetails')}
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </section>
      </main>

      <footer className="bg-gray-900 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <p>
            {t('common.copyright', { 
              year: new Date().getFullYear() 
            })}
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;
