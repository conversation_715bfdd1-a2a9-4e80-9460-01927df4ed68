export type BookingStatus = 'confirmed' | 'pending' | 'cancelled' | 'completed';

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
}

export interface BookingExperience {
  id: string;
  name: string;
  duration: number; // in hours
  basePrice: number;
}

export interface Booking {
  id: string;
  customer: Customer;
  experience: string;
  date: string; // ISO date string
  time: string;
  guests: number;
  status: BookingStatus;
  amount: number;
  notes?: string;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

export interface BookingFilters {
  status?: BookingStatus | 'all';
  dateFrom?: string;
  dateTo?: string;
  searchQuery?: string;
  sortBy?: 'date' | 'createdAt' | 'amount';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  pageSize?: number;
}

export interface BookingsResponse {
  data: Booking[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
