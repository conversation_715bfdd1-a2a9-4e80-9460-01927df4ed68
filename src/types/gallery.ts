/**
 * Gallery image type definition
 */
export interface GalleryImage {
  id: string;
  src: string;
  srcSet: {
    [key: string]: string; // key is the width (e.g., '400w', '800w')
  };
  alt: string;
  width: number;
  height: number;
  aspectRatio: number;
  caption?: string;
  categories: string[];
  tripTypes: string[];
  featured: boolean;
  createdAt: string;
  updatedAt: string;
  metadata?: {
    camera?: string;
    lens?: string;
    focalLength?: string;
    aperture?: string;
    shutterSpeed?: string;
    iso?: number;
    location?: {
      name?: string;
      coordinates?: [number, number];
    };
  };
}

/**
 * Gallery filter options
 */
export interface GalleryFilters {
  category?: string;
  tripType?: string;
  searchQuery?: string;
  sortBy?: 'newest' | 'oldest' | 'featured' | 'random';
}

/**
 * Gallery category type
 */
export interface GalleryCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  imageCount: number;
  coverImage?: string;
}

/**
 * Trip type for gallery filtering
 */
export interface TripType {
  id: string;
  name: string;
  slug: string;
  description?: string;
  imageCount: number;
  icon?: string;
}

/**
 * Lightbox image type (extends GalleryImage with additional lightbox-specific properties)
 */
export interface LightboxImage extends GalleryImage {
  index: number;
  nextId: string | null;
  prevId: string | null;
}

/**
 * Gallery state for the lightbox
 */
export interface GalleryState {
  images: GalleryImage[];
  filteredImages: GalleryImage[];
  categories: GalleryCategory[];
  tripTypes: TripType[];
  selectedImage: LightboxImage | null;
  isOpen: boolean;
  currentIndex: number;
  filters: GalleryFilters;
  isLoading: boolean;
  error: string | null;
}

/**
 * Gallery API response type
 */
export interface GalleryApiResponse {
  images: GalleryImage[];
  categories: GalleryCategory[];
  tripTypes: TripType[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Image upload response
 */
export interface ImageUploadResponse {
  id: string;
  url: string;
  width: number;
  height: number;
  format: string;
  size: number;
  metadata: Record<string, any>;
}
