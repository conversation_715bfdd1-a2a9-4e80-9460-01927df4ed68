export interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  avatar?: string;
  joinDate: string; // ISO date string
  lastActive?: string; // ISO date string
  totalBookings: number;
  totalSpent: number;
  notes?: string;
  tags?: string[];
  address?: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  preferences?: {
    communication?: {
      email: boolean;
      sms: boolean;
    };
    dietaryRestrictions?: string[];
    accessibilityNeeds?: string[];
  };
  metadata?: Record<string, any>;
}

export interface CustomerFilters {
  searchQuery?: string;
  tags?: string[];
  sortBy?: 'name' | 'joinDate' | 'totalBookings' | 'totalSpent';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  pageSize?: number;
}

export interface CustomersResponse {
  data: Customer[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
