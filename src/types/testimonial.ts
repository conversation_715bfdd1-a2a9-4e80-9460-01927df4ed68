export interface TestimonialAuthor {
  id: string;
  name: string;
  avatar?: string;
  location?: string;
  role?: string;
}

export interface Testimonial {
  id: string;
  content: string;
  rating: number; // 1-5 stars
  author: TestimonialAuthor;
  status: 'pending' | 'approved' | 'rejected';
  featured: boolean;
  createdAt: string;
  updatedAt: string;
  testimonialDate?: string; // When the testimonial was given
  source?: string; // e.g., 'Google', 'TripAdvisor', 'Website', etc.
  response?: {
    content: string;
    date: string;
    author: string;
  };
}

export interface TestimonialFormData {
  content: string;
  rating: number;
  authorName: string;
  authorRole?: string;
  authorLocation?: string;
  featured: boolean;
  status: 'pending' | 'approved' | 'rejected';
  source?: string;
  testimonialDate?: string;
  response?: {
    content: string;
    author: string;
  };
}

export interface TestimonialFilters {
  status?: 'pending' | 'approved' | 'rejected' | 'all';
  rating?: number;
  featured?: boolean;
  search?: string;
  sortBy?: 'newest' | 'oldest' | 'highest' | 'lowest';
}
