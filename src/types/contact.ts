/**
 * Contact form data types
 */

export interface ContactFormData {
  /** User's full name */
  name: string;
  
  /** User's email address */
  email: string;
  
  /** User's phone number (optional) */
  phone?: string;
  
  /** Email subject */
  subject: string;
  
  /** Message content */
  message: string;
  
  /** Whether the user has accepted the privacy policy */
  privacyPolicy: boolean;
  
  /** reCAPTCHA token for spam prevention */
  recaptchaToken?: string;
  
  /** Additional metadata (not part of the form but can be added programmatically) */
  metadata?: {
    /** Page URL where the form was submitted from */
    pageUrl?: string;
    
    /** Timestamp of form submission */
    timestamp?: string;
    
    /** User agent string */
    userAgent?: string;
    
    /** IP address (should only be collected server-side) */
    ipAddress?: string;
    
    /** Any other custom fields */
    [key: string]: any;
  };
}

/** Response from the contact form submission API */
export interface ContactFormResponse {
  /** Whether the submission was successful */
  success: boolean;
  
  /** Message to display to the user */
  message: string;
  
  /** Validation errors, if any */
  errors?: Record<string, string>;
  
  /** Additional data that might be useful */
  data?: {
    /** Submission ID for reference */
    submissionId?: string;
    
    /** Timestamp of submission */
    submittedAt?: string;
    
    /** Any other relevant data */
    [key: string]: any;
  };
}

/** Email template props for contact form notifications */
export interface ContactFormEmailProps {
  /** Form data that was submitted */
  formData: ContactFormData;
  
  /** Whether this is an admin notification (true) or user confirmation (false) */
  isAdmin?: boolean;
  
  /** Additional metadata */
  metadata?: {
    /** Submission date in a formatted string */
    submissionDate?: string;
    
    /** Any other relevant data */
    [key: string]: any;
  };
}

/** Configuration for the contact form component */
export interface ContactFormConfig {
  /** reCAPTCHA site key */
  recaptchaSiteKey: string;
  
  /** Whether to enable reCAPTCHA (default: true) */
  enableRecaptcha?: boolean;
  
  /** Custom submit handler (defaults to using the API) */
  onSubmit?: (data: Omit<ContactFormData, 'recaptchaToken'>) => Promise<boolean>;
  
  /** Callback when form is successfully submitted */
  onSuccess?: (response: ContactFormResponse) => void;
  
  /** Callback when form submission fails */
  onError?: (error: Error) => void;
  
  /** Custom validation function */
  validate?: (data: ContactFormData) => Record<string, string> | null;
  
  /** Custom labels and messages */
  labels?: {
    /** Form title */
    title?: string;
    
    /** Field labels */
    fields?: {
      name?: string;
      email?: string;
      phone?: string;
      subject?: string;
      message?: string;
      privacyPolicy?: string;
      submit?: string;
      [key: string]: string | undefined;
    };
    
    /** Success message */
    success?: string;
    
    /** Error messages */
    errors?: {
      required?: string;
      email?: string;
      minLength?: string;
      privacyPolicy?: string;
      recaptcha?: string;
      [key: string]: string | undefined;
    };
    
    /** Privacy policy text (can include HTML) */
    privacyPolicyText?: string | React.ReactNode;
  };
  
  /** Custom CSS classes */
  classes?: {
    form?: string;
    field?: string;
    input?: string;
    textarea?: string;
    button?: string;
    error?: string;
    success?: string;
    [key: string]: string | undefined;
  };
}
