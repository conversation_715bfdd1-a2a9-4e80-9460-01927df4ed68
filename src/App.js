import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Navigation } from './components/navigation';
import { Hero } from './components/sections/hero';
import { Experiences } from './components/sections/experiences';
import './App.css';
function App() {
    const { t } = useTranslation();
    const [activeTab, setActiveTab] = useState('home');
    return (_jsxs("div", { className: "min-h-screen bg-gradient-to-b from-blue-50 to-white", children: [_jsx(Navigation, { activeTab: activeTab, onTabChange: setActiveTab }), _jsxs("main", { children: [_jsx(Hero, {}), _jsx(Experiences, {}), _jsx("section", { className: "py-16 bg-gray-50", children: _jsxs("div", { className: "container mx-auto px-4", children: [_jsx("h2", { className: "text-3xl md:text-4xl font-bold text-center mb-6 text-gray-900", children: _jsxs("span", { className: "relative", children: [t('home.featured.title', { defaultValue: 'Featured Destinations' }), _jsx("span", { className: "absolute bottom-0 left-0 w-full h-2 bg-blue-100 -z-10 transform translate-y-1" })] }) }), _jsx("p", { className: "text-center text-gray-500 mb-12 max-w-2xl mx-auto", children: t('home.featured.subtitle', {
                                        defaultValue: "Explore our most popular sailing destinations around Auckland's stunning coastline and islands."
                                    }) }), _jsx("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8", children: ['caribbean', 'mediterranean', 'southeastAsia'].map((destinationKey) => {
                                        const destinationName = t(`home.featured.destinations.${destinationKey}`);
                                        return (_jsxs("div", { className: "bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-transform hover:-translate-y-1", children: [_jsx("div", { className: "h-48 bg-blue-200 flex items-center justify-center", children: _jsx("span", { className: "text-lg font-medium text-gray-600", children: t('common.imagePlaceholder', { destination: destinationName }) }) }), _jsxs("div", { className: "p-6", children: [_jsx("h3", { className: "text-xl font-semibold mb-2 text-gray-900", children: t('home.featured.destinations.sailingIn', {
                                                                defaultValue: '{{destination}} Sailing',
                                                                destination: destinationName
                                                            }) }), _jsx("p", { className: "text-gray-600 mb-4", children: t('home.featured.destinations.description', {
                                                                destination: destinationName
                                                            }) }), _jsx(Button, { variant: "outline", className: "w-full hover:bg-blue-50 hover:border-blue-200", children: t('home.featured.destinations.viewDetails') })] })] }, destinationKey));
                                    }) })] }) })] }), _jsx("footer", { className: "bg-gray-900 text-white py-8", children: _jsx("div", { className: "container mx-auto px-4 text-center", children: _jsx("p", { children: t('common.copyright', {
                            year: new Date().getFullYear()
                        }) }) }) })] }));
}
export default App;
