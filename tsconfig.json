{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@pages/*": ["./src/pages/*"], "@hooks/*": ["./src/hooks/*"], "@lib/*": ["./src/lib/*"], "@services/*": ["./src/services/*"], "@styles/*": ["./src/styles/*"], "@types/*": ["./src/types/*"], "@i18n/*": ["./src/i18n/*"]}, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "esModuleInterop": true, "skipLibCheck": true}, "include": ["src"], "references": []}